package service;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/4 11:56
 */
public interface ThirdChannelJobTikTokService {
    void timedEvent();

    void pushProduct(String channel);

    void updateProduct();

    void cancelProduct();

    void deleteProduct();

    void pullOrder(String channel, String startDate, String endDate);
    void pullOrder(String channel, String startDate, String endDate,String voJson);

    void pushFulfillment(String channel);

    void updateStock();

    void pullProduct(String name, String o);
}
