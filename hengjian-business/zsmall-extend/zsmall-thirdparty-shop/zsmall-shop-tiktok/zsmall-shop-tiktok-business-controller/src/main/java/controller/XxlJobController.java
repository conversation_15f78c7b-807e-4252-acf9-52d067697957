//package controller;
//
//import java.io.FileNotFoundException;
//
//
//
//import com.alibaba.fastjson.JSONObject;
//
//import com.zsmall.common.domain.tiktok.domain.dto.order.TikTokOrder;
//import com.zsmall.common.domain.vo.XxlJobSearchVO;
//import com.zsmall.common.enums.common.ChannelTypeEnum;
//import com.zsmall.common.util.GenerateSqlMapperUtil;
//import com.zsmall.xxl.job.service.ThirdChannelJobService;
//import lombok.RequiredArgsConstructor;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//
//    import java.io.FileNotFoundException;
//
//import com.alibaba.fastjson.JSONObject;
//
//import com.zsmall.common.domain.tiktok.domain.dto.order.TikTokOrder;
//import com.zsmall.common.domain.vo.XxlJobSearchVO;
//import com.zsmall.common.enums.common.ChannelTypeEnum;
//import com.zsmall.common.util.GenerateSqlMapperUtil;
//import com.zsmall.xxl.job.service.ThirdChannelJobService;
//import lombok.RequiredArgsConstructor;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//
//    import java.io.FileNotFoundException;
//
//
///**
// * 定时任务流程测试
// *
// * <AUTHOR> Theo
// * @create 2024/2/2 12:01
// */
//@Validated
//@RestController
//@RequestMapping("/test/xxlJob")
//@RequiredArgsConstructor
//public class XxlJobController {
//
//    private final ThirdChannelJobService thirdChannelJobService;
//
//    @PostMapping("/pullOrder")
//    public void pullOrder(@RequestBody  XxlJobSearchVO vo) {
//        String jsonString = JSONObject.toJSONString(vo);
//        thirdChannelJobService.pullOrder(ChannelTypeEnum.TikTok.name(), null, null,jsonString);
//    }
//
//    @PostMapping("/pullProduct")
//    public void pullProduct(@RequestBody  XxlJobSearchVO vo) {
//        String jsonString = JSONObject.toJSONString(vo);
//        thirdChannelJobService.pullProduct(ChannelTypeEnum.TikTok.name(),jsonString);
//    }
//
//
//    @GetMapping("/getSql")
//    public String getSql( ) throws FileNotFoundException {
//
//        return GenerateSqlMapperUtil.generateCreateTableSql("tik_tok_order", TikTokOrder.class);
//    }
//
//
//}
