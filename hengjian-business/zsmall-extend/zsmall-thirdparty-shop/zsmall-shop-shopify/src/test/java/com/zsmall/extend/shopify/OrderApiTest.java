package com.zsmall.extend.shopify;

import cn.hutool.core.lang.Console;
import cn.hutool.json.JSONUtil;
import com.zsmall.extend.core.utils.ZonedDateUtil;
import com.zsmall.extend.shop.shopify.api.model.order.InOrderPage;
import com.zsmall.extend.shop.shopify.kit.ShopifyKit;
import com.zsmall.extend.shop.shopify.model.ShopifyPage;
import com.zsmall.extend.shop.shopify.model.order.OrderList;
import org.junit.jupiter.api.Test;

import java.time.ZoneId;

public class OrderApiTest extends BaseTest {

    @Test
    public void testQuery() {
        InOrderPage orderPage = new InOrderPage();
//        orderPage.setLimit(10);

        String start = ZonedDateUtil.local2ZoneDateTime("2023-05-10 00:00:00", ZoneId.of("+12")).toString();
        String end = ZonedDateUtil.local2ZoneDateTime("2023-06-05 23:59:59", ZoneId.of("+12")).toString();
        System.out.println("start === " + start);
        System.out.println("end === " + end);
        orderPage.setUpdatedAtMin(start);
        orderPage.setUpdatedAtMax(end);

        ShopifyPage<OrderList> orders = ShopifyKit.create(shopifyClientBean).orderApi("champagne-test-05.myshopify.com")
            .getOrders(orderPage);

        Console.log(JSONUtil.toJsonStr(orders));

    }

}
