package com.zsmall.extend.shopify;

import com.zsmall.extend.shop.shopify.kit.AccessTokenKit;
import com.zsmall.extend.shop.shopify.model.ShopifyClientBean;
import com.zsmall.extend.shop.shopify.model.accesstoken.AccessToken;
import org.junit.jupiter.api.BeforeEach;

public class BaseTest {

    protected ShopifyClientBean shopifyClientBean;

    @BeforeEach
    public void testInit() {
        shopifyClientBean = new ShopifyClientBean(
            "df001b9319030bdc1fe893059136a6cd",
            "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",
            "2023-01"
        );

        AccessToken accessToken = new AccessToken();
        accessToken.setAccessToken("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");
        AccessTokenKit.put("champagne-test-05.myshopify.com", accessToken);
    }


}
