package com.zsmall.extend.shop.shopify.model.inventory;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;

/**
 * 库存水平
 *
 * @date 2022-12-25 11:13
 */
@Data
@Accessors(chain = true)
public class InventoryLevel {

    /**
     * 可供出售的库存物品数量。null如果未跟踪库存项目，则返回。
     * The quantity of inventory items available for sale. Returns null if the inventory item is not tracked.
     */
    private Integer available;

    /**
     * 库存级别所属的库存项目的ID。
     * The ID of the inventory item that the inventory level belongs to.
     */
    @Alias(JsonConstants.INVENTORY_ITEM_ID)
    private Long inventoryItemId;

    /**
     * 库存级别所属位置的ID。要查找位置的ID，请使用位置资源。
     * The ID of the location that the inventory level belongs to. To find the ID of the location, use the Location
     * resource.
     */
    @Alias(JsonConstants.LOCATION_ID)
    private Long locationId;

    /**
     * 上次修改库存水平 的日期和时间（ISO 8601格式）。
     * The date and time (ISO 8601 format) when the collect was last updated.
     */
    @Alias(JsonConstants.UPDATED_AT)
    private ZonedDateTime updatedAt;

}
