package com.zsmall.extend.shop.shopify.model.order;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import com.zsmall.extend.shop.shopify.enums.FinancialStatus;
import com.zsmall.extend.shop.shopify.enums.LineItemStatus;
import com.zsmall.extend.shop.shopify.enums.OrderCancelReason;
import com.zsmall.extend.shop.shopify.enums.ProcessingMethod;
import com.zsmall.extend.shop.shopify.model.Attribute;
import com.zsmall.extend.shop.shopify.model.customer.Customer;
import com.zsmall.extend.shop.shopify.model.fulfillment.Fulfillment;
import com.zsmall.extend.shop.shopify.model.fulfillment.LineItem;
import com.zsmall.extend.shop.shopify.model.order.refund.Refund;
import com.zsmall.extend.shop.shopify.model.shop.PriceSet;
import com.zsmall.extend.shop.shopify.model.shop.TaxLine;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * 订单
 *
 * @date 2022-12-27 23:48
 */
@Data
@Accessors(chain = true)
public class Order {

    /**
     * 创建订单的应用的ID。
     * The ID of the app that created the order.
     */
    @Alias(JsonConstants.APP_ID)
    private Long app_id;

    /**
     * 与付款方式关联的邮寄地址。该地址是一个可选字段，在不需要付款方式的订单上将不可用。
     * <p>
     * The mailing address associated with the payment method. This address is an optional field that won't be
     * available on orders that do not require a payment method
     */
    @Alias(JsonConstants.BILLING_ADDRESS)
    private BillingAddress billingAddress;

    /**
     * The IP address of the browser used by the customer when they placed the order.
     */
    @Alias(JsonConstants.BROWSER_IP)
    private String browserIp;

    /**
     * Whether the customer consented to receive email updates from the shop.
     */
    @Alias(JsonConstants.BUYER_ACCEPTS_MARKETING)
    private boolean buyerAcceptsMarketing;

    /**
     * The reason why the order was canceled. Valid values:
     * <p>
     * customer: The customer canceled the order.
     * fraud: The order was fraudulent.
     * inventory: Items in the order were not in inventory.
     * declined: The payment was declined.
     * other: A reason not in this list.
     */
    @Alias(JsonConstants.CANCEL_REASON)
    private OrderCancelReason cancelReason;

    /**
     * The date and time ( ISO 8601 format) when the order was canceled.
     */
    @Alias(JsonConstants.CANCELLED_AT)
    private ZonedDateTime cancelledAt;

    /**
     * The ID of the cart that's associated with the order.
     */
    @Alias(JsonConstants.CART_TOKEN)
    private String cartToken;

    /**
     * The ID of the checkout that's associated with the order.
     */
    @Alias(JsonConstants.CHECKOUT_TOKEN)
    private String checkoutToken;

    /**
     * Information about the browser that the customer used when they placed their order:
     */
    @Alias(JsonConstants.CLIENT_DETAILS)
    private ClientDetails clientDetails;

    /**
     * 订单关闭的日期和时间（ISO 8601格式）。
     * The date and time (ISO 8601 format) when the order was closed.
     */
    @Alias(JsonConstants.CLOSED_AT)
    private ZonedDateTime closedAt;

    /**
     * 在Shopify中创建订单时的自动生成的日期和时间（ISO 8601格式）。此属性的值无法更改。
     * The autogenerated date and time (ISO 8601 format) when the order was created in Shopify. The value for this
     * property cannot be changed.
     */
    @Alias(JsonConstants.CREATED_AT)
    private ZonedDateTime created_at;

    /**
     * 商店货币的三个字母的代码（ISO 4217格式）。
     * The three-letter code (ISO 4217 format) for the shop currency.
     */
    private String currency;

    /**
     * 当前以商店货币表示的订单总折扣。该字段的值反映了订单编辑，退货和退款。
     * The current total discounts on the order in the shop currency. The value of this field reflects order edits,
     * returns, and refunds.
     */
    @Alias(JsonConstants.CURRENT_TOTAL_DISCOUNTS)
    private String currentTotalDiscounts;

    /**
     * 当前订单上商店和展示货币的总折扣。amount与该字段关联的值反映了订单编辑，退货和退款。
     * The current total discounts on the order in shop and presentment currencies. The amount values associated with
     * this field reflect order edits, returns, and refunds.
     */
    @Alias(JsonConstants.CURRENT_TOTAL_DISCOUNTS_SET)
    private PriceSet currentTotalDiscountsSet;


    /**
     * 当前以商店和演示货币对订单收取的总关税。amount与该字段关联的值反映了订单编辑，退货和退款。
     * The current total duties charged on the order in shop and presentment currencies. The amount values associated with this field reflect order edits, returns, and refunds.
     */
    @Alias(JsonConstants.CURRENT_TOTAL_DUTIES_SET)
    private PriceSet currentTotalDutiesSet;

    /**
     * 订单当前的总价（以商店货币计）。该字段的值反映了订单编辑，退货和退款。
     * The current total price of the order in the shop currency. The value of this field reflects order edits, returns,
     * and refunds.
     */
    @Alias(JsonConstants.CURRENT_TOTAL_PRICE)
    private String currentTotalPrice;

    /**
     * 订单当前的总价，以商店和演示货币表示。amount与该字段关联的值反映了订单编辑，退货和退款。
     * TThe current total price of the order in shop and presentment currencies. The amount values associated with this
     * field reflect order edits, returns, and refunds.
     */
    @Alias(JsonConstants.CURRENT_TOTAL_PRICE_SET)
    private PriceSet currentTotalPriceSet;

    /**
     * 以商店货币表示的订单的当前小计价格。该字段的值反映了订单编辑，退货和退款。
     * The current subtotal price of the order in the shop currency. The value of this field reflects order edits,
     * returns, and refunds.
     */
    @Alias(JsonConstants.CURRENT_SUBTOTAL_PRICE)
    private String currentSubtotalPrice;

    /**
     * 商店和展示货币中订单的当前小计价格。amount与该字段关联的值反映了订单编辑，退货和退款。
     * The current subtotal price of the order in shop and presentment currencies. The amount values associated with this field reflect order edits, returns, and refunds.
     */
    @Alias(JsonConstants.CURRENT_SUBTOTAL_PRICE_SET)
    private PriceSet currentSubtotalPriceSet;

    /**
     * 当前以商店货币对订单收取的总税额。该字段的值反映了订单编辑，退货或退款。
     * The current total taxes charged on the order in the shop currency. The value of this field reflects order edits,
     * returns, or refunds.
     */
    @Alias(JsonConstants.CURRENT_TOTAL_TAX)
    private String currentTotalTax;

    /**
     * 当前以商店和演示货币对订单收取的总税款。amount与该字段关联的值反映了订单编辑，退货和退款。
     * The current total taxes charged on the order in shop and presentment currencies. The amount values associated
     * with this field reflect order edits, returns, and refunds.
     */
    @Alias(JsonConstants.CURRENT_TOTAL_TAX_SET)
    private PriceSet currentTotalTaxSet;

    /**
     * 有关客户的信息。该订单可能没有客户，并且应用程序不应依赖于customer对象的存在。null如果订单是通过Shopify POS创建的，则此值为。有关该customer对象的更多信息，请参见客户资源。
     * Information about the customer. The order might not have a customer and apps should not depend on the existence
     * of a customer object. This value might be null if the order was created through Shopify POS. For more
     * information about the customer object, see the Customer resource.
     */
    private Customer customer;

    /**
     * 两个或三个字母的语言代码，可以选择后面跟一个区域修饰符。
     * The two or three-letter language code, optionally followed by a region modifier.
     */
    @Alias(JsonConstants.CUSTOMER_LOCALE)
    private String customerLocale;

    /**
     * 堆叠折扣应用程序的有序列表。
     * An ordered list of stacked discount applications.
     */
    @Alias(JsonConstants.DISCOUNT_APPLICATIONS)
    private List<DiscountApplication> discountApplications;

    /**
     * The customer's email address.
     * 客户的电子邮件地址
     */
    private String email;

    /**
     * 与订单相关的付款状态。只能在创建订单时设置。
     * The status of payments associated with the order
     */
    @Alias(JsonConstants.FINANCIAL_STATUS)
    private FinancialStatus financialStatus;

    /**
     * A list of fulfillments associated with the order. For more information, see the Fulfillment API.
     * 与订单相关的履行清单。有关更多信息，请参见Fulfillment API。
     */
    private List<Fulfillment> fulfillments;

    /**
     * 根据已完成订单项的订单状态
     * The order's status in terms of fulfilled line items.
     */
    @Alias(JsonConstants.FULFILLMENT_STATUS)
    private LineItemStatus fulfillmentStatus;

    /**
     * The payment gateway used.
     * 使用的支付网关。
     */
    @Deprecated
    private String gateway;

    /**
     * The ID of the order, used for API purposes. This is different from the order_number property, which is the ID
     * used by the shop owner and customer.'
     * 订单的ID，用于API。这与order_number属性不同，属性是商店所有者和客户使用的ID。”
     */
    private Long id;

    /**
     * The URL for the page where the buyer landed when they entered the shop.
     * 买方进入商店时到达的页面页面的URL
     */
    @Alias(JsonConstants.LANDING_SITE)
    private String landingSite;

    /**
     * 订单项对象的列表，每个对象都包含有关订单中项目的信息。
     * A list of line item objects, each containing information about an item in the order.
     */
    @Alias(JsonConstants.LINE_ITEMS)
    private List<LineItem> lineItems;

    /**
     * The ID of the physical location where the order was processed. This property refers to the POS location.
     * location_id will always be set to null for online orders. If you need to reference the location against an
     * order, then use the FulfillmentOrder resource.
     * 处理订单的实际位置的ID。此属性是指POS位置。location_id将始终设置null为在线订单。如果需要根据订单引用位置，请使用FulfillmentOrder资源。
     */
    @Alias(JsonConstants.LOCATION_ID)
    private Long locationId;

    /**
     * 订单名称，是通过将该order_number属性与商家常规设置中设置的订单前缀和后缀组合而生成的。这与id属性不同，该属性是API使用的订单的ID。API也可以将此字段设置为任何字符串值。
     * The order name, generated by combining the order_number property with the order prefix and suffix that are set
     * in the merchant's general settings. This is different from the id property, which is the ID of the order used by
     * the API. This field can also be set by the API to be any string value.
     */
    private String name;

    /**
     * An optional note that a shop owner can attach to the order.
     * 店主可以附加到订单的可选注释
     */
    private String note;

    /**
     * Extra information that is added to the order. Appears in the Additional details section of an order details page
     * . Each array entry must contain a hash with name and value keys.
     * 添加到订单的其他信息。出现在订单详细信息页面的“其他详细信息”部分。每个数组条目必须包含带有name和value键的哈希。
     */
    @Alias(JsonConstants.NOTE_ATTRIBUTES)
    private List<Attribute> noteAttributes;

    /**
     * The order's position in the shop's count of orders. Numbers are sequential and start at 1.
     * 订单在商店订单计数中的位置。数字是连续的，从1开始。
     */
    private Integer number;

    /**
     * The order 's position in the shop's count of orders starting at 1001. Order numbers are sequential and start at
     * 1001.
     * 商店中订单的数量（从1001开始）。订单编号是连续的，从1001开始。
     */
    @Alias(JsonConstants.ORDER_NUMBER)
    private Long orderNumber;

    /**
     * The original total duties charged on the order in shop and presentment currencies.
     * 按商店和演示货币对订单收取的原始总关税
     */
    @Alias(JsonConstants.ORIGINAL_TOTAL_DUTIES_SET)
    private PriceSet originalTotalDutiesSet;

    /**
     * An object containing information about the payment.
     * 包含有关付款信息的对象
     */
    @Deprecated
    private Object payment_details;

    /**
     * The list of payment gateways used for the order.
     * 用于订单的付款网关列表
     */
    @Alias(JsonConstants.PAYMENT_GATEWAY_NAMES)
    private List<String> paymentGatewayNames;

    /**
     * The customer's phone number for receiving SMS notifications.
     * 用于接收SMS通知的客户的电话号码
     */
    private String phone;

    /**
     * The presentment currency that was used to display prices to the customer.
     * 用于向客户显示价格的演示货币
     */
    @Alias(JsonConstants.PRESENTMENT_CURRENCY)
    private String presentmentCurrency;

    /**
     * The date and time (ISO 8601 format) when an order was processed. This value is the date that appears on your
     * orders and that's used in the analytic reports. By default, it matches the created_at value. If you're importing
     * orders from an app or another platform, then you can set processed_at to a date and time in the past to match
     * when the original order was created.
     * 处理订单 的日期和时间（ISO 8601格式）。此值是您的订单上显示的日期，该日期已在分析报告中使用。默认情况下，它与created_at值匹配。如果要从应用程序或其他平台导入订单，则可以设置processed_at
     * 过去的日期和时间以匹配原始订单的创建时间。
     */
    @Alias(JsonConstants.PROCESSED_AT)
    private ZonedDateTime processedAt;

    /**
     * How the payment was processed
     * 付款处理方式。
     */
    @Alias(JsonConstants.PROCESSING_METHOD)
    private ProcessingMethod processingMethod;

    /**
     * The website where the customer clicked a link to the shop.
     * 客户单击商店链接的网站
     */
    @Alias(JsonConstants.REFERRING_SITE)
    private String referringSite;

    /**
     * 应用于订单的退款清单。有关更多信息，请参阅Refund API。
     * A list of refunds applied to the order. For more information, see the Refund API.
     */
    private List<Refund> refunds;

    /**
     * 将订单运送到的邮寄地址。该地址是可选的，在不需要运输的订单上将不可用。它具有以下属性：
     * The mailing address to where the order will be shipped
     */
    @Alias(JsonConstants.SHIPPING_ADDRESS)
    private BillingAddress shippingAddress;

    /**
     * An array of objects, each of which details a shipping method used.
     * 对象数组，每个对象详细说明所使用的运输方法。
     */
    @Alias(JsonConstants.SHIPPING_LINES)
    private List<ShippingLine> shippingLines;

    /**
     * Where the order originated. Can be set only during order creation, and is not writeable afterwards. Values for
     * Shopify channels are protected and cannot be assigned by other API clients: web, pos, shopify_draft_order,
     * iphone, and android. Orders created via the API can be assigned any other string of your choice. If unspecified,
     * then new orders are assigned the value of your app's ID.
     * 订单起源的地方。只能在创建订单时设置，以后不能写。对Shopify渠道的值是保护，不能被其他API客户端分配：web，pos，shopify_draft_order，iphone，和android。通过API
     * 创建的订单可以分配给您选择的任何其他字符串。如果未指定，则为新订单分配应用程序ID的值。
     */
    @Alias(JsonConstants.SOURCE_NAME)
    private String sourceName;

    /**
     * The price of the order in the shop currency after discounts but before shipping, taxes, and tips.
     * 折扣后但未扣除运费，税金和小费的订单价格（以商店货币计）。
     */
    @Alias(JsonConstants.SUBTOTAL_PRICE)
    private Double subtotalPrice;

    /**
     * The subtotal of the order in shop and presentment currencies.
     * 商店和展示货币的订单小计
     */
    @Alias(JsonConstants.SUBTOTAL_PRICE_SET)
    private PriceSet subtotalPriceSet;

    /**
     * Tags attached to the order, formatted as a string of comma-separated values. Tags are additional short
     * descriptors, commonly used for filtering and searching. Each individual tag is limited to 40 characters in length.
     */
    private String tags;

    /**
     * An array of tax line objects, each of which details a tax applicable to the order.
     * 税线对象数组，每个对象详细说明适用于该订单的税。
     */
    private List<TaxLine> tax_lines;

    /**
     * 订单小计中是否包含税款
     * Whether taxes are included in the order subtotal.
     */
    @Alias(JsonConstants.TAXES_INCLUDED)
    private boolean taxesIncluded;

    /**
     * Whether this is a test order.
     * 这是否是测试订单。
     */
    private boolean test;

    /**
     * A unique token for the order.
     * 订单的唯一令牌
     */
    private String token;

    /**
     * The total discounts applied to the price of the order in the shop currency.
     * 总折扣适用于以商店货币表示的订单价格
     */
    @Alias(JsonConstants.TOTAL_DISCOUNTS)
    private Double totalDiscounts;

    /**
     * 总折扣适用于以商店和演示货币表示的订单价格
     * The total discounts applied to the price of the order in shop and presentment currencies.
     */
    @Alias(JsonConstants.TOTAL_DISCOUNTS_SET)
    private PriceSet totalDiscountsSet;

    /**
     * The sum of all line item prices in the shop currency.
     * 以商店货币表示的所有订单项价格的总和
     */
    @Alias(JsonConstants.TOTAL_LINE_ITEMS_PRICE)
    private Double totalLineItemsPrice;

    /**
     * 商店和展示货币中所有订单项价格的总和
     * The total of all line item prices in shop and presentment currencies.
     */
    @Alias(JsonConstants.TOTAL_LINE_ITEMS_PRICE_SET)
    private PriceSet totalLineItemsPriceSet;

    /**
     * The total outstanding amount of the order in the shop currency.
     * 以商店货币表示的订单的未付总额
     */
    @Alias(JsonConstants.TOTAL_OUTSTANDING)
    private Double totalOutstanding;

    /**
     * The sum of all line item prices, discounts, shipping, taxes, and tips in the shop currency. Must be positive.
     * 所有订单项价格，折扣，运费，税费和商店货币小费的总和。必须是积极的。
     */
    @Alias(JsonConstants.TOTAL_PRICE)
    private Double totalPrice;

    /**
     * The total price of the order in shop and presentment currencies.
     * 订单的总价（以商店和演示货币表示）。
     */
    @Alias(JsonConstants.TOTAL_PRICE_SET)
    private PriceSet totalPriceSet;

    /**
     * The total shipping price of the order, excluding discounts and returns, in shop and presentment currencies. If
     * taxes_included is set to true, then total_shipping_price_set includes taxes.
     * 订单的总运输价格，不包括折扣和退货，以商店和演示货币表示。如果taxes_included设置为true，则total_shipping_price_set含税。
     */
    @Alias(JsonConstants.TOTAL_SHIPPING_PRICE_SET)
    private PriceSet totalShippingPriceSet;

    /**
     * 以商店货币计入订单的所有税金之和。必须为正数）。
     * The sum of all the taxes applied to the order in th shop currency. Must be positive).
     */
    @Alias(JsonConstants.TOTAL_TAX)
    private Double totalTax;

    /**
     * The total tax applied to the order in shop and presentment currencies.
     * 以商店和演示货币应用于订单的总税额
     */
    @Alias(JsonConstants.TOTAL_TAX_SET)
    private PriceSet totalTaxSet;

    /**
     * The sum of all the tips in the order in the shop currency.
     * 订单中所有小费的总和（以商店货币计）。
     */
    @Alias(JsonConstants.TOTAL_TIP_RECEIVED)
    private Double totalTipReceived;

    /**
     * The sum of all line item weights in grams.
     * 所有订单项权重的总和（以克为单位）。
     */
    @Alias(JsonConstants.TOTAL_WEIGHT)
    private Double totalWeight;

    /**
     * The ID of the user logged into Shopify POS who processed the order, if applicable.
     * 登录到Shopify POS并处理订单的用户的ID（如果有）。
     */
    @Alias(JsonConstants.USER_ID)
    private Long userId;

    /**
     * The URL pointing to the order status web page, if applicable.
     * 指向订单状态网页的URL （如果适用）。
     */
    @Alias(JsonConstants.ORDER_STATUS_URL)
    private String orderStatusUrl;

    /**
     * 上次修改订单的日期和时间（ISO 8601格式）。
     * The date and time (ISO 8601 format) when the order was last modified.
     */
    @Alias(JsonConstants.UPDATED_AT)
    private ZonedDateTime updatedAt;

}
