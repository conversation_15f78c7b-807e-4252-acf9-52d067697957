package com.zsmall.extend.shop.shopify.model.product;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;

import java.time.ZonedDateTime;

/**
 * 收藏夹
 *
 * @date 2022-12-23 12:25
 */
public class Collect {

    /**
     * The ID of the custom collection containing the product.
     */
    @Alias(JsonConstants.COLLECTION_ID)
    private Long collectionId;

    /**
     * The date and time (ISO 8601 format) when the collect was created.
     */
    @Alias(JsonConstants.CREATED_AT)
    private ZonedDateTime createAt;

    /**
     * A unique numeric identifier for the collect.
     */
    private Long id;

    /**
     * The position of this product in a manually sorted custom collection. The first position is 1. This value is applied only when the custom collection is sorted manually.
     */
    private Integer position;

    /**
     * The unique numeric identifier for the product in the custom collection.
     */
    @Alias(JsonConstants.PRODUCT_ID)
    private Long productId;

    /**
     * This is the same value as position but padded with leading zeroes to make it alphanumeric-sortable. This value is applied only when the custom collection is sorted manually.
     */
    @Alias(JsonConstants.SORT_VALUE)
    private String sortValue;

    /**
     * 自定义集合的最新修改日期和时间（ISO 8601格式）。
     * The date and time (ISO 8601 format) when the collect was last updated.
     */
    @Alias(JsonConstants.UPDATED_AT)
    private ZonedDateTime updatedAt;

}
