package com.zsmall.extend.shop.shopify.model.fulfillment;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @date 2022-12-25 17:59
 */
@Data
@Accessors(chain = true)
public class FulfillmentService {

    private Long id;

    /**
     * 说明Shopify检索清单和跟踪更新所需的URL端点。如果履行服务已选择使用履行订单，则此URL端点也将用于发送履行和取消请求。
     * 如果inventory_management，tracking_support或fulfillment_orders_opt_in设置为“ true” ，则此字段是必需的。
     * States the URL endpoint that Shopify needs to retrieve inventory and tracking updates. If the Fulfillment
     * Service has opted in to use fulfillment orders, then this URL endpoint is also used to send fulfillment and
     * cancellation requests. This field is necessary if inventory_management, tracking_support, or
     * fulfillment_orders_opt_in is set to "true".
     */
    @Alias(JsonConstants.CALLBACK_URL)
    private String callbackUrl;

    /**
     * 指定API输出的格式。有效值为json和xml。
     * Specifies the format of the API output. Valid values are json and xml.
     */
    private String format;

    /**
     * 履行服务是否要注册与履行订单相关的API。要使用履行订单管理履行，请参阅使用Fulfillment和FulfillmentOrder资源管理履行。
     * Whether the fulfillment service wants to register for APIs related to fulfillment orders. To manage fulfillments
     * using fulfillment orders, see Manage fulfillments with Fulfillment and FulfillmentOrder resources.
     */
    @Alias(JsonConstants.FULFILLMENT_ORDERS_OPT_IN)
    private boolean fulfillmentOrdersOptIn;

    /**
     * 从其标题生成的用于实现服务的人性化的唯一字符串。
     * A human-friendly unique string for the fulfillment service generated from its title.
     */
    private String handle;


    /**
     * 说明履行服务是否跟踪产品库存并向Shopify提供更新。有效值为“ true”和“ false”。
     * States if the fulfillment service tracks product inventory and provides updates to Shopify. Valid values are
     * "true" and "false".
     */
    @Alias(JsonConstants.INVENTORY_MANAGEMENT)
    private boolean inventoryManagement;


    /**
     * 与履行服务相关联的位置的唯一标识符
     * The unique identifier of the location tied to the fulfillment service
     */
    @Alias(JsonConstants.LOCATION_ID)
    private Long locationId;

    /**
     * 商家及其客户看到的履行服务的名称。
     * The name of the fulfillment service as seen by merchants and their customers.
     */
    private String name;

    /**
     * 履行服务提供商的唯一标识符。
     * A unique identifier for the fulfillment service provider.
     */
    @Alias(JsonConstants.PROVIDER_ID)
    private Long providerId;

    /**
     * 说明履行服务是否要求实际运送产品。有效值为“ true”和“ false”。
     * States if the fulfillment service requires products to be physically shipped. Valid values are "true" and "false".
     */
    @Alias(JsonConstants.REQUIRES_SHIPPING_METHOD)
    private boolean requiresShippingMethod;

    /**
     * 说明履行服务是否提供包裹的跟踪号。有效值为“ true”和“ false”。
     * States if the fulfillment service provides tracking numbers for packages. Valid values are "true" and "false".
     */
    @Alias(JsonConstants.TRACKING_SUPPORT)
    private boolean trackingSupport;

}
