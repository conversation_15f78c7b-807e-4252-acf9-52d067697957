package com.zsmall.extend.shop.shopify.constant;

public final class JsonConstants {
    public static final String ID = "id";

    // Count
    public static final String COUNT = "count";

    // Customer
    public static final String CUSTOMERS = "customers";

    public static final String EMAIL = "email";
    public static final String FIRST_NAME = "first_name";
    public static final String LAST_NAME = "last_name";
    public static final String CREATED_AT = "created_at";
    public static final String PROCESSED_AT = "processed_at";
    public static final String ACCEPTS_MARKETING = "accepts_marketing";
    public static final String NOTE = "note";
    public static final String TAGS = "tags";
    public static final String ORDERS_COUNT = "total_spent";
    public static final String TOTAL_SPENT = "total_spent";
    public static final String LAST_ORDER_ID = "last_order_id";
    public static final String VERIFIED_EMAIL = "verified_email";
    public static final String MULTIPASS_IDENTIFIER = "multipass_identifier";
    public static final String TAX_EXEMPT = "tax_exempt";
    public static final String TAX_EXEMPTIONS = "tax_exemptions";
    public static final String LAST_ORDER_NAME = "last_order_name";

    // Address
    public static final String ADDRESSES = "addresses";

    public static final String COMPANY = "company";
    public static final String COUNTRY = "country";
    public static final String CITY = "city";
    public static final String PROVINCE = "province";
    public static final String PHONE = "phone";
    public static final String ZIP = "zip";
    public static final String ADDRESS1 = "address1";
    public static final String ADDRESS2 = "address2";

    // Collections
    public static final String CUSTOM_COLLECTIONS = "custom_collections";
    public static final String SMART_COLLECTIONS = "smart_collections";

    public static final String TITLE = "title";

    // Product
    public static final String PRODUCTS = "products";

    public static final String PRODUCT_EXISTS = "product_exists";
    public static final String VARIANT_ID = "variant_id";
    public static final String HANDLE = "handle";
    public static final String IMAGE = "image";
    public static final String IMAGES = "images";
    public static final String POSITION = "position";
    public static final String PRICE = "price";
    public static final String COMPARE_AT_PRICE = "compare_at_price";
    public static final String PUBLISHED_AT = "published_at";
    public static final String PRODUCT_VARIANTS = "variants";
    public static final String PRODUCT_VARIANT_OPTIONS = "options";
    public static final String PRODUCT_TYPE = "product_type";

    public static final String OPTION_1 = "option1";
    public static final String OPTION_2 = "option2";
    public static final String OPTION_3 = "option3";
    public static final String IMAGE_ID = "image_id";
    public static final String INVENTORY_ITEM_ID = "inventory_item_id";
    public static final String INVENTORY_MANAGEMENT = "inventory_management";
    public static final String INVENTORY_POLICY = "inventory_policy";
    public static final String INVENTORY_QUANTITY = "inventory_quantity";
    public static final String OLD_INVENTORY_QUANTITY = "old_inventory_quantity";
    public static final String INVENTORY_QUANTITY_ADJUSTMENT = "inventory_quantity_adjustment";
    public static final String PRESENTMENT_PRICES = "presentment_prices";
    public static final String TAX_CODE = "tax_code";
    public static final String CURRENCY_CODE = "currency_code";

    public static final String OPTION_NAME = "name";
    public static final String OPTION_POSITION = "position";
    public static final String OPTION_VALUES = "values";

    public static final String FULFILLMENT_SERVICE = "fulfillment_service";
    public static final String METAFIELDS_GLOBAL_TITLE_TAG = "metafields_global_title_tag";
    public static final String METAFIELDS_GLOBAL_DESCRIPTION_TAG = "metafields_global_description_tag";

    // Collect
    public static final String COLLECTS = "collects";
    public static final String SORT_VALUE = "sort_value";

    public static final String BODY_HTML = "body_html";
    public static final String PUBLISHED = "published";
    public static final String PUBLISHED_SCOPE = "published_scope";
    public static final String SORT_ORDER = "sort_order";
    public static final String TEMPLATE_SUFFIX = "template_suffix";
    public static final String METAFIELDS = "metafields";
    public static final String SINCE_ID = "since_id";
    public static final String PUBLISHED_STATUS = "published_status";

    public static final String PRODUCT_ID = "product_id";
    public static final String COLLECTION_ID = "collection_id";

    // Order
    public static final String ORDERS = "orders";

    public static final String TOTAL_PRICE = "total_price";
    public static final String DISCOUNT = "total_discounts";
    public static final String FINANCIAL_STATUS = "financial_status";
    public static final String CUSTOMER = "customer";
    public static final String LINE_ITEMS = "line_items";
    public static final String VARIANT_TITLE = "variant_title";
    public static final String NAME = "name";
    public static final String QUANTITY = "quantity";
    public static final String BILLING_ADDRESS = "billing_address";
    public static final String SHIPPING_ADDRESS = "shipping_address";
    public static final String APP_ID = "app_id";
    public static final String BUYER_ACCEPTS_MARKETING = "buyer_accepts_marketing";
    public static final String CANCEL_REASON = "cancel_reason";
    public static final String CANCELLED_AT = "cancelled_at";
    public static final String CART_TOKEN = "cart_token";
    public static final String CHECKOUT_TOKEN = "checkout_token";
    public static final String CLIENT_DETAILS = "client_details";
    public static final String CLOSED_AT = "closed_at";
    public static final String CURRENT_TOTAL_DISCOUNTS = "current_total_discounts";
    public static final String CURRENT_TOTAL_DISCOUNTS_SET = "current_total_discounts_set";
    public static final String CURRENT_TOTAL_DUTIES_SET = "current_total_duties_set";
    public static final String CURRENT_TOTAL_PRICE = "current_total_price";
    public static final String CURRENT_TOTAL_PRICE_SET = "current_total_price_set";
    public static final String CURRENT_SUBTOTAL_PRICE = "current_subtotal_price";
    public static final String CURRENT_SUBTOTAL_PRICE_SET = "current_subtotal_price_set";
    public static final String CURRENT_TOTAL_TAX = "current_total_tax";
    public static final String CURRENT_TOTAL_TAX_SET = "current_total_tax_set";
    public static final String CUSTOMER_LOCALE = "customer_locale";
    public static final String DISCOUNT_APPLICATIONS = "discount_applications";
    public static final String LANDING_SITE = "landing_site";
    public static final String NOTE_ATTRIBUTES = "note_attributes";
    public static final String ORDER_NUMBER = "order_number";
    public static final String ORIGINAL_TOTAL_DUTIES_SET = "original_total_duties_set";
    public static final String PAYMENT_GATEWAY_NAMES = "payment_gateway_names";
    public static final String PRESENTMENT_CURRENCY = "presentment_currency";
    public static final String PROCESSING_METHOD = "processing_method";
    public static final String REFERRING_SITE = "referring_site";
    public static final String SHIPPING_LINES = "shipping_lines";
    public static final String SOURCE_NAME = "source_name";
    public static final String SUBTOTAL_PRICE = "subtotal_price";
    public static final String SUBTOTAL_PRICE_SET = "subtotal_price_set";
    public static final String TOTAL_DISCOUNTS = "total_discounts";
    public static final String TOTAL_DISCOUNTS_SET = "total_discounts_set";
    public static final String TOTAL_LINE_ITEMS_PRICE = "total_line_items_price";
    public static final String TOTAL_LINE_ITEMS_PRICE_SET = "total_line_items_price_set";
    public static final String TOTAL_OUTSTANDING = "total_outstanding";
    public static final String TOTAL_PRICE_SET = "total_price_set";
    public static final String TOTAL_SHIPPING_PRICE_SET = "total_shipping_price_set";
    public static final String TOTAL_TAX = "total_tax";
    public static final String TOTAL_TAX_SET = "total_tax_set";
    public static final String TOTAL_TIP_RECEIVED = "total_tip_received";
    public static final String TOTAL_WEIGHT = "total_weight";
    public static final String USER_ID = "user_id";
    public static final String ORDER_STATUS_URL = "order_status_url";

    public static final String ATTRIBUTION_APP_ID = "attribution_app_id";
    public static final String PROCESSED_AT_MAX = "processed_at_max";
    public static final String PROCESSED_AT_MIN = "processed_at_max";

    public static final String INVENTORY_BEHAVIOUR = "inventory_behaviour";
    public static final String SEND_RECEIPT = "send_receipt";
    public static final String SEND_FULFILLMENT_RECEIPT = "send_fulfillment_receipt";

    // Webhook
    public static final String WEBHOOK = "webhook";
    public static final String WEBHOOKS = "webhooks";

    public static final String TOPIC = "topic";
    public static final String ADDRESS = "address";
    public static final String FORMAT = "format";

    // Image
    public static final String SRC = "src";
    public static final String ATTACHMENT = "attachment";
    public static final String ALT = "alt";
    public static final String HEIGHT = "height";
    public static final String WIDTH = "width";
    public static final String VARIANT_IDS = "variant_ids";

    // Transaction
    public static final String TRANSACTIONS = "transactions";
    public static final String ORDER_ID = "order_id";
    public static final String KIND = "kind";
    public static final String STATUS = "status";
    public static final String CURRENCY = "currency";
    public static final String AMOUNT = "amount";
    public static final String DEVICE_ID = "device_id";
    public static final String PARENT_ID = "parent_id";
    public static final String ERROR_CODE = "error_code";
    public static final String PAYMENT_DETAILS = "payment_details";

    public static final String AVS_RESULT_CODE = "avs_result_code";
    public static final String CREDIT_CARD_BIN = "credit_card_bin";
    public static final String CVV_RESULT_CODE = "cvv_result_code";
    public static final String CREDIT_CARD_NUMBER = "credit_card_number";
    public static final String CREDIT_CARD_COMPANY = "credit_card_company";

    public static final String ORIGINAL_AMOUNT = "original_amount";
    public static final String FINAL_AMOUNT = "final_amount";
    public static final String CURRENCY_EXCHANGE_ADJUSTMENT = "currency_exchange_adjustment";

    // Recurring application
    public static final String RECURRING_APPLICATION_CHARGE = "recurring_application_charge";

    public static final String CHARGE_ID = "id";
    public static final String PLAN_NAME = "name";
    public static final String PLAN_PRICE = "price";
    public static final String TERMS = "terms";
    public static final String CHARGE_STATUS = "status";
    public static final String BILLING_ON = "billing_on";
    public static final String UPDATED_AT = "updated_at";
    public static final String TEST = "test";
    public static final String ACTIVATED_ON = "activated_on";
    public static final String TRIAL_ENDS_ON = "trial_ends_on";
    public static final String CANCELED_ON = "cancelled_on";
    public static final String TRIAL_DAYS = "trial_days";
    public static final String CAPPED_AMOUNT = "capped_amount";
    public static final String BALANCE_USED = "balance_used";
    public static final String BALANCE_REMAINING = "balance_remaining";
    public static final String RISK_LEVEL = "risk_level";
    public static final String RETURN_URL = "return_url";
    public static final String DECORATED_RETURN_URL = "decorated_return_url";
    public static final String CONFIRMATION_URL = "confirmation_url";

    // Shop
    public static final String CHECKOUT_API_SUPPORTED = "checkout_api_supported";
    public static final String COUNTRY_CODE = "country_code";
    public static final String COUNTRY_NAME = "country_name";
    public static final String COUNTY_TAXES = "county_taxes";
    public static final String CUSTOMER_EMAIL = "customer_email";
    public static final String ENABLED_PRESENTMENT_CURRENCIES = "enabled_presentment_currencies";
    public static final String ELIGIBLE_FOR_CARD_READER_GIVEAWAY = "eligible_for_card_reader_giveaway";
    public static final String ELIGIBLE_FOR_PAYMENTS = "eligible_for_payments";
    public static final String FORCE_SSL = "force_ssl";
    public static final String GOOGLE_APPS_DOMAIN = "google_apps_domain";
    public static final String GOOGLE_APPS_LOGIN_ENABLED = "google_apps_login_enabled";
    public static final String HAS_DISCOUNTS = "has_discounts";
    public static final String WEIGHT_UNIT = "weight_unit";
    public static final String TAX_SHIPPING = "tax_shipping";
    public static final String TAXES_INCLUDED = "taxes_included";
    public static final String SHOP_OWNER = "shop_owner";
    public static final String SETUP_REQUIRED = "setup_required";
    public static final String REQUIRES_EXTRA_PAYMENTS_AGREEMENT = "requires_extra_payments_agreement";
    public static final String PROVINCE_CODE = "province_code";
    public static final String PRIMARY_LOCATION_ID = "primary_location_id";
    public static final String PASSWORD_ENABLED = "password_enabled";
    public static final String COOKIE_CONSENT_LEVEL = "cookie_consent_level";
    public static final String PRE_LAUNCH_ENABLED = "pre_launch_enabled";
    public static final String PLAN_DISPLAY_NAME = "plan_display_name";
    public static final String MYSHOPIFY_DOMAIN = "myshopify_domain";
    public static final String MULTI_LOCATION_ENABLED = "multi_location_enabled";
    public static final String MONEY_WITH_CURRENCY_IN_EMAILS_FORMAT = "money_with_currency_in_emails_format";
    public static final String MONEY_WITH_CURRENCY_FORMAT = "money_with_currency_format";
    public static final String MONEY_IN_EMAILS_FORMAT = "money_in_emails_format";
    public static final String MONEY_FORMAT = "money_format";
    public static final String IANA_TIMEZONE = "iana_timezone";
    public static final String HAS_STOREFRONT = "has_storefront";
    public static final String HAS_GIFT_CARDS = "has_gift_cards";

    // AccessToken
    public static final String ACCESS_TOKEN = "access_token";
    public static final String EXPIRES_IN = "expires_in";
    public static final String ASSOCIATED_USER_SCOPE = "associated_user_scope";

    public static final String UPDATED_AT_MIN = "updated_at_min";
    public static final String UPDATED_AT_MAX = "updated_at_max";
    public static final String PUBLISHED_AT_MIN = "published_at_min";
    public static final String PUBLISHED_AT_MAX = "published_at_max";
    public static final String COLLECT_ID = "collect_id";


    // Metafields
    public static final String OWNER_RESOURCE = "owner_resource";
    public static final String OWNER_ID = "owner_id";
    public static final String VALUE_TYPE = "value_type";

    public static final String CREATED_AT_MIN = "created_at_min";
    public static final String CREATED_AT_MAX = "created_at_max";
    public static final String PRESENTMENT_CURRENCIES = "presentment_currencies";


    // Inventory
    public static final String HARMONIZED_SYSTEM_CODE = "harmonized_system_code";
    public static final String COUNTRY_CODE_OF_ORIGIN = "country_code_of_origin";
    public static final String COUNTRY_HARMONIZED_SYSTEM_CODES = "country_harmonized_system_codes";
    public static final String PROVINCE_CODE_OF_ORIGIN = "province_code_of_origin";
    public static final String REQUIRES_SHIPPING = "requires_shipping";
    public static final String INVENTORY_ITEMS = "inventory_items";
    public static final String LOCATION_ID = "location_id";
    public static final String STOCK_ITEM_IDS = "stock_item_ids";
    public static final String LOCATION_IDS = "location_ids";
    public static final String INVENTORY_LEVELS = "inventory_levels";
    public static final String STOCK_ITEM_ID = "stock_item_id";
    public static final String AVAILABLE_ADJUSTMENT = "available_adjustment";
    public static final String RELOCATE_IF_NECESSARY = "relocate_if_necessary";
    public static final String DISCONNECT_IF_NECESSARY = "disconnect_if_necessary";

    // Location
    public static final String LOCALIZED_COUNTRY_NAME = "localized_country_name";
    public static final String LOCALIZED_PROVINCE_NAME = "localized_province_name";

    // FulfillmentService
    public static final String CALLBACK_URL = "callback_url";
    public static final String FULFILLMENT_ORDERS_OPT_IN = "fulfillment_orders_opt_in";
    public static final String PROVIDER_ID = "provider_id";
    public static final String REQUIRES_SHIPPING_METHOD = "requires_shipping_method";
    public static final String TRACKING_SUPPORT = "tracking_support";
    public static final String FULFILLMENT_SERVICES = "fulfillment_services";
    public static final String FULFILLMENT_ORDER_LINE_ITEMS = "fulfillment_order_line_items";
    // FulfillmentOrder
    public static final String SHOP_ID = "shop_id";
    public static final String FULFILLMENT_ORDER_ID = "fulfillment_order_id";
    public static final String LINE_ITEM_ID = "line_item_id";
    public static final String FULFILLABLE_QUANTITY = "fulfillable_quantity";
    public static final String SHIPPING_METHOD = "shipping_method";
    public static final String REQUEST_OPTIONS = "request_options";
    public static final String ASSIGNED_LOCATION_ID = "assigned_location_id";
    public static final String REQUEST_STATUS = "request_status";
    public static final String SUPPORTED_ACTIONS = "supported_actions";
    public static final String MERCHANT_REQUESTS = "merchant_requests";
    public static final String ASSIGNED_LOCATION = "assigned_location";
    public static final String FULFILLMENT_ORDERS = "fulfillment_orders";
    public static final String NEW_LOCATION_ID = "new_location_id";
    // Fulfillment
    public static final String SHIPMENT_STATUS = "shipment_status";
    public static final String TRACKING_COMPANY = "tracking_company";
    public static final String TRACKING_NUMBERS = "tracking_numbers";
    public static final String TRACKING_URL = "tracking_url";
    public static final String TRACKING_URLS = "tracking_urls";
    public static final String NOTIFY_CUSTOMER = "notify_customer";
    public static final String TRACKING_NUMBER = "tracking_number";
    public static final String TRACKING_INFO = "tracking_info";
    public static final String LINE_ITEMS_BY_FULFILLMENT_ORDER = "line_items_by_fulfillment_order";
    // CarrierService
    public static final String SERVICE_DISCOVERY = "service_discovery";
    public static final String CARRIER_SERVICE_TYPE = "carrier_service_type";

    // money
    public static final String SHOP_MONEY = "shop_money";
    public static final String PRESENTMENT_MONEY = "presentment_money";
    public static final String PRICE_SET = "price_set";
    public static final String TAX_LINES = "tax_lines";
    public static final String GIFT_CARD = "gift_card";
    public static final String VARIANT_INVENTORY_MANAGEMENT = "variant_inventory_management";
    public static final String TOTAL_DISCOUNT = "total_discount";
    public static final String FULFILLMENT_STATUS = "fulfillment_status";

    // ClientDetails
    public static final String ACCEPT_LANGUAGE = "accept_language";
    public static final String BROWSER_HEIGHT = "browser_height";
    public static final String BROWSER_IP = "browser_ip";
    public static final String BROWSER_WIDTH = "browser_width";
    public static final String SESSION_HASH = "session_hash";
    public static final String USER_AGENT = "user_agent";

    // DiscountApplication
    public static final String ALLOCATION_METHOD = "allocation_method";
    public static final String TARGET_SELECTION = "target_selection";
    public static final String TARGET_TYPE = "target_type";

    // ShippingLine
    public static final String DISCOUNTED_PRICE = "discounted_price";
    public static final String DISCOUNTED_PRICE_SET = "discounted_price_set";
    public static final String CARRIER_IDENTIFIER = "carrier_identifier";
    public static final String REQUESTED_FULFILLMENT_SERVICE_ID = "requested_fulfillment_service_id";

    // OrderAdjustment
    public static final String REFUND_ID = "refund_id";
    public static final String TAX_AMOUNT = "tax_amount";
    public static final String AMOUNT_SET = "amount_set";
    public static final String TAX_AMOUNT_SET = "tax_amount_set";
    // RefundDuty
    public static final String DUTY_ID = "duty_id";
    public static final String REFUND_TYPE = "refund_type";
    // RefundLineItem
    public static final String LINE_ITEM = "line_item";
    public static final String RESTOCK_TYPE = "restock_type";
    public static final String SUBTOTAL_SET = "subtotal_set";
    // Refund
    public static final String ORDER_ADJUSTMENTS = "order_adjustments";
    public static final String REFUND_DUTIES = "refund_duties";
    public static final String REFUND_LINE_ITEMS = "refund_line_items";
}
