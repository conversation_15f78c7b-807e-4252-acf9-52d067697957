package com.zsmall.extend.shop.shopify.model.product;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import com.zsmall.extend.shop.shopify.model.Image;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.LinkedList;
import java.util.List;

/**
 * @date 2022-12-24 17:28
 */
@Data
@EqualsAndHashCode(callSuper=false)
@Accessors(chain = true)
public class ProductImage extends Image {

    /**
     * The unique numeric identifier for the product in the custom collection.
     */
    @Alias(JsonConstants.PRODUCT_ID)
    private Long productId;

    @Alias(JsonConstants.VARIANT_IDS)
    private List<String> variantIds = new LinkedList<>();

}
