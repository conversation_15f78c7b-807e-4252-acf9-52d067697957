package com.zsmall.extend.shop.shopify.model.order;

import com.zsmall.extend.shop.shopify.enums.DiscountType;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @date 2022-12-28 12:25
 */
@Data
@Accessors(chain = true)
public class DiscountCode {

    /**
     * 当关联的折扣应用程序为type时code，此属性返回在结帐时输入的折扣代码。否则，此属性返回所应用折扣的标题。
     */
    private String code;

    /**
     * 从订单总额中扣除的金额。创建订单时，此值是要扣除的百分比或金额。创建订单后，此属性返回计算的金额。
     * The amount that's deducted from the order total. When you create an order, this value is the percentage or
     * monetary amount to deduct. After the order is created, this property returns the calculated amount.
     */
    private Double amount;

    /**
     * 折扣的类型
     */
    private DiscountType type;

}
