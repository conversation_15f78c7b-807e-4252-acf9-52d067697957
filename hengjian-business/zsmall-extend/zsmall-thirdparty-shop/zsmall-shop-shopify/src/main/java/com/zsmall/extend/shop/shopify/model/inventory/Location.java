package com.zsmall.extend.shop.shopify.model.inventory;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;

/**
 * 位置
 *
 * @date 2022-12-25 16:10
 */
@Data
@Accessors(chain = true)
public class Location {

    private Long id;

    /**
     * Whether the location is active. If true, then the location can be used to sell products, stock inventory, and
     * fulfill orders. Merchants can deactivate locations from the Shopify admin. Deactivated locations don't
     * contribute to the shop's location limit.
     */
    private Boolean active;

    /**
     * The first line of the address.
     */
    private String address1;

    /**
     * The second line of the address.
     */
    private String address2;

    /**
     * The city the location is in.
     */
    private String city;

    /**
     * The country the location is in.
     */
    private String country;

    /**
     * The two-letter code (ISO 3166-1 alpha-2 format) corresponding to country the location is in.
     */
    @Alias(JsonConstants.COUNTRY_CODE)
    private String country_code;

    /**
     * The date and time (ISO 8601 format) when the location was created.
     */
    @Alias(JsonConstants.CREATED_AT)
    private ZonedDateTime createAt;

    /**
     * 这是否是履行服务位置。如果为true，则该位置为配送服务位置。如果为false，则该位置是由商家创建的，并且不依赖于配送服务。
     * <p>
     * Whether this is a fulfillment service location. If true, then the location is a fulfillment service location. If
     * false, then the location was created by the merchant and isn't tied to a fulfillment service.
     */
    private Boolean legacy;

    /**
     * The name of the location.
     */
    private String name;


    /**
     * 位置的电话号码。该值可以包含特殊字符，例如-和+。
     * The phone number of the location. This value can contain special characters like - and +.
     */
    private String phone;

    /**
     * The province the location is in.
     */
    private String province;

    /**
     * The two-letter code corresponding to province or state the location is in.
     */
    @Alias(JsonConstants.PROVINCE_CODE)
    private String provinceCode;

    /**
     * The date and time (ISO 8601 format) when the location was last updated.
     */
    @Alias(JsonConstants.UPDATED_AT)
    private ZonedDateTime updatedAt;

    /**
     * The zip or postal code.
     */
    private String zip;

    /**
     * 所在国家/地区的本地名称。
     * The localized name of the location's country.
     */
    @Alias(JsonConstants.LOCALIZED_COUNTRY_NAME)
    private String localizedCountryName;

    /**
     * 位置区域的本地化名称。通常是一个省，州或州。
     * The localized name of the location's region. Typically a province, state, or prefecture.
     */
    @Alias(JsonConstants.LOCALIZED_PROVINCE_NAME)
    private String localizedProvinceName;


}
