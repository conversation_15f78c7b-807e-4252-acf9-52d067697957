package com.zsmall.extend.shop.shopify.exception;


import com.zsmall.extend.shop.shopify.enums.ShopifyErrorEnums;

public class ShopifyClientException extends RuntimeException {

    private static final long serialVersionUID = -5992356578452439224L;

    private final String code;
    private final String description;

    public ShopifyClientException(final String code, final String description) {
        super(description);
        this.description = description;
        this.code = code;
    }

    public ShopifyClientException(ShopifyErrorEnums shopifyErrorEnums) {
        super(shopifyErrorEnums.getDescription());
        this.description = shopifyErrorEnums.getDescription();
        this.code = shopifyErrorEnums.name();
    }

    public ShopifyClientException(final String description, final Throwable throwable) {
        super(description, throwable);
        this.description = description;
        this.code = ShopifyErrorEnums.CLIENT_EXCEPTION.name();
    }

    public ShopifyClientException(final String code, final String description, final Throwable throwable) {
        super(description, throwable);
        this.description = description;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
