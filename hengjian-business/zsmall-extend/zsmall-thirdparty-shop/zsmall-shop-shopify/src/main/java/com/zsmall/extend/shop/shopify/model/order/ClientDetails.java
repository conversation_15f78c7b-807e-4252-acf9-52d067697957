package com.zsmall.extend.shop.shopify.model.order;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @date 2022-12-28 0:45
 */
@Data
@Accessors(chain = true)
public class ClientDetails {

    /**
     *
     */
    @Alias(JsonConstants.ACCEPT_LANGUAGE)
    private String acceptLanguage;
    /**
     * 浏览器屏幕高度（以像素为单位）（如果有）。
     * The browser screen height in pixels, if available.
     */
    @<PERSON>as(JsonConstants.BROWSER_HEIGHT)
    private int browserHeight;
    /**
     * 浏览器IP地址。
     * The browser IP address.
     */
    @Alias(JsonConstants.BROWSER_IP)
    private String browserIp;
    /**
     * 以像素为单位的浏览器屏幕宽度（如果有）。
     * The browser screen width in pixels, if available.
     */
    @<PERSON>as(JsonConstants.BROWSER_WIDTH)
    private int BrowserWidth;
    /**
     * 会话的哈希。
     * A hash of the session.
     */
    @Alias(JsonConstants.SESSION_HASH)
    private String sessionHash;
    /**
     * Details of the browsing client, including software and operating versions.
     */
    @Alias(JsonConstants.USER_AGENT)
    private String userAgent;

}
