package com.zsmall.extend.shop.shopify.enums;

/**
 * Shopify 错误枚举
 */
public enum ShopifyErrorEnums {

    CLIENT_EXCEPTION(""),

    URL_IS_INCORRECT("The request url is incorrect."),

    RESPONSE_CONTENT_EMPTY("Shopify api response content is empty."),

    GENERAL_ACCESS_TOKEN_EXCEPTION("There was a problem generating access token"),

    PARAMETER_CAN_NOT_BE_EMPTY("The request parameter can not be empty."),

    CURRENCY_NOT_BE_EMPTY("For multi-currency orders, the currency property is required whenever the amount property is provided."),

    ;

    private String description;

    ShopifyErrorEnums(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

}
