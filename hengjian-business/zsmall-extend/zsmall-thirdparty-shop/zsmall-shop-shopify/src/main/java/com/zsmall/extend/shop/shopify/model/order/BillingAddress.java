package com.zsmall.extend.shop.shopify.model.order;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @date 2022-12-27 23:54
 */
@Data
@Accessors(chain = true)
public class BillingAddress {

    /**
     * 帐单地址的街道地址
     */
    private String address1;

    /**
     * 帐单地址的街道地址
     */
    private String address2;

    /**
     * 帐单邮寄地址的城市，城镇或村庄。
     */
    private String city;

    /**
     * 与帐单地址相关联的人的公司。
     */
    private String company;

    /**
     * 与帐单地址相关联的人的公司。
     */
    private String country;

    /**
     * 与付款方式相关联的人员的名字。
     */
    @Alias(JsonConstants.FIRST_NAME)
    private String firstName;

    /**
     * 与付款方式相关联的人员的姓氏。
     */
    @Alias(JsonConstants.LAST_NAME)
    private String lastName;

    /**
     * 帐单地址上的电话号码。
     */
    private String phone;

    /**
     * 帐单地址上的电话号码。
     */
    private String province;

    /**
     * 帐单邮寄地址的邮政编码（邮政编码，邮政编码，Eircode等）。
     */
    private String zip;

    /**
     * 帐单邮寄地址的区域的两个字母的缩写
     */
    private String name;

    /**
     * 帐单邮寄地址的区域的两个字母的缩写
     */
    @Alias(JsonConstants.PROVINCE_CODE)
    private String provinceCode;

    /**
     * 帐单邮寄地址所在国家/地区的名称。
     */
    @Alias(JsonConstants.COUNTRY_CODE)
    private String countryCode;

    /**
     * 帐单地址的纬度。
     */
    private Double latitude;

    /**
     * 帐单地址的纬度。
     */
    private Double longitude;

}
