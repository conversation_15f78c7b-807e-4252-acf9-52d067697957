package com.zsmall.extend.shop.shopify.model.order;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 堆叠折扣应用程序的有序列表。
 * <p>
 * The discount_applications property includes 3 types: discount_code, manual, and script. All 3 types share a common
 * structure and have some type specific attributes.
 *
 * @date 2022-12-28 12:03
 */
@Data
@Accessors(chain = true)
public class DiscountApplication {

    /**
     * The discount application type. Valid values:
     * automatic: The discount was applied automatically, such as by a Buy X Get Y automatic discount.
     * discount_code: The discount was applied by a discount code.
     * manual: The discount was manually applied by the merchant (for example, by using an app or creating a draft order).
     * script: The discount was applied by a Shopify Script.
     * <p>
     * type：折扣应用程序类型。有效值：
     * automatic：折扣是自动应用的，例如“买X送Y”的自动折扣。
     * discount_code：折扣已通过折扣代码应用。
     * manual：折扣是由商家手动应用的（例如，通过使用应用或创建草稿订单）。
     * script：折扣是由Shopify脚本应用的。
     */
    private String type;

    /**
     * 用于应用折扣的折扣代码。仅适用于折扣代码应用程序。
     * The discount code that was used to apply the discount. Available only for discount code applications.
     */
    private String code;

    /**
     * The title of the discount application, as defined by the merchant. Available only for manual discount applications.
     * 折扣应用程序的标题，由商家定义。仅适用于手动折扣应用程序。
     */
    private String title;

    /**
     * 折扣应用程序的描述，由商人或Shopify脚本定义。仅适用于手动和脚本折扣应用程序。
     * The description of the discount application, as defined by the merchant or the Shopify Script. Available only for manual and script discount applications.
     */
    private String description;

    /**
     * 折扣应用程序的值（以十进制表示）。这代表了折扣申请的意图。例如，如果意图是应用20％的折扣，则值将为20.0。如果打算进行$ 15的折扣，则价值为15.0。
     * he value of the discount application as a decimal. This represents the intention of the discount application. For example, if the intent was to apply a 20% discount, then the value will be 20.0. If the intent was to apply a $15 discount, then the value will be 15.0.
     */
    private String value;

    /**
     * The type of the value. Valid values:
     * fixed_amount: A fixed amount discount value in the currency of the order.
     * percentage: A percentage discount value.
     * <p>
     * 值的类型。有效值：
     * fixed_amount：定额折扣值，以订单货币表示。
     * percentage：百分比折扣值。
     */
    @Alias(JsonConstants.VALUE_TYPE)
    private String valueType;

    /**
     * The method by which the discount application value has been allocated to entitled lines. Valid values:
     * across: The value is spread across all entitled lines.
     * each: The value is applied onto every entitled line.
     * one: The value is applied onto a single line.
     * 将折扣应用程序值分配给已授权行的方法。有效值：
     * across：该值分布在所有授权行中。
     * each：该值应用于所有授权行。
     * one：该值应用于一行。
     */
    @Alias(JsonConstants.ALLOCATION_METHOD)
    private String allocationMethod;

    /**
     * The lines on the order, of the type defined by target_type, that the discount is allocated over. Valid values:
     * all: The discount is allocated onto all lines,
     * entitled: The discount is allocated only onto lines it is entitled for.
     * explicit: The discount is allocated onto explicitly selected lines
     * <p>
     * target_type分配折扣的订单上的行，类型由定义。有效值：
     * all：折扣分配到所有行，
     * entitled：折扣仅分配给有权使用的行。
     * explicit：折扣分配到明确选择的行。
     */
    @Alias(JsonConstants.TARGET_SELECTION)
    private String targetSelection;

    /**
     * The type of line on the order that the discount is applicable on. Valid values:
     * line_item: The discount applies to line items.
     * shipping_line: The discount applies to shipping lines.
     * <p>
     * 适用折扣的订单行的类型。有效值：
     * line_item：折扣适用于订单项。
     * shipping_line：折扣适用于运输公司。
     */
    @Alias(JsonConstants.TARGET_TYPE)
    private String targetType;

}
