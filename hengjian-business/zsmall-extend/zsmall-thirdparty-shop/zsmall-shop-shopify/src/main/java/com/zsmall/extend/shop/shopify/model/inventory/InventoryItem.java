package com.zsmall.extend.shop.shopify.model.inventory;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * 库存物品
 *
 * @date 2022-12-25 10:07
 */
@Data
@Accessors(chain = true)
public class InventoryItem {

    private Long id;

    /**
     * The unit cost of the inventory item.
     */
    private Double cost;

    /**
     * The two-digit code for the country where the inventory item was made.
     */
    @Alias(JsonConstants.COUNTRY_CODE_OF_ORIGIN)
    private String countryCodeOfOrigin;


    /**
     * 物料的 一组特定于国家/地区的协调系统（HS）代码。用于确定将库存物品运送到某些国家/地区时的关税。
     * TAn array of country-specific Harmonized System (HS) codes for the item. Used to determine duties when shipping the inventory item to certain countries.
     * [{
     * "harmonized_system_code": "1234561111",
     * "country_code": "CA"
     * }]
     */
    @Alias(JsonConstants.COUNTRY_HARMONIZED_SYSTEM_CODES)
    private List<CountryHarmonizedSystemCode> countryHarmonizedSystemCodes;

    /**
     * The date and time (ISO 8601 format) when the collect was created.
     */
    @Alias(JsonConstants.CREATED_AT)
    private ZonedDateTime createAt;

    /**
     * 库存项目 的通用协调系统（HS）代码。如果特定国家/地区的HS代码不可用，则使用此代码。
     * The general Harmonized System (HS) code for the inventory item. Used if a country-specific HS code is not available.
     */
    @Alias(JsonConstants.HARMONIZED_SYSTEM_CODE)
    private Long harmonizedSystemCode;

    /**
     * 产生库存物料的省的两位数字代码。仅在库存商品的运输提供商为加拿大邮政时使用。
     * The two-digit code for the province where the inventory item was made. Used only if the shipping provider for the inventory item is Canada Post.
     */
    @Alias(JsonConstants.PROVINCE_CODE_OF_ORIGIN)
    private String provinceCodeOfOrigin;

    /**
     * 库存物料的唯一SKU（库存单位）。
     * The unique SKU (stock keeping unit) of the inventory item.
     */
    private String sku;

    /**
     * 是否跟踪库存项目。如果为true，则Shopify会跟踪库存数量更改。
     * Whether the inventory item is tracked. If true, then inventory quantity changes are tracked by Shopify.
     */
    private Boolean tracked;

    /**
     * 上次修改库存项目 的日期和时间（ISO 8601格式）。
     * The date and time (ISO 8601 format) when the collect was last updated.
     */
    @Alias(JsonConstants.UPDATED_AT)
    private ZonedDateTime updatedAt;

    /**
     * 下订单包含库存商品时，客户是否需要提供送货地址。
     * Whether a customer needs to provide a shipping address when placing an order containing the inventory item.
     */
    @Alias(JsonConstants.REQUIRES_SHIPPING)
    private boolean requiresShipping;


}
