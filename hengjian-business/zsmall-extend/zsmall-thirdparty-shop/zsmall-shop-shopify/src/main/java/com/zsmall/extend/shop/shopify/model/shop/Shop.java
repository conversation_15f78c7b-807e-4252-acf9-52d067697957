package com.zsmall.extend.shop.shopify.model.shop;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * Shopify店铺实体类
 *
 * <AUTHOR>
 * @create 2022/5/26 20:06
 */
@Data
public class Shop {

    private long id;
    private String name;
    private String email;
    private String domain;
    private String province;
    private String country;
    private String address1;
    private String zip;
    private String city;
    private String source;
    private String phone;
    private double latitude;
    private double longitude;
    private String primary_locale;
    private Date address2;
    private Date created_at;
    private Date updated_at;
    private String country_code;
    private String country_name;
    private String currency;
    private String customer_email;
    private String timezone;
    private String iana_timezone;
    private String shop_owner;
    private String money_format;
    private String money_with_currency_format;
    private String weight_unit;
    private String province_code;
    private String taxes_included;
    private String auto_configure_tax_inclusivity;
    private String tax_shipping;
    private boolean county_taxes;
    private String plan_display_name;
    private String plan_name;
    private boolean has_discounts;
    private boolean has_gift_cards;
    private String myshopify_domain;
    private String google_apps_domain;
    private String google_apps_login_enabled;
    private String money_in_emails_format;
    private String money_with_currency_in_emails_format;
    private boolean eligible_for_payments;
    private boolean requires_extra_payments_agreement;
    private boolean password_enabled;
    private boolean has_storefront;
    private boolean eligible_for_card_reader_giveaway;
    private boolean finances;
    private long primary_location_id;
    private String cookie_consent_level;
    private String visitor_tracking_consent_preference;
    private boolean checkout_api_supported;
    private boolean multi_location_enabled;
    private boolean setup_required;
    private boolean pre_launch_enabled;
    private List<String> enabled_presentment_currencies;
    private boolean transactional_sms_disabled;

}
