package com.zsmall.extend.shop.shopify.model.accesstoken;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import lombok.Data;

/**
 * @date 2022-11-22 0:25
 */
@Data
public class AccessToken {

    /**
     * 认证token
     */
    @Alias(value = JsonConstants.ACCESS_TOKEN)
    private String accessToken;

    /**
     * 访问范围
     */
    private String scope;

    /**
     * 超时时间
     */
    @Alias(value = JsonConstants.EXPIRES_IN)
    private Long expiresIn;

    /**
     * 管理用户，访问范围
     */
    @Alias(value = JsonConstants.ASSOCIATED_USER_SCOPE)
    private String associatedUserScope;

    /**
     * session id
     */
    private String session;

}
