package com.zsmall.extend.shop.shopify.model.order.refund;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import com.zsmall.extend.shop.shopify.model.order.transaction.Transaction;
import com.zsmall.extend.shop.shopify.model.shop.Duty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * @date 2022-12-28 17:48
 */
@Data
@Accessors(chain = true)
public class Refund {

    /**
     * 创建退款的日期和时间（ISO 8601格式）。
     */
    @Alias(JsonConstants.CREATED_AT)
    private ZonedDateTime createdAt;

    /**
     * 退款中已退还的关税清单。
     * A list of duties that have been returned as part of the refund.
     */
    private List<Duty> duties;

    /**
     * 退款的唯一标识符。
     */
    private Long id;

    /**
     * 退款随附的可选注释。
     */
    private String note;

    /**
     * A list of order adjustments attached to the refund. Order adjustments are generated to account for refunded
     * shipping costs and differences between calculated and actual refund amounts
     * 退款所附的订单调整清单。生成订单调整以考虑退款的运输成本以及计算出的退款额与实际退款额之间的差额。
     */
    @Alias(JsonConstants.ORDER_ADJUSTMENTS)
    private List<OrderAdjustment> order_adjustments;

    /**
     * The date and time (ISO 8601 format) when the refund was imported. This value can be set to a date in the past
     * when importing from other systems. If no value is provided, then it will be auto-generated as the current time
     * in Shopify.
     * 导入退款的日期和时间（ISO 8601格式）。从其他系统导入时，可以将该值设置为过去的日期。如果未提供任何值，则它将作为Shopify中的当前时间自动生成。
     */
    @Alias(JsonConstants.PROCESSED_AT)
    private ZonedDateTime processedAt;

    /**
     * A list of refunded duties.
     */
    @Alias(JsonConstants.REFUND_DUTIES)
    private List refund_duties;

    /**
     * A list of refunded line items
     * 退款的订单项列表
     */
    @Alias(JsonConstants.REFUND_LINE_ITEMS)
    private List<RefundLineItem> refund_line_items;

    /**
     * A list of transactions involved in the refund. For more information, see the Transaction resource.
     * 退款涉及的交易清单。有关更多信息，请参见事务资源。
     */
    @Alias(JsonConstants.TRANSACTIONS)
    private List<Transaction> transactions;

    /**
     * The ID of the user logged into Shopify POS who processed the order, if applicable.
     * 登录到Shopify POS并处理订单的用户的ID（如果有）。
     */
    @Alias(JsonConstants.USER_ID)
    private Long userId;
}
