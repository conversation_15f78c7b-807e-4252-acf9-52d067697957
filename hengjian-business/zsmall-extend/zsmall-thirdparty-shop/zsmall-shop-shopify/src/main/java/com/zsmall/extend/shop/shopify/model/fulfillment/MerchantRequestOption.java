package com.zsmall.extend.shop.shopify.model.fulfillment;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 商家返回的请求选项
 *
 * @date 2022-12-26 16:44
 */
@Data
@Accessors(chain = true)
public class MerchantRequestOption {

    /**
     *
     */
    @Alias(JsonConstants.SHIPPING_METHOD)
    private String shippingMethod;

    private String note;

    private String date;

}
