package com.zsmall.extend.shop.shopify.model.product;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import lombok.Data;

import java.util.List;

@Data
public class VariantOption {

    @Alias(JsonConstants.ID)
    private long id;

    @<PERSON>as(JsonConstants.OPTION_NAME)
    private String name;

    @<PERSON>as(JsonConstants.OPTION_POSITION)
    private int position;

    @Alias(JsonConstants.OPTION_VALUES)
    private List<String> values;
}
