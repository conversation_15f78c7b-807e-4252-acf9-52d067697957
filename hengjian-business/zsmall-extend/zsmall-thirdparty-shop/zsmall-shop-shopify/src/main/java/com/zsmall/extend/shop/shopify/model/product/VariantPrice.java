package com.zsmall.extend.shop.shopify.model.product;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 商店支持的每种展示货币的变体的展示价格和比较价格的列表
 *
 * @date 2022-12-25 0:59
 */
@Data
@Accessors(chain = true)
public class VariantPrice {

    /**
     * The three-letter code (ISO 4217 format) for one of the shop's enabled presentment currencies.
     */
    @Alias(JsonConstants.CURRENCY_CODE)
    private String currencyCode;

    /**
     * The variant's price or compare-at price in the presentment currency.
     */
    private Double amount;

}
