package com.zsmall.extend.shop.shopify.model.shop;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @date 2022-12-27 1:10
 */
@Data
@Accessors(chain = true)
public class PriceSet {

    @Alias(JsonConstants.SHOP_MONEY)
    private ShopMoney shopMoney;

    @Alias(JsonConstants.PRESENTMENT_MONEY)
    private ShopMoney presentmentMoney;

}
