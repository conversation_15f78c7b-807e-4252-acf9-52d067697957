package com.zsmall.extend.shop.shopify.enums;

/**
 * pending：付款未决。在这种状态下付款可能会失败。再次检查以确认付款是否已成功支付。
 * authorized：付款已被授权。
 * partially_paid：订单已支付了部分费用。
 * paid：已付款。
 * partially_refunded：该款项已部分退还。
 * refunded：款项已退还。
 * voided：付款已无效。
 * <p>
 * pending: The payments are pending. Payment might fail in this state. Check again to confirm whether the payments
 * have been paid successfully.
 * authorized: The payments have been authorized.
 * partially_paid: The order have been partially paid.
 * paid: The payments have been paid.
 * partially_refunded: The payments have been partially refunded.
 * refunded: The payments have been refunded.
 * voided: The payments have been voided.
 */
public enum FinancialStatus {

    pending,

    authorized,

    partially_paid,

    paid,

    partially_refunded,

    refunded,

    voided,

    ;

}
