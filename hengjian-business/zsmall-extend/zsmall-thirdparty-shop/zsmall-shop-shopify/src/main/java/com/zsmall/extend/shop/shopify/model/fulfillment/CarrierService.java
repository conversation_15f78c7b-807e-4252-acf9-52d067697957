package com.zsmall.extend.shop.shopify.model.fulfillment;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 承运人服务信息
 */
@Data
@Accessors(chain = true)
public class CarrierService {

    private Long id;

    private String name;

    private String active;

    /**
     * Shopify需要检索运费的URL端点。 这必须是一个公共URL。
     * The URL endpoint that Shopify needs to retrieve shipping rates. This must be a public URL.
     */
    @Alias(JsonConstants.CALLBACK_URL)
    private String callbackUrl;

    /**
     * 商家是否能够通过Shopify管理员将虚拟数据发送到您的服务，以查看运费示例。
     * Whether merchants are able to send dummy data to your service through the Shopify admin to see shipping rate examples.
     */
    @Alias(JsonConstants.SERVICE_DISCOVERY)
    private Boolean serviceDiscovery;

    /**
     * 区分API或传统运营商服务。
     */
    @Alias(JsonConstants.CARRIER_SERVICE_TYPE)
    private String carrierServiceType = "api";

}
