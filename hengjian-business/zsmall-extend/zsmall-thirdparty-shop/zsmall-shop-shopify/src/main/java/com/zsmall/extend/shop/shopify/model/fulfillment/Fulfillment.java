package com.zsmall.extend.shop.shopify.model.fulfillment;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import com.zsmall.extend.shop.shopify.enums.FulfillmentStatus;
import com.zsmall.extend.shop.shopify.enums.ShipmentStatus;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * You can use the Fulfillment resource to view, create, modify, or delete an order's or fulfillment order's
 * fulfillments. A fulfillment order represents a group of one or more items in an order that are to be fulfilled
 * from the same location. A fulfillment represents work that is completed as part of a fulfillment order and can
 * include one or more items. You can use the Fulfillment resource to manage fulfillments for both orders and
 * fulfillment orders.
 * 您可以使用“履行”资源来查看，创建，修改或删除订单或履行订单的履行。履行订单表示要从同一位置履行的订单中的一组一个或多个项目。
 * 履行是指作为履行订单的一部分完成的工作，可以包括一个或多个项目。您可以使用“配送”资源来管理订单和配送订单的配送。
 * <p>
 * This resource is most often used in apps that perform shipping-related actions, such as making tracking and
 * delivery updates, or creating additional shipments as required for an order or fulfillment order.
 * 此资源最常用于执行与运输有关的操作的应用程序，例如进行跟踪和交付更新，或根据订单或履行订单的需要创建其他运输。
 * Each fulfillment supports a single tracking number. If you need to use multiple tracking numbers, then you should
 * create separate fulfillments.
 * 每个履行都支持一个跟踪号。如果需要使用多个跟踪编号，则应创建单独的履行。
 *
 * @date 2022-12-28 12:32
 */
@Data
@Accessors(chain = true)
public class Fulfillment {

    /**
     * 创建完成的日期和时间。API以ISO 8601格式返回此值。
     * The date and time when the fulfillment was created. The API returns this value in ISO 8601 format.
     */
    @Alias(JsonConstants.CREATED_AT)
    private ZonedDateTime createdAt;

    private Long id;

    /**
     * A historical record of each item in the fulfillment:
     * 履行中每个项目的历史记录：
     */
    @Alias(JsonConstants.LINE_ITEMS)
    private List<LineItem> lineItems;

    /**
     * 履行应针对的位置的唯一标识符。要查找位置的ID，请使用位置资源。
     * The unique identifier of the location that the fulfillment should be processed for. To find the ID of the
     * location, use the Location resource.
     */
    @Alias(JsonConstants.LOCATION_ID)
    private Long locationId;

    /**
     * The uniquely identifying fulfillment name, consisting of two parts separated by a .. The first part represents
     * the order name and the second part represents the fulfillment number. The fulfillment number automatically
     * increments depending on how many fulfillments are in an order (e.g. #1001.1, #1001.2).
     * 唯一标识的履行名称，由两部分组成，并用分隔.。第一部分代表订单名称，第二部分代表履行编号。自动履行数目的增量取决于有多少应验在命令（例如#1001.1，#1001.2）。
     */
    private String name;

    /**
     * 是否应通知客户。如果设置为true，则在创建或更新实现时将发送一封电子邮件。对于最初使用API​​创建的订单，默认值为false。对于所有其他订单，默认值为true。
     * <p>
     * Whether the customer should be notified. If set to true, then an email will be sent when the fulfillment is
     * created or updated. For orders that were initially created using the API, the default value is false. For all
     * other orders, the default value is true.
     */
    @Alias(JsonConstants.NOTIFY_CUSTOMER)
    private boolean notifyCustomer;

    /**
     * The unique numeric identifier for the order.
     * 订单的唯一数字标识符。
     */
    private Long orderId;

    /**
     * 提供有关收据信息的文本字段：
     * <p>
     * 测试用例：实现是否为测试用例。
     * 授权：授权码。
     * A text field that provides information about the receipt:
     * <p>
     * testcase: Whether the fulfillment was a testcase.
     * authorization: The authorization code.
     */
    private Receipt receipt;

    /**
     * 履行的当前发货状态
     * The type of service used.
     */
    private String service;

    /**
     * 履行的当前发货状态
     * The current shipment status of the fulfillment
     */
    @Alias(JsonConstants.SHIPMENT_STATUS)
    private ShipmentStatus shipmentStatus;

    /**
     * 履行的状态
     * The status of the fulfillment
     */
    private FulfillmentStatus status;

    /**
     * 跟踪公司的名称。
     * The name of the tracking company.
     * <p>
     * 4PX
     * APC
     * Amazon Logistics UK
     * Amazon Logistics US
     * Anjun Logistics
     * Australia Post
     * Bluedart
     * Canada Post
     * Canpar
     * China Post
     * Chukou1
     * Correios
     * Couriers Please
     * DHL Express
     * DHL eCommerce
     * DHL eCommerce Asia
     * DPD
     * DPD Local
     * DPD UK
     * Delhivery
     * Eagle
     * FSC
     * Fastway Australia
     * FedEx
     * GLS
     * GLS (US)
     * Globegistics
     * Japan Post (EN)
     * Japan Post (JA)
     * La Poste
     * New Zealand Post
     * Newgistics
     * PostNL
     * PostNord
     * Purolator
     * Royal Mail
     * SF Express
     * SFC Fulfillment
     * Sagawa (EN)
     * Sagawa (JA)
     * Sendle
     * Singapore Post
     * StarTrack
     * TNT
     * Toll IPEC
     * UPS
     * USPS
     * Whistl
     * Yamato (EN)
     * Yamato (JA)
     * YunExpress
     */
    @Alias(JsonConstants.TRACKING_COMPANY)
    private String trackingCompany;

    /**
     * A list of tracking numbers, provided by the shipping company.
     * 货运公司提供的跟踪编号列表。
     */
    @Alias(JsonConstants.TRACKING_NUMBERS)
    private List<String> trackingNumbers;

    /**
     * The URLs of tracking pages for the fulfillment.
     * 用于实现的跟踪页面的URL。
     */
    @Alias(JsonConstants.TRACKING_URLS)
    private List<String> trackingUrls;

    /**
     * The name of the inventory management service.
     * 库存管理服务的名称。
     */
    @Alias(JsonConstants.VARIANT_INVENTORY_MANAGEMENT)
    private String variantInventoryManagement;
}
