package com.zsmall.extend.shop.shopify.model.fulfillment;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 商家针对此履行订单向履行服务发送的请求的列表。
 *
 * @date 2022-12-26 16:42
 */
@Data
@Accessors(chain = true)
public class MerchantRequests {

    /**
     * 商家返回的消息（如果有）。
     * The message returned by the merchant, if any.
     */
    private String message;

    /**
     * 要求的种类。有效值：履行要求，取消要求或legacy_fulfill_request。
     * The kind of request. Valid values: fulfillment_request, cancellation_request, or legacy_fulfill_request.
     */
    private String kind;

    /**
     * 商家返回的请求选项（如果有）。
     * The request options returned by the merchant, if any.
     */
    @Alias(JsonConstants.REQUEST_OPTIONS)
    private MerchantRequestOption requestOption;

}
