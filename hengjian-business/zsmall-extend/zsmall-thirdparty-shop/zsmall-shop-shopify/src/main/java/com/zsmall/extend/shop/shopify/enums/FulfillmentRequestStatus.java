package com.zsmall.extend.shop.shopify.enums;

/**
 * 履行订单的请求状态
 */
public enum FulfillmentRequestStatus {

    /**
     * The initial state for newly created fulfillment orders. This is the only valid state for fulfillment orders not
     * assigned to a fulfillment service.
     */
    unsubmitted,
    /**
     * The merchant requested fulfillment for this fulfillment order.
     */
    submitted,
    /**
     * The fulfillment service accepted the merchant's fulfillment request.
     */
    accepted,
    /**
     * The fulfillment service rejected the merchant's fulfillment request.
     */
    rejected,
    /**
     * The merchant requested a cancellation of the fulfillment request for this fulfillment order.
     */
    cancellation_requested,
    /**
     * The fulfillment service accepted the merchant's fulfillment cancellation request.
     */
    cancellation_accepted,
    /**
     * The fulfillment service rejected the merchant's fulfillment cancellation request.
     */
    cancellation_rejected,
    /**
     * The fulfillment service closed the fulfillment order without completing it.
     */
    closed,

    ;

}
