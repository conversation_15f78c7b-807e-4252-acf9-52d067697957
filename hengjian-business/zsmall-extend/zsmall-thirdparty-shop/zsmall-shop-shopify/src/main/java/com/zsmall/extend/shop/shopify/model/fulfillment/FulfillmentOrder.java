package com.zsmall.extend.shop.shopify.model.fulfillment;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import com.zsmall.extend.shop.shopify.enums.FulfillmentRequestStatus;
import com.zsmall.extend.shop.shopify.enums.OrderStatus;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 履行订单
 *
 * @date 2022-12-26 15:39
 */
@Data
@Accessors(chain = true)
public class FulfillmentOrder {

    /**
     * An ID for the fulfillment order.
     * 履行订单的ID。
     */
    private Long id;
    /**
     * The ID of the location that has been assigned to do the work.
     * 已分配用于执行工作的位置的ID。
     */
    @Alias(JsonConstants.ASSIGNED_LOCATION_ID)
    private Long assignedLocationId;

    /**
     * 目的地
     */
    private Destination destination;

    /**
     * Represents line items belonging to a fulfillment order:
     * 履行订单的订单项
     */
    @Alias(JsonConstants.LINE_ITEMS)
    private List<LineItem> lineItems;

    /**
     * 与履行订单相关联的订单的ID。
     * The ID of the order that's associated with the fulfillment order.
     */
    @Alias(JsonConstants.ORDER_ID)
    private Long order_id;

    /**
     * 履行订单的请求状态
     * The request status of the fulfillment order. Valid values:
     * unsubmitted: The initial state for newly created fulfillment orders. This is the only valid state for
     * fulfillment orders not assigned to a fulfillment service.
     * submitted: The merchant requested fulfillment for this fulfillment order.
     * accepted: The fulfillment service accepted the merchant's fulfillment request.
     * rejected: The fulfillment service rejected the merchant's fulfillment request.
     * cancellation_requested: The merchant requested a cancellation of the fulfillment request for this fulfillment
     * order.
     * cancellation_accepted: The fulfillment service accepted the merchant's fulfillment cancellation request.
     * cancellation_rejected: The fulfillment service rejected the merchant's fulfillment cancellation request.
     * closed: The fulfillment service closed the fulfillment order without completing it.
     */
    @Alias(JsonConstants.REQUEST_STATUS)
    private FulfillmentRequestStatus requestStatus;

    /**
     * The ID of the shop that's associated with the fulfillment order.
     * 与履行订单关联的商店的ID。
     */
    @Alias(JsonConstants.SHOP_ID)
    private Long shopId;


    /**
     * 履行订单的状态。
     * The status of the fulfillment order. Valid values:
     * open: Default state for newly created fulfillment orders.
     * in_progress: The fulfillment order is being processed.
     * cancelled: The fulfillment order has been cancelled by the merchant.
     * incomplete: The fulfillment order cannot be completed as requested.
     * closed: The fulfillment order has been completed and closed.
     */
    private OrderStatus status;

    /**
     * 可以在此履行订单上执行的动作。
     * The actions that can be performed on this fulfillment order.
     */
    @Alias(JsonConstants.SUPPORTED_ACTIONS)
    private List<String> supportActions;

    /**
     * 商家针对此履行订单向履行服务发送的请求的列表。
     * A list of requests sent by the merchant to the fulfillment service for this fulfillment order.
     */
    @Alias(JsonConstants.MERCHANT_REQUESTS)
    private List<MerchantRequests> merchant_requests;

    /**
     * 履行订单的分配位置。这是期望实现的位置。
     * The fulfillment order's assigned location. This is the location expected to perform fulfillment.
     */
    @Alias(JsonConstants.ASSIGNED_LOCATION)
    private AssignedLocation assignedLocation;

}
