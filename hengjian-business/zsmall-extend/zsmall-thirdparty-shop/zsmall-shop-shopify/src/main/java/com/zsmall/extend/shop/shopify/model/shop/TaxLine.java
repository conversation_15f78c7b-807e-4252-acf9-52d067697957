package com.zsmall.extend.shop.shopify.model.shop;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @date 2022-12-27 1:12
 */
@Data
@Accessors(chain = true)
public class TaxLine {

    /**
     * The name of the tax.
     */
    private String title;

    /**
     * The amount of tax to be charged in the shop currency.
     */
    private String price;

    /**
     * The rate of tax to be applied.
     */
    private double rate;

    @Alias(JsonConstants.PRICE_SET)
    private PriceSet priceSet;

}
