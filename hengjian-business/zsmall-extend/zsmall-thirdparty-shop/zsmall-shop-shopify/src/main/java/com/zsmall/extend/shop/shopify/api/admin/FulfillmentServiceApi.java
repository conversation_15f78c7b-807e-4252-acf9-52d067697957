package com.zsmall.extend.shop.shopify.api.admin;

import cn.hutool.core.lang.Assert;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import com.zsmall.extend.shop.shopify.api.model.common.OutCommonBean;
import com.zsmall.extend.shop.shopify.api.model.fulfillment.InFulfillmentQuery;
import com.zsmall.extend.shop.shopify.api.model.fulfillment.InFulfillmentServiceCreate;
import com.zsmall.extend.shop.shopify.api.model.fulfillment.InFulfillmentUpdate;
import com.zsmall.extend.shop.shopify.enums.ShopifyErrorEnums;
import com.zsmall.extend.shop.shopify.exception.ShopifyClientException;
import com.zsmall.extend.shop.shopify.model.ShopifyClientBean;
import com.zsmall.extend.shop.shopify.model.fulfillment.FulfillmentService;
import com.zsmall.extend.shop.shopify.model.fulfillment.FulfillmentServiceList;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 履行服务接口
 * 在商店注册履行服务。然后，商家可以将其设置为产品的履行服务，您的服务可以使用Fulfillment和FulfillmentEvent API管理这些产品的履行
 * Register a fulfillment service with a store. The merchant can then set it as the fulfillment service for their
 * products, and your service can manage the fulfillment of those products using the Fulfillment and FulfillmentEvent
 * APIs.
 */
@Slf4j
public class FulfillmentServiceApi extends Component {

    /**
     * GET - 接收所有FulfillmentServices的列表
     * Receive a list of all FulfillmentServices
     * POST - Create a new FulfillmentService
     * 创建一个新的FulfillmentService
     */
    private static final String URL_SERVICES = "/admin/api/{version}/fulfillment_services.json";

    /**
     * GET - 收到一个FulfillmentService
     * Receive a single FulfillmentService
     * PUT - Modify an existing FulfillmentService
     * 修改现有的FulfillmentService
     * DELETE - Remove an existing FulfillmentService
     * 删除现有的FulfillmentService
     */
    private static final String URL_SERVICES_ID =
        "/admin/api/{version}/fulfillment_services/{fulfillment_service_id}.json";


    public FulfillmentServiceApi(ShopifyClientBean shopifyClientBean, String shopDomain) {
        super(shopifyClientBean, shopDomain);
    }

    /**
     * 接收所有FulfillmentServices的列表。
     *
     * @param fulfillmentQuery
     * @return
     */
    public List<FulfillmentService> getFulfillmentServices(InFulfillmentQuery fulfillmentQuery) {
        Map<String, Object> paramMap = this.getStringObjectMap(fulfillmentQuery);
        log.info("FulfillmentServiceApi - getFulfillmentServices url = {}, paramMap = {}", URL_SERVICES,
            JSONUtil.toJsonStr(paramMap));

        FulfillmentServiceList fulfillmentServiceList =
            this.httpResponseWithAccessToken(getRequestUrl(URL_SERVICES), Method.GET, paramMap, FulfillmentServiceList.class);
        if (fulfillmentServiceList == null) {
            throw new ShopifyClientException(ShopifyErrorEnums.RESPONSE_CONTENT_EMPTY);
        }
        return fulfillmentServiceList.getFulfillmentServices();
    }


    /**
     * 创建履行服务
     *
     * @param fulfillmentCreate
     * @return
     */
    public FulfillmentService createFulfillmentService(InFulfillmentServiceCreate fulfillmentCreate) {
        checkCreateFulfillmentService(fulfillmentCreate);
        try {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("fulfillment_service", fulfillmentCreate);

            log.info("FulfillmentServiceApi - createFulfillmentService url = {}, paramMap = {}", URL_SERVICES,
                JSONUtil.toJsonStr(paramMap));
            OutCommonBean outCommonBean =
                this.httpResponseWithAccessToken(getRequestUrl(URL_SERVICES), Method.POST, paramMap, OutCommonBean.class);
            if (outCommonBean == null) {
                throw new ShopifyClientException(ShopifyErrorEnums.RESPONSE_CONTENT_EMPTY);
            }
            return outCommonBean.getFulfillmentService();
        } catch (final Exception e) {
            throw new ShopifyClientException(e.getMessage(), e);
        }
    }

    private void checkCreateFulfillmentService(InFulfillmentServiceCreate fulfillmentCreate) {
        if (fulfillmentCreate == null) {
            throw new ShopifyClientException(ShopifyErrorEnums.PARAMETER_CAN_NOT_BE_EMPTY);
        }
        Assert.notBlank(fulfillmentCreate.getName(), "[Assertion failed] - {} it must not be null or blank", "name");
    }

    /**
     * 通过ID获得单一的履行服务。
     *
     * @param fulfillmentId
     * @return
     */
    public FulfillmentService getFulfillmentService(Long fulfillmentId) {
        Assert.notNull(fulfillmentId, "[Assertion failed] - {} it must not be null. ", "fulfillment_service_id");
        String url = this.replaceUrlValue(getRequestUrl(URL_SERVICES_ID), "fulfillment_service_id", fulfillmentId);
        log.info("FulfillmentServiceApi - getFulfillmentService url = {}", url);

        OutCommonBean outCommonBean = this.httpResponseWithAccessToken(url, Method.GET, null, OutCommonBean.class);
        if (outCommonBean == null) {
            throw new ShopifyClientException(ShopifyErrorEnums.RESPONSE_CONTENT_EMPTY);
        }
        return outCommonBean.getFulfillmentService();
    }


    /**
     * 更新履行服务
     *
     * @param fulfillmentUpdate
     * @return
     */
    public FulfillmentService updateFulfillmentService(InFulfillmentUpdate fulfillmentUpdate) {
        if (fulfillmentUpdate == null) {
            throw new ShopifyClientException(ShopifyErrorEnums.PARAMETER_CAN_NOT_BE_EMPTY);
        }
        Long id = fulfillmentUpdate.getId();
        Assert.notNull(id, "[Assertion failed] - {} it must not be null. ", "fulfillment_service_id");
        try {
            String url = this.replaceUrlValue(getRequestUrl(URL_SERVICES_ID), "fulfillment_service_id", id);

            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("fulfillment_service", fulfillmentUpdate);

            log.info("FulfillmentServiceApi - createFulfillmentService url = {}, paramMap = {}", url,
                JSONUtil.toJsonStr(paramMap));
            OutCommonBean outCommonBean =
                this.httpResponseWithAccessToken(url, Method.PUT, paramMap, OutCommonBean.class);
            if (outCommonBean == null) {
                throw new ShopifyClientException(ShopifyErrorEnums.RESPONSE_CONTENT_EMPTY);
            }
            return outCommonBean.getFulfillmentService();
        } catch (final Exception e) {
            throw new ShopifyClientException(e.getMessage(), e);
        }
    }


    /**
     * 删除单个履行服务
     *
     * @param fulfillmentId
     * @return
     */
    public void deleteFulfillmentService(Long fulfillmentId) {
        Assert.notNull(fulfillmentId, "[Assertion failed] - {} it must not be null", "fulfillmentId");

        String url = this.replaceUrlValue(getRequestUrl(URL_SERVICES_ID), "fulfillment_service_id", fulfillmentId);
        log.info("FulfillmentServiceApi - deleteFulfillmentService url = {}", url);

        OutCommonBean outCommonBean = this.httpResponseWithAccessToken(url, Method.DELETE, null, OutCommonBean.class);
        if (outCommonBean == null) {
            throw new ShopifyClientException(ShopifyErrorEnums.RESPONSE_CONTENT_EMPTY);
        }
    }

}
