package com.zsmall.extend.shop.shopify.enums;

/**
 * 履行的状态。有效值：
 * <p>
 * 待处理：正在履行。
 * open：服务已确认履行，正在处理中。
 * 成功：成功实现。
 * 已取消：履行已取消。
 * 错误：履行请求存在错误。
 * 失败：履行请求失败。
 * <p>
 * The status of the fulfillment. Valid values:
 * <p>
 * pending: The fulfillment is pending.
 * open: The fulfillment has been acknowledged by the service and is in processing.
 * success: The fulfillment was successful.
 * cancelled: The fulfillment was cancelled.
 * error: There was an error with the fulfillment request.
 * failure: The fulfillment request failed.
 */
public enum FulfillmentStatus {

    pending, open, success, cancelled, error, failure,

    ;

}
