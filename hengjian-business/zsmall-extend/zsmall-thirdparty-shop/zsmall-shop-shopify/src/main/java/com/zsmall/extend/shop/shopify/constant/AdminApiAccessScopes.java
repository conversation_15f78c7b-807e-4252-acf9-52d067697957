package com.zsmall.extend.shop.shopify.constant;

/**
 * Admin API Authenticated access scopes
 * Authenticated access scopes control access to resources in the REST Admin API and the GraphQL Admin API.
 * Authenticated access is intended for interacting with a store on behalf of the merchant to perform actions such as
 * creating products and managing discount codes.
 */
public class AdminApiAccessScopes {

    /**
     * read_content, write_content
     * Access to Article, Blog, Comment, Page, and Redirect.
     */
    public static final class Content {
        public static final String READ = "read_content";
        public static final String WRITE = "write_content";
    }


    /**
     * read_themes, write_themes
     * Access to Asset and Theme.
     */
    public static final class Themes {
        public static final String READ = "read_themes";
        public static final String WRITE = "write_themes";
    }


    /**
     * read_products, write_products
     * Access to Product, Product Variant, Product Image, Collect, Custom Collection, and Smart Collection.
     */
    public static final class Product {
        public static final String READ = "read_products";
        public static final String WRITE = "write_products";
    }


    /**
     * read_product_listings
     * Access to Product Listing, and java.util.Collection Listing.
     */
    public static final class ProductListing {
        public static final String READ = "read_product_listings";
    }


    /**
     * read_customers, write_customers
     * Access to Customer and Saved Search.
     */
    public static final class Customer {
        public static final String READ = "read_customers";
        public static final String WRITE = "write_customers";
    }


    /**
     * read_orders, write_orders
     * Access to Abandoned checkouts, Fulfillment, Order, and Transaction.
     */
    public static final class Order {
        public static final String READ = "read_orders";
        public static final String WRITE = "write_orders";
    }


    /**
     * read_all_orders
     * Grants access to all orders rather than the default window of 60 days worth of orders. This OAuth scope is used
     * in conjunction with read_orders, or write_orders. You need to request this scope from your Partner Dashboard
     * before adding it to your app.
     */
    public static final class AllOrder {
        public static final String READ = "read_all_orders";
    }


    /**
     * read_draft_orders, write_draft_orders
     * Access to Draft Order.
     */
    public static final class DraftOrder {
        public static final String READ = "read_draft_orders";
        public static final String WRITE = "write_draft_orders";
    }


    /**
     * read_inventory, write_inventory
     * Access to Inventory Level and Inventory Item.
     */
    public static final class Inventory {
        public static final String READ = "read_inventory";
        public static final String WRITE = "write_inventory";
    }


    /**
     * read_locations
     * Access to Location.
     */
    public static final class Location {
        public static final String READ = "read_locations";
    }


    /**
     * read_script_tags, write_script_tags
     * Access to Script Tag.
     */
    public static final class ScriptTag {
        public static final String READ = "read_script_tags";
        public static final String WRITE = "write_script_tags";
    }


    /**
     * read_fulfillments, write_fulfillments
     * Access to Fulfillment Service.
     */
    public static final class Fulfillment {
        public static final String READ = "read_fulfillments";
        public static final String WRITE = "write_fulfillments";
    }


    /**
     * read_assigned_fulfillment_orders, write_assigned_fulfillment_orders
     * Access to FulfillmentOrder resources assigned to a location managed by your fulfillment service.
     */
    public static final class FulfillmentOrder {
        public static final String READ = "read_assigned_fulfillment_orders";
        public static final String WRITE = "write_assigned_fulfillment_orders";
    }


    /**
     * read_merchant_managed_fulfillment_orders, write_merchant_managed_fulfillment_orders
     * Access to FulfillmentOrder resources assigned to merchant-managed locations.
     */
    public static final class MerchantFulfillmentOrder {
        public static final String READ = "read_merchant_managed_fulfillment_orders";
        public static final String WRITE = "write_merchant_managed_fulfillment_orders";
    }


    /**
     * read_third_party_fulfillment_orders, write_third_party_fulfillment_orders
     * Access to FulfillmentOrder resources assigned to a location managed by any fulfillment service.
     */
    public static final class ThirdPartyFulfillmentOrder {
        public static final String READ = "read_third_party_fulfillment_orders";
        public static final String WRITE = "write_third_party_fulfillment_orders";
    }


    /**
     * read_shipping, write_shipping
     * Access to Carrier Service, Country and Province.
     */
    public static final class Shipping {
        public static final String READ = "read_shipping";
        public static final String WRITE = "write_shipping";
    }


    /**
     * read_analytics
     * Access to Analytics API.
     */
    public static final class Analytics {
        public static final String READ = "read_analytics";
    }


    /**
     * read_users, write_users
     * Access to User SHOPIFY PLUS.
     */
    public static final class User {
        public static final String READ = "read_users";
        public static final String WRITE = "write_users";
    }


    /**
     * read_checkouts, write_checkouts
     * Access to Checkouts.
     */
    public static final class Checkouts {
        public static final String READ = "read_checkouts";
        public static final String WRITE = "write_checkouts";
    }


    /**
     * read_reports, write_reports
     * Access to Reports.
     */
    public static final class Reports {
        public static final String READ = "read_reports";
        public static final String WRITE = "write_reports";
    }


    /**
     * read_price_rules, write_price_rules
     * Access to Price Rules.
     */
    public static final class PriceRules {
        public static final String READ = "read_price_rules";
        public static final String WRITE = "write_price_rules";
    }


    /**
     * read_discounts, write_discounts
     * Access to GraphQL Admin API Discounts features.
     */
    public static final class Discounts {
        public static final String READ = "read_discounts";
        public static final String WRITE = "write_discounts";
    }


    /**
     * read_marketing_events, write_marketing_events
     * Access to Marketing Event.
     */
    public static final class Marketing {
        public static final String READ = "read_marketing_events";
        public static final String WRITE = "write_marketing_events";
    }


    /**
     * read_resource_feedbacks, write_resource_feedbacks
     * Access to ResourceFeedback.
     */
    public static final class ResourceFeedback {
        public static final String READ = "read_resource_feedbacks";
        public static final String WRITE = "write_resource_feedbacks";
    }


    /**
     * read_shopify_payments_payouts
     * Access to the Shopify Payments Payout, Balance, and Transaction resources.
     */
    public static final class PaymentsPayout {
        public static final String READ = "read_shopify_payments_payouts";
    }


    /**
     * read_shopify_payments_disputes
     * Access to the Shopify Payments Dispute resource.
     */
    public static final class PaymentsDispute {
        public static final String READ = "read_shopify_payments_disputes";
    }


    /**
     * read_translations, write_translations
     * Access to Translatable Resource.
     */
    public static final class Translatable {
        public static final String READ = "read_translations";
        public static final String WRITE = "write_translations";
    }


    /**
     * read_locales, write_locales
     * Access to Shop Locale.
     */
    public static final class Locale {
        public static final String READ = "read_locales";
        public static final String WRITE = "write_locales";
    }
}
