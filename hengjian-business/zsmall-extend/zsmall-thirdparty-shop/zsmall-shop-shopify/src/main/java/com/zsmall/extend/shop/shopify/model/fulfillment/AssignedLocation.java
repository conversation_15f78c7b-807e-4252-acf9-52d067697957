package com.zsmall.extend.shop.shopify.model.fulfillment;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 履行订单的指定位置
 *
 * @date 2022-12-26 17:03
 */
@Data
@Accessors(chain = true)
public class AssignedLocation {

    /**
     * 指定位置的街道地址。
     */
    private String address1;

    /**
     * 可选的附加字段，用于指定位置的街道地址。
     */
    private String address2;

    /**
     * 目的地城市。
     */
    private String city;

    /**
     * 指定位置的国家/地区的两个字母的代码。
     */
    @Alias(JsonConstants.COUNTRY_CODE)
    private String countryCode;

    /**
     * 目标客户的电子邮件。
     */
    private String email;

    /**
     * 指定位置的ID。
     */
    @Alias(JsonConstants.LOCATION_ID)
    private Long locationId;

    /**
     * 指定位置的名称。
     */
    private String name;

    /**
     * 指定位置的电话号码。
     */
    private String phone;

    /**
     * 指定位置的省。
     */
    private String province;

    /**
     * 指定位置的邮政编码。
     */
    private String zip;

}
