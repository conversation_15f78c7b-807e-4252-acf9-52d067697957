package com.zsmall.extend.shop.shopify.enums;

/**
 * active: The product is ready to sell and is available to customers on the online store, sales channels, and apps.
 * By default, existing products are set to active.
 * archived: The product is no longer being sold and isn't available to customers on sales channels and apps.
 * draft: The product isn't ready to sell and is unavailable to customers on sales channels and apps. By default,
 * duplicated and unarchived products are set to draft.
 * Product statuses aren't currently available to stores on the Shopify Plus plan.
 */
public enum ProductStatusEnum {

    /**
     * The product is ready to sell and is available to customers on the online store, sales channels, and apps.
     * By default, existing products are set to active
     */
    active,

    /**
     * The product is no longer being sold and isn't available to customers on sales channels and apps.
     */
    archived,

    /**
     * The product isn't ready to sell and is unavailable to customers on sales channels and apps. By default,
     * duplicated and unarchived products are set to draft.
     */
    draft,

    ;

}
