package com.zsmall.extend.shop.shopify.model.user;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.util.List;

/**
 * Shopify用户实体
 */
@Data
public class User {

    /**
     * Whether the user is the owner of the Shopify account.
     */
    @<PERSON><PERSON>("account_owner")
    private boolean accountOwner;

    /**
     * The description the user has written for themselves.
     */
    private String bio;

    /**
     * The user's email address.
     */
    private String email;

    /**
     * The user's first name.
     */
    @<PERSON>as("first_name")
    private String firstName;

    /**
     * The ID of the user's staff.
     */
    private long id;

    /**
     * The user's IM account address.
     */
    private String im;

    /**
     * The user's last name.
     */
    @<PERSON>as("last_name")
    private String lastName;

    /**
     * The user's phone number.
     */
    private String phone;

    /**
     * Whether this account will receive email announcements from Shopify. Valid values: 0, 1
     */
    @<PERSON>as("receive_announcements")
    private int receiveAnnouncements;

    /**
     * The user's homepage or other web address.
     */
    private String url;

    /**
     * The user's preferred locale. Locale values use the format language or language-COUNTRY,
     * where language is a two-letter language code, and COUNTRY is a two-letter country code.
     * For example: en or en-US
     */
    private String locale;

    /**
     * The type of account the user has. Valid values:
     * <p>
     * regular: The user's account can access the Shopify admin.
     * restricted: The user's account cannot access the Shopify admin.
     * invited: The user has not yet accepted the invitation to create staff.
     * collaborator: The user account of a partner who collaborates with the merchant.
     */
    @Alias("userType")
    private String user_type;

    /**
     * The permissions granted to the user's staff account. Valid values:
     * <p>
     * applications: The user can authorize the installation of applications.
     * billing_application_charges: The user can approve application charges.
     * billing_charges: The user can view and export billing charges.
     * billing_invoices_view: The user can view billing invoices.
     * billing_payment_methods_view: The user can view billing payment methods.
     * customers: The user can view, create, edit, and delete customers, and respond to customer messages in Shopify Ping.
     * dashboard: The user can view the Home page, which includes sales information and other store data.
     * domains: The user can view, buy, and manage domains.
     * draft_orders: The user can create, update, and delete draft orders.
     * edit_orders: The user can edit orders.
     * edit_private_apps: The user can give permission to private apps to read, write, and make changes to the store.
     * export_customers: The user can export customers.
     * export_draft_orders: The user can export draft orders.
     * export_products: The user can export products and inventory.
     * export_orders: The user can export orders.
     * gift_cards: The user can view, create, issue, and export gift cards to a CSV file.
     * links: The user can view and modify links and navigation menus.
     * locations: The user can create, update, and delete locations where you stock or manage inventory.
     * marketing: The user can view and create discount codes and automatic discounts, and export discounts to a CSV file.
     * marketing_section: The user can view, create, and automate marketing campaigns.
     * orders: The user can view, create, update, delete, and cancel orders, and receive order notifications.
     * overviews: The user can view the Overview and Live view pages, which include sales information, and other store
     * and sales channels data.
     * pages: The user can view, create, update, publish, and delete blog posts and pages.
     * preferences: The user can view the preferences and configuration of a shop.
     * products: The user can view, create, import, and update products, collections, and inventory.
     * reports: The user can view and create all reports, which includes sales information and other store data.
     * shopify_payments_accounts: The user can view Shopify Payments account details.
     * shopify_payments_transfers: The user can view Shopify Payments payouts.
     * staff_audit_log_view: The user can view Shopify admin browser sessions.
     * staff_management_activation: The user can activate or deactivate staff in the store.
     * staff_management_create: The user can add staff to the store.
     * staff_management_delete: The user can delete staff from the store.
     * staff_management_update: The user can update staff in the store.
     * themes: The user can view, update, and publish themes.
     * view_private_apps: The user can view private apps installed on the store.
     */
    private List<String> permissions;

}
