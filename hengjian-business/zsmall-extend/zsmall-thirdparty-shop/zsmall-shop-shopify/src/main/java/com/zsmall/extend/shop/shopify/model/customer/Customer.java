package com.zsmall.extend.shop.shopify.model.customer;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import com.zsmall.extend.shop.shopify.enums.CustomerState;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * 用户信息（注意：缺少部分信息）
 *
 * @date 2022-12-28 11:39
 */
@Data
@Accessors(chain = true)
public class Customer {

    private Long id;

    private String email;

    @<PERSON>as(JsonConstants.ACCEPTS_MARKETING)
    private boolean accepts_marketing;

    @<PERSON>as(JsonConstants.CREATED_AT)
    private ZonedDateTime created_at;

    @Alias(JsonConstants.UPDATED_AT)
    private ZonedDateTime updatedAt;

    @Alias(JsonConstants.FIRST_NAME)
    private String firstName;

    @Alias(JsonConstants.LAST_NAME)
    private String lastName;

    @Alias(JsonConstants.ORDERS_COUNT)
    private String ordersCount;

    private CustomerState state;

    /**
     *
     */
    @Alias(JsonConstants.TOTAL_SPENT)
    private Double totalSpent;

    /**
     *
     */
    @Alias(JsonConstants.LAST_ORDER_ID)
    private Long lastOrderId;

    private String note;

    /**
     *
     */
    @Alias(JsonConstants.VERIFIED_EMAIL)
    private boolean verifiedEmail;

    /**
     *
     */
    @Alias(JsonConstants.MULTIPASS_IDENTIFIER)
    private String multipassIdentifier;

    /**
     *
     */
    @Alias(JsonConstants.TAX_EXEMPT)
    private boolean taxExempt;

    /**
     *
     */
    @Alias(JsonConstants.TAX_EXEMPTIONS)
    private List<String> taxExemptions;

    private String phone;

    private String tags;

    /**
     *
     */
    @Alias(JsonConstants.LAST_ORDER_NAME)
    private String lastOrderName;

    private String currency;

//  private Addresses addresses;

//  private String admin_graphql_api_id;

//  private Default_address default_address;

}
