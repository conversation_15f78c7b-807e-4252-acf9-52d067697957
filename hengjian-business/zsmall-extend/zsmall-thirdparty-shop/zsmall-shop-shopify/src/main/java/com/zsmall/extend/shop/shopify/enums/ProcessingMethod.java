package com.zsmall.extend.shop.shopify.enums;

/**
 * How the payment was processed. It has the following valid values:
 * <p>
 * checkout: The order was processed using the Shopify checkout.
 * direct: The order was processed using a direct payment provider.
 * manual: The order was processed using a manual payment method.
 * offsite: The order was processed by an external payment provider to the Shopify checkout.
 * express: The order was processed using PayPal Express Checkout.
 * free: The order was processed as a free order using a discount code.
 * <p>
 * 付款处理方式。它具有以下有效值：
 * <p>
 * checkout：已使用Shopify结帐处理订单。
 * direct：订单是使用直接付款提供商处理的。
 * manual：使用手动付款方式处理订单。
 * offsite：订单已由外部付款提供商处理到Shopify结帐处。
 * express：使用PayPal Express Checkout处理订单。
 * free：使用折扣代码将订单作为免费订单处理。
 */
public enum ProcessingMethod {

    checkout, direct, manual, offsite, express, free;
}
