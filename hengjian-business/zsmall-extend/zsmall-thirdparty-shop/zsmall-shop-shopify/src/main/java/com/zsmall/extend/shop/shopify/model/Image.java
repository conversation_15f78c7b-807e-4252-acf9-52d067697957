package com.zsmall.extend.shop.shopify.model;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.LinkedList;
import java.util.List;

@Data
public class Image {

    private Long id;

    /**
     * 指定图像位置的源URL。
     * The source URL that specifies the location of the image.
     */
    @Alias(value = JsonConstants.SRC)
    private String src;

    /**
     *
     */
    @Alias(value = JsonConstants.POSITION)
    private Integer position;

    /**
     * 附加到自定义集合的图像，该图像以Base64编码的二进制数据形式返回。
     * An image attached to a custom collection returned as Base64-encoded binary data.
     */
    @Alias(value = JsonConstants.ATTACHMENT)
    private String attachment;

    /**
     * 描述集合图像的替代文本。
     * Alternative text that describes the collection image.
     */
    @Alias(value = JsonConstants.ALT)
    private String alt;

    /**
     * 将图像添加到集合中的时间和日期
     * The time and date (ISO 8601 format) when the image was added to the collection.
     */
    @Alias(value = JsonConstants.CREATED_AT)
//  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT-05:00")
    private ZonedDateTime createdAt;

    /**
     * 图像的宽度，以像素为单位。
     * The width of the image in pixels.
     */
    @Alias(value = JsonConstants.WIDTH)
    private Integer width;

    /**
     * 图像的高度，以像素为单位。
     * The height of the image in pixels.
     */
    @Alias(value = JsonConstants.HEIGHT)
    private Integer height;

    private List<Metafield> metafields = new LinkedList<>();

}
