package com.zsmall.extend.shop.shopify.model.fulfillment;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 目的地
 *
 * @date 2022-12-26 15:42
 */
@Data
@Accessors(chain = true)
public class Destination {

    /**
     * 履行订单目的地的ID。
     */
    private Long id;

    /**
     * 指定位置的街道地址。
     */
    private String address1;

    /**
     * 可选的附加字段，用于分配位置的街道地址。
     */
    private String address2;

    /**
     * 目的地城市。
     */
    private String city;

    /**
     * 目的地公司。
     */
    private String company;

    /**
     * 目的地国家。
     */
    private String country;

    /**
     * 目标客户的电子邮件。
     */
    private String email;

    /**
     * 目的地的客户的名字。
     */
    @Alias(JsonConstants.FIRST_NAME)
    private String firstName;

    /**
     * 目的地客户的姓氏。
     */
    @Alias(JsonConstants.LAST_NAME)
    private String lastName;

    /**
     * 目的地客户的电话号码。
     */
    private String phone;

    /**
     * 目的地的省。
     */
    private String province;

    /**
     * 目的地的邮政编码。
     */
    private String zip;

}
