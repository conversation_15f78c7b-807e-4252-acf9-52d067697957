package com.zsmall.extend.shop.shopify.model.shop;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;

import java.util.List;

/**
 *
 */
public class Duty {

    private String id;

    @<PERSON>as(JsonConstants.HARMONIZED_SYSTEM_CODE)
    private String harmonizedSystemCode;

    @<PERSON>as(JsonConstants.COUNTRY_CODE_OF_ORIGIN)
    private String countryCodeOfOrigin;

    @<PERSON><PERSON>(JsonConstants.SHOP_MONEY)
    private ShopMoney shopMoney;

    @<PERSON><PERSON>(JsonConstants.PRESENTMENT_MONEY)
    private ShopMoney presentmentMoney;

    @<PERSON>as(JsonConstants.TAX_LINES)
    private List<TaxLine> taxLines;


//  private String admin_graphql_api_id;


}
