package com.zsmall.extend.shop.shopify.model.order.refund;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import com.zsmall.extend.shop.shopify.enums.RefundType;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @date 2022-12-28 18:22
 */
@Data
@Accessors(chain = true)
public class RefundDuty {

    /**
     * The unique identifier of the duty.
     * 职责的唯一标识符。
     */
    @Alias(JsonConstants.DUTY_ID)
    private Long dutyId;

    /**
     * Specifies how you want the duty refunded.
     * 指定您如何退税。
     */
    @Alias(JsonConstants.REFUND_TYPE)
    private RefundType refundType;

}
