package com.zsmall.extend.shop.shopify.model.order.refund;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import com.zsmall.extend.shop.shopify.model.fulfillment.LineItem;
import com.zsmall.extend.shop.shopify.model.shop.PriceSet;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 退款的订单项列表
 * A list of refunded line items
 *
 * @date 2022-12-28 18:29
 */
@Data
@Accessors(chain = true)
public class RefundLineItem {

    /**
     * The unique identifier of the line item in the refund.
     */
    private Long id;

    /**
     * A line item being returned.
     */
    @Alias(JsonConstants.LINE_ITEM)
    private LineItem lineItem;

    /**
     * The ID of the related line item in the order.
     */
    @Alias(JsonConstants.LINE_ITEM_ID)
    private Long lineItemId;

    /**
     * The quantity of the associated line item that was returned.
     */
    private int quantity;

    /**
     * The unique identifier of the location where the items will be restocked. Required when restock_type has the value return or cancel.
     */
    @Alias(JsonConstants.LOCATION_ID)
    private Long location_id;

    /**
     * The subtotal of the refund line item in shop and presentment currencies.
     */
    @Alias(JsonConstants.RESTOCK_TYPE)
    private String restockType;

    /**
     * The subtotal of the refund line item.
     */
    private double subtotal;

    /**
     * The total tax on the refund line item.
     */
    @Alias(JsonConstants.TOTAL_TAX)
    private double totalTax;

    /**
     * The subtotal of the refund line item in shop and presentment currencies.
     */
    @Alias(JsonConstants.SUBTOTAL_SET)
    private PriceSet subtotalSet;

    /**
     * The total tax of the line item in shop and presentment currencies.
     */
    @Alias(JsonConstants.TOTAL_TAX_SET)
    private PriceSet totalTaxSet;

}
