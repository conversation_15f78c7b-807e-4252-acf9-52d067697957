package com.zsmall.extend.shop.shopify.utils;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;

import java.util.HashMap;
import java.util.Map;

/**
 * 许可认证校验工具类
 */
public class HmacValidateUtil {

    /**
     * 许可认证
     *
     * @param clientSecret
     * @param map          请求参数
     * @return
     */
    public static boolean validateAskForPermission(String clientSecret, Map<String, String> map) {
        String hmac = map.get("hmac");
        Assert.notBlank(hmac);

        Map<String, String> build = MapUtil.builder(new HashMap<String, String>()).build();
        for (String key : map.keySet()) {
            if (StrUtil.equals("hmac", key)) {
                continue;
            }
            String value = map.get(key);
            build.put(key, value);
        }

        return validate(clientSecret, hmac, build);
    }

    /**
     * 认证
     *
     * @param clientSecret 秘钥
     * @param hmac         认证结果比对值
     * @param map          请求参数集合
     * @return
     */
    private static boolean validate(String clientSecret, String hmac, Map<String, String> map) {
        String queryString = MapUtil.sortJoin(map, "&", "=", false);
        System.out.println("queryString = " + queryString);
        // 此处密钥如果有非ASCII字符，考虑编码
        byte[] key = clientSecret.getBytes();
        HMac mac = new HMac(HmacAlgorithm.HmacSHA256, key);

        String macHex1 = mac.digestHex(queryString);


        return StrUtil.equals(macHex1, hmac);
    }

}
