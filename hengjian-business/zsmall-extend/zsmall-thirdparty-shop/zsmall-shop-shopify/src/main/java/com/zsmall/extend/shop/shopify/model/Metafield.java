package com.zsmall.extend.shop.shopify.model;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import com.zsmall.extend.shop.shopify.enums.MetafieldValueType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;

/**
 *
 */
@Data
@Accessors(chain = true)
public class Metafield {

    private String id;

    private String key;

    private String value;

    @<PERSON><PERSON>(JsonConstants.VALUE_TYPE)
    private MetafieldValueType valueType;

    private String namespace;

    @<PERSON>as(JsonConstants.OWNER_ID)
    private String ownerId;

    @<PERSON>as(JsonConstants.OWNER_RESOURCE)
    private String ownerResource;

    @<PERSON>as(JsonConstants.CREATED_AT)
    private ZonedDateTime createdAt;

    @<PERSON><PERSON>(JsonConstants.UPDATED_AT)
    private ZonedDateTime updatedAt;

}
