package com.zsmall.extend.shop.shopify.model;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * 店铺信息
 */
@Data
@Accessors(chain = true)
public class Shop {

    /**
     * 商店的街道地址。
     * The shop's street address.
     */
    private String address1;
    /**
     * 商店街道地址的可选第二行。
     * The the optional second line of the shop's street address.
     */
    private String address2;
    /**
     * 商店是否能够直接通过Checkout API接受付款。
     * Whether the shop is capable of accepting payments directly through the Checkout API.
     */
    @Alias(value = JsonConstants.CHECKOUT_API_SUPPORTED)
    private boolean checkoutApiSupported = true;
    /**
     * 商店的城市。
     * The shop's city.
     */
    private String city;
    /**
     * 商店的国家/地区。在大多数情况下，匹配country_code。
     * The shop's country. In most cases matches the country_code.
     */
    private String country;
    /**
     * 与商店所在国家/地区对应的两个字母的国家/地区代码。
     * The two-letter country code corresponding to the shop's country.
     */
    @Alias(value = JsonConstants.COUNTRY_CODE)
    private String countryCode;
    /**
     * 商店的标准化国家/地区名称。
     * The shop's normalized country name.
     */
    @Alias(value = JsonConstants.COUNTRY_NAME)
    private String countryName;
    /**
     * 商店是否按县征收税款。仅适用于美国的商店。有效值：true或null。”
     * Whether the shop is applying taxes on a per-county basis. Only applicable to shops based in the US. Valid
     * values: true or null."
     */
    @Alias(value = JsonConstants.COUNTY_TAXES)
    private Boolean countyTaxes;
    /**
     * 创建商店的日期和时间（ISO 8601）。
     * The date and time (ISO 8601) when the shop was created.
     */
//  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT-05:00")
    @Alias(value = JsonConstants.CREATED_AT)
    private ZonedDateTime createdAt;
    /**
     * 用于商店所有者和客户之间通信的联系电子邮件。
     * The contact email used for communication between the shop owner and the customer.
     */
    @Alias(value = JsonConstants.CUSTOMER_EMAIL)
    private String customerEmail;
    /**
     * 商店默认货币的三个字母的代码（ISO 4217格式）。
     * The three-letter code (ISO 4217 format) for the shop's default currency.
     */
    private String currency;
    /**
     * 商店的域。
     * The shop's domain.
     */
    private String domain;
    /**
     * 商店接受的已启用货币列表（ISO 4217格式）。商家可以从Shopify管理员的Shopify付款设置中启用货币。
     * A list of enabled currencies (ISO 4217 format) that the shop accepts. Merchants can enable currencies from their
     * Shopify Payments settings in the Shopify admin.
     */
    @Alias(value = JsonConstants.ENABLED_PRESENTMENT_CURRENCIES)
    private List<String> enabledPresentmentCurrencies;
    /**
     * 商店是否有资格从Shopify接收免费的信用卡阅读器。
     * Whether the shop is eligible to receive a free credit card reader from Shopify.
     */
    @Alias(value = JsonConstants.ELIGIBLE_FOR_CARD_READER_GIVEAWAY)
    private boolean eligibleForCardReaderGiveaway = true;
    /**
     * 商店是否有资格使用Shopify付款。
     * Whether the shop is eligible to use Shopify Payments.
     */
    @Alias(value = JsonConstants.ELIGIBLE_FOR_PAYMENTS)
    private boolean eligibleForPayments = true;
    /**
     * 用于Shopify和商店所有者之间通信的联系电子邮件。
     * The contact email used for communication between Shopify and the shop owner.
     */
    private String email;
    /**
     * 商店是否强制使用HTTPS协议通过SSL发出对其资源的请求。有效值：true或false。
     * Whether the shop forces requests made to its resources to be made over SSL using the HTTPS protocol. Valid
     * values: true or false.
     */
    @Alias(value = JsonConstants.FORCE_SSL)
    private boolean force_ssl = true;
    /**
     * 商店的GSuite URL（如果适用）。
     * The GSuite URL for the store, if applicable.
     */
    @Alias(value = JsonConstants.GOOGLE_APPS_DOMAIN)
    private String googleAppsDomain;
    /**
     * 是否启用GSuite登录。具有此功能的商店将能够通过GSuite登录页面登录。有效值：true，null
     * Whether the GSuite login is enabled. Shops with this feature will be able to log in through the GSuite login
     * page. Valid values: true, null
     */
    @Alias(value = JsonConstants.GOOGLE_APPS_LOGIN_ENABLED)
    private Boolean googleAppsLoginEnabled;
    /**
     * 商店是否有任何有效折扣。
     * Whether any active discounts exist for the shop.
     */
    @Alias(value = JsonConstants.HAS_DISCOUNTS)
    private boolean hasDiscounts = false;
    /**
     * 商店是否有任何有效的礼品卡。
     */
    @Alias(value = JsonConstants.HAS_GIFT_CARDS)
    private boolean hasGiftCards = true;
    /**
     * 商店是否有在线店面。
     * Whether the shop has an online storefront.
     */
    @Alias(value = JsonConstants.HAS_STOREFRONT)
    private boolean hasStorefront = true;
    /**
     * IANA分配的时区名称。
     * The name of the timezone assigned by the IANA.
     */
    @Alias(value = JsonConstants.IANA_TIMEZONE)
    private String ianaTimezone;
    /**
     * 商店的ID。64位无符号整数。
     * The ID for the shop. A 64-bit unsigned integer.
     */
    private Long id;
    /**
     * 商店位置的纬度。
     * The latitude of the shop's location.
     */
    private String latitude;
    /**
     * 商店位置的经度。
     * The longitude of the shop's location.
     */
    private String longitude;
    /**
     * 未指定货币时表示货币格式化方式的字符串。
     * A string representing the way currency is formatted when the currency isn't specified.
     */
    @Alias(value = JsonConstants.MONEY_FORMAT)
    private String moneyFormat;
    /**
     * 未指定货币时，表示电子邮件中的货币格式格式的字符串。
     * A string representing the way currency is formatted in email notifications when the currency isn't specified.
     */
    @Alias(value = JsonConstants.MONEY_IN_EMAILS_FORMAT)
    private String moneyInEmailsFormat;
    /**
     * 指定货币时，表示货币格式格式的字符串。
     * A string representing the way currency is formatted when the currency is specified.
     */
    @Alias(value = JsonConstants.MONEY_WITH_CURRENCY_FORMAT)
    private String moneyWithCurrencyFormat;
    /**
     * 指定币种后，一个字符串表示货币在电子邮件通知中格式化的方式。
     * A string representing the way currency is formatted in email notifications when the currency is specified.
     */
    @Alias(value = JsonConstants.MONEY_WITH_CURRENCY_IN_EMAILS_FORMAT)
    private String moneyWithCurrencyInEmailsFormat;
    /**
     * 商店是否启用了多个位置。
     * Whether the shop has enabled multiple locations.
     */
    @Alias(value = JsonConstants.MULTI_LOCATION_ENABLED)
    private boolean multiLocationEnabled = false;
    /**
     * 商店的myshopify.com域。
     * The shop's myshopify.com domain.
     */
    @Alias(value = JsonConstants.MYSHOPIFY_DOMAIN)
    private String myshopifyDomain;
    /**
     * 商店名称。
     * The name of the shop.
     */
    private String name;
    /**
     * 在线店面是否启用了密码保护页面。
     * Whether the password protection page is enabled on the online storefront.
     */
    @Alias(value = JsonConstants.PASSWORD_ENABLED)
    private boolean passwordEnabled = false;
    /**
     * 商店的联系电话。
     * The contact phone number for the shop.
     */
    private String phone;
    /**
     * 商店所在的Shopify计划的显示名称。
     * The display name of the Shopify plan the shop is on.
     */
    @Alias(value = JsonConstants.PLAN_DISPLAY_NAME)
    private String planDisplayName;
    /**
     * 在线商店上是否启用了预启动页面。
     * Whether the pre-launch page is enabled on the online storefront.
     */
    @Alias(value = JsonConstants.PRE_LAUNCH_ENABLED)
    private boolean preLaunchEnabled = false;
    /**
     * 在线商店上定义的Cookie同意级别。
     * The cookie consent level defined on the online storefront.
     */
    @Alias(value = JsonConstants.COOKIE_CONSENT_LEVEL)
    private String cookieConsentLevel;
    /**
     * 商店所在的Shopify计划的名称。
     * The name of the Shopify plan the shop is on.
     */
    @Alias(value = JsonConstants.PLAN_NAME)
    private String planName;
    /**
     * 商店主题的语言设置中配置的商店的主要语言环境。
     * The shop's primary locale, as configured in the language settings of the shop's theme.
     */
    @Alias(value = JsonConstants.PASSWORD_ENABLED)
    private String primaryLocale;
    /**
     * 以前用于运送来源位置的ID（只读）。
     * Formerly used for the ID of the shipping origin location (read only).
     */
    @Alias(value = JsonConstants.PRIMARY_LOCATION_ID)
    private Long primaryLocationId;
    /**
     * 商店的标准化省或州名称。
     * The shop's normalized province or state name.
     */
    private String province;
    /**
     * 商店的省或州的两个字母的代码。
     * The two-letter code for the shop's province or state.
     */
    @Alias(value = JsonConstants.PROVINCE_CODE)
    private String provinceCode;
    /**
     * 商店是否需要额外的Shopify付款协议。
     * Whether the shop requires an extra Shopify Payments agreement.
     */
    @Alias(value = JsonConstants.REQUIRES_EXTRA_PAYMENTS_AGREEMENT)
    private boolean requiresExtraPaymentsAgreement = false;
    /**
     * 商店是否有任何未完成的设置步骤。
     * Whether the shop has any outstanding setup steps or not.
     */
    @Alias(value = JsonConstants.SETUP_REQUIRED)
    private boolean setupRequired = false;
    /**
     * 商店所有者的用户名
     * The username of the shop owner.
     */
    @Alias(value = JsonConstants.SHOP_OWNER)
    private String shopOwner;
    /**
     * 将商户引至Shopify的合作伙伴帐户的句柄（如果适用）。
     * The handle of the partner account that referred the merchant to Shopify, if applicable.
     */
    private String source;
    /**
     * 产品价格中是否包含适用税。有效值：true或null。
     * Whether applicable taxes are included in product prices. Valid values: true or null.
     */
    @Alias(value = JsonConstants.TAXES_INCLUDED)
    private Boolean taxesIncluded;
    /**
     * 是否收取运输税。有效值：true或false。
     * Whether taxes are charged for shipping. Valid values: true or false.
     */
    @Alias(value = JsonConstants.TAX_SHIPPING)
    private Boolean taxShipping;
    /**
     * 商店所在时区的名称。
     * The name of the timezone the shop is in.
     */
    private String timezone;
    /**
     * 商店上次更新的日期和时间（ISO 8601）。
     * The date and time (ISO 8601) when the shop was last updated.
     */
    @Alias(value = JsonConstants.UPDATED_AT)
//  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT-05:00")
    private ZonedDateTime updatedAt;
    /**
     * 商店的默认重量计量单位。
     * The default unit of weight measurement for the shop.
     */
    @Alias(value = JsonConstants.WEIGHT_UNIT)
    private String weightUnit;
    /**
     * 商店的邮政编码。
     * The shop's zip or postal code.
     */
    private String zip;

}
