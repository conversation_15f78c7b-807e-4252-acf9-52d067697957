package com.zsmall.extend.shop.shopify.model.order.refund;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import com.zsmall.extend.shop.shopify.enums.OrderAdjustmentKind;
import com.zsmall.extend.shop.shopify.model.shop.PriceSet;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * A list of order adjustments attached to the refund. Order adjustments are generated to account for refunded
 * shipping costs and differences between calculated and actual refund amounts. Each entry has the following properties:
 * 退款所附的订单调整清单。生成订单调整以考虑退款的运输成本以及计算出的退款额与实际退款额之间的差额。
 *
 * @date 2022-12-28 18:07
 */
@Data
@Accessors(chain = true)
public class OrderAdjustment {

    /**
     * 订单调整的唯一标识符。
     * The unique identifier for the order adjustment.
     */
    private Long id;

    /**
     * 与订单调整关联的订单的唯一标识符。
     * The unique identifier for the order that the order adjustment is associated with.
     */
    @Alias(JsonConstants.ORDER_ID)
    private Long orderId;

    /**
     * 与订单调整关联的退款的唯一标识符。
     * The unique identifier for the refund that the order adjustment is associated with.
     */
    @Alias(JsonConstants.REFUND_ID)
    private Long refundId;

    /**
     * 计算出的退款与实际退款之间的差额。如果该kind属性的值为shipping_refund，则amount返回退还给客户的运费的值。
     * The value of the discrepancy between the calculated refund and the actual refund. If the kind property's value is shipping_refund, then amount returns the value of shipping charges refunded to the customer.
     */
    private Double amount;

    /**
     * 添加到的税款amount，例如添加到装运退款中的适用装运税。
     * The taxes that are added to amount, such as applicable shipping taxes added to a shipping refund.
     */
    @Alias(JsonConstants.TAX_AMOUNT)
    private Double taxAmount;

    /**
     * 订单调整类型。有效值：shipping_refund和refund_discrepancy。
     * The order adjustment type. Valid values: shipping_refund and refund_discrepancy.
     */
    private OrderAdjustmentKind kind;

    /**
     * 订单调整的原因。要设置此值，请discrepancy_reason在创建退款时包括在内。
     * The reason for the order adjustment. To set this value, include discrepancy_reason when you create a refund.
     */
    private String reason;

    /**
     * 以商店和演示货币表示的订单调整金额。
     * The amount of the order adjustment in shop and presentment currencies.
     */
    @Alias(JsonConstants.AMOUNT_SET)
    private PriceSet amountSet;

    /**
     * 以商店和演示货币表示的订单调整的税额。
     * The tax amount of the order adjustment in shop and presentment currencies.
     */
    @Alias(JsonConstants.TAX_AMOUNT_SET)
    private PriceSet taxAmountSet;

}
