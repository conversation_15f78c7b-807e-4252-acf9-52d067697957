package com.zsmall.extend.shop.shopify.model.order.transaction;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 货币兑换调整
 * <p>
 * 交易上的调整，显示因货币汇率波动而损失或获得的金额。
 */
@Data
@Accessors(chain = true)
public class CurrencyExchangeAdjustment {

    /**
     * 调整的 ID
     */
    private int id;

    /**
     * 关联交易记录上的金额与父交易记录之间的差额
     */
    private String adjustment;

    /**
     * 以商店货币表示的父交易记录的金额
     */
    @Alias(JsonConstants.ORIGINAL_AMOUNT)
    private String originalAmount;

    /**
     * 以商店货币表示的关联交易记录的金额
     */
    @Alias(JsonConstants.FINAL_AMOUNT)
    private String finalAmount;

    /**
     * 商店货币
     */
    private String currency;

}
