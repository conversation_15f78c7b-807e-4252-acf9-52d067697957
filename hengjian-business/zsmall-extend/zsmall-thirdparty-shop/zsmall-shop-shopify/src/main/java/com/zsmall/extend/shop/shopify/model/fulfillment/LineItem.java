package com.zsmall.extend.shop.shopify.model.fulfillment;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import com.zsmall.extend.shop.shopify.enums.LineItemStatus;
import com.zsmall.extend.shop.shopify.model.shop.Duty;
import com.zsmall.extend.shop.shopify.model.shop.TaxLine;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 履行订单的订单项
 * Represents line items belonging to a fulfillment order:
 *
 * @date 2022-12-26 15:47
 */
@Data
@Accessors(chain = true)
public class LineItem {

    /**
     * 履行订单订单项的ID。
     */
    private Long id;

    /**
     * The ID of the shop associated with the fulfillment order line item.
     * 与履行订单订单项关联的商店的ID。
     */
    @Alias(JsonConstants.SHOP_ID)
    private Long shopId;

    /**
     * The ID of the fulfillment order associated with this line item.
     * 与此订单项关联的履行订单的ID。
     */
    @Alias(JsonConstants.FULFILLMENT_ORDER_ID)
    private Long fulfillmentOrderId;

    /**
     * 履约状态
     * 未履约-null,
     * 已履约-fulfilled,
     * 部分履约-partial,
     * 不符合条件-not_eligible
     */
    @Alias(JsonConstants.FULFILLMENT_STATUS)
    private String fulfillmentStatus;

    /**
     * 与此履行订单订单项相关联的订单项的ID。
     * The ID of the line item associated with this fulfillment order line item.
     */
    @Alias(JsonConstants.LINE_ITEM_ID)
    private Long lineItemId;

    /**
     * 与此履行订单行项目相关联的库存项目的ID。
     * The ID of the inventory item associated with this fulfillment order line item.
     */
    @Alias(JsonConstants.INVENTORY_ITEM_ID)
    private Long inventoryItemId;

    /**
     * 要实现的总数量。
     * The total number of units to be fulfilled.
     */
    private Integer quantity;

    /**
     * The number of units remaining to be fulfilled.
     * 剩余需要履行的单位数。
     */
    @Alias(JsonConstants.FULFILLABLE_QUANTITY)
    private Integer fulfillableQuantity;

    /**
     * 与此履行订单订单项相关联的变体的ID。
     * The ID of the variant associated with this fulfillment order line item.
     */
    @Alias(JsonConstants.VARIANT_ID)
    private Long variantId;

    /**
     * 产品的标题。
     * The title of the product.
     */
    private String title;

    /**
     * 商品价格。
     * The price of the item.
     */
    private Double price;

    /**
     * 履行中项目的唯一标识符。
     * The unique identifier of the item in the fulfillment.
     */
    private String sku;

    /**
     * 要实现的产品变体的标题。
     * The title of the product variant being fulfilled.
     */
    @Alias(JsonConstants.VARIANT_TITLE)
    private String variantTitle;

    /**
     * 项目供应商的名称。
     * The name of the supplier of the item.
     */
    private String vendor;

    /**
     * 正在执行服务的服务提供者。
     * The service provider who is doing the fulfillment.
     */
    @Alias(JsonConstants.FULFILLMENT_SERVICE)
    private FulfillmentService fulfillmentService;

    /**
     * 实现中产品的唯一数字标识符。
     * The unique numeric identifier for the product in the fulfillment.
     */
    @Alias(JsonConstants.PRODUCT_ID)
    private Long productId;

    /**
     * 客户在订购此产品变体时是否需要提供送货地址。
     * 应课税：该订单项是否应课税。
     * Whether a customer needs to provide a shipping address when placing an order for this product variant.
     */
    @Alias(JsonConstants.REQUIRES_SHIPPING)
    private boolean requiresShipping;

    /**
     * 该订单项是否应交税。
     * Whether the line item is taxable.
     */
    private boolean taxable;

    /**
     * 订单项是否为礼品卡。
     * Whether the line item is a gift card.
     */
    @Alias(JsonConstants.GIFT_CARD)
    private boolean giftCard;

    /**
     * 产品型号的名称。
     * The name of the product variant.
     */
    private String name;

    /**
     * 库存管理系统的名称。
     * The name of the inventory management system.
     */
    @Alias(JsonConstants.VARIANT_INVENTORY_MANAGEMENT)
    private String variantInventoryManagement;

    /**
     * 与订单项关联的任何其他属性。
     * Any additional properties associated with the line item.
     */
    private List properties;

    /**
     * 产品是否存在。
     * Whether the product exists.
     */
    @Alias(JsonConstants.PRODUCT_EXISTS)
    private boolean productExists;

    /**
     * 应用于订单项的所有折扣的总和。
     * The total of any discounts applied to the line item.
     */
    @Alias(JsonConstants.TOTAL_DISCOUNT)
    private Double totalDiscount;

    /**
     * 根据订单项的订单状态。有效值：fulfilled，null或partial。
     * he status of an order in terms of the line items being fulfilled. Valid values: fulfilled, null, or partial.
     */
    @Alias(JsonConstants.FULFILLMENT_STATUS)
    private LineItemStatus lineItemStatus;

    /**
     * title，price和rate应用到行项目的任何税款。
     * The title, price, and rate of any taxes applied to the line item.
     */
    @Alias(JsonConstants.TAX_LINES)
    private List<TaxLine> taxLines;

    /**
     * 职责对象列表，每个对象包含有关订单项职责的信息。
     * A list of duty objects, each containing information about a duty on the line item.
     */
    private List<Duty> duties;


}
