package com.zsmall.extend.shop.shopify.model.product;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.api.model.product.InMetafield;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import com.zsmall.extend.shop.shopify.enums.ProductStatusEnum;
import com.zsmall.extend.shop.shopify.enums.PublishedScopeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * 商品
 *
 * @date 2022-12-24 15:06
 */
@Data
@Accessors(chain = true)
public class Product {

    /**
     * An unsigned 64-bit integer that's used as a unique identifier for the product. Each id is unique across the
     * Shopify system. No two products will have the same id, even if they're from different shops.
     */
    private Long id;

    /**
     * A description of the product. Supports HTML formatting.
     */
    @Alias(JsonConstants.BODY_HTML)
    private String bodyHtml;

    /**
     * The date and time (ISO 8601 format) when the product was created.
     */
    @Alias(JsonConstants.CREATED_AT)
    private ZonedDateTime createdAt;

    /**
     * A unique human-friendly string for the product. Automatically generated from the product's title. Used by the
     * Liquid templating language to refer to objects.
     */
    private String handle;

    /**
     * A list of product image objects, each one representing an image associated with the product.
     * "images": [
     * {
     * "id": 850703190,
     * "product_id": 632910392,
     * "position": 1,
     * "created_at": "2018-01-08T12:34:47-05:00",
     * "updated_at": "2018-01-08T12:34:47-05:00",
     * "width": 110,
     * "height": 140,
     * "src": "http://example.com/burton.jpg",
     * "variant_ids": [
     * {}
     * ]
     * }
     * ]
     */
    private List<ProductImage> image;

    /**
     * The custom product property names like Size, Color, and Material. You can add up to 3 options of up to 255
     * characters each.
     */
    @Alias(JsonConstants.PRODUCT_VARIANT_OPTIONS)
    private List<VariantOption> variantOptions;

    /**
     * A categorization for the product used for filtering and searching products.
     */
    @Alias(JsonConstants.PRODUCT_TYPE)
    private String productType;

    /**
     * 使集合可见的时间和日期（ISO 8601格式）。返回null隐藏的自定义集合。
     * "published_at": "2007-12-31T19:00:00-05:00"
     * The date and time (ISO 8601 format) when the product was published. Can be set to null to unpublish the product
     * from the Online Store channel.
     */
    @Alias(JsonConstants.PUBLISHED_AT)
    private ZonedDateTime publishedAt;

    /**
     * Whether the product is published to the Point of Sale channel. Valid values:
     * <p>
     * web: The product is published to the Online Store channel but not published to the Point of Sale channel.
     * global: The product is published to both the Online Store channel and the Point of Sale channel.
     */
    @Alias(JsonConstants.PUBLISHED_SCOPE)
    private PublishedScopeEnum publishedScope;

    private ProductStatusEnum status;

    /**
     * A string of comma-separated tags that are used for filtering and search. A product can have up to 250 tags. Each
     * tag can have up to 255 characters.
     */
    private String tags;

    /**
     * The suffix of the Liquid template used for the product page. If this property is specified, then the product
     * page uses a template called "product.suffix.liquid", where "suffix" is the value of this property. If this
     * property is "" or null, then the product page uses the default template "product.liquid". (default: null)
     */
    @Alias(JsonConstants.TEMPLATE_SUFFIX)
    private String templateSuffix;

    /**
     * The name of the product.
     */
    private String title;

    /**
     * The date and time (ISO 8601 format) when the product was last modified.
     */
    @Alias(JsonConstants.UPDATED_AT)
    private ZonedDateTime updatedAt;

    /**
     * A list of product variants, each representing a different version of the product.
     * The position property is read-only. The position of variants is indicated by the order in which they are listed.
     * To retrieve the presentment_prices property on a variant, include the request header 'X-Shopify-Api-Features':
     * 'include-presentment-prices'.
     */
    @Alias(JsonConstants.PRODUCT_VARIANTS)
    private List<ProductVariant> productVariants;

    /**
     * The name of the product's vendor.
     */
    private String vendor;

    private List<InMetafield> metafields;

}
