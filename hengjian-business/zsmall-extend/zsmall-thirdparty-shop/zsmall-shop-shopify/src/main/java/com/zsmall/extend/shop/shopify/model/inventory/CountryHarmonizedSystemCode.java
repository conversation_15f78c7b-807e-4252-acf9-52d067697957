package com.zsmall.extend.shop.shopify.model.inventory;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 物料的 一组特定于国家/地区的协调系统（HS）代码。用于确定将库存物品运送到某些国家/地区时的关税。
 *
 * @date 2022-12-25 10:10
 */
@Data
@Accessors(chain = true)
public class CountryHarmonizedSystemCode {

    @Alias(JsonConstants.HARMONIZED_SYSTEM_CODE)
    private String harmonizedSystemCode;

    @Alias(JsonConstants.COUNTRY_CODE)
    private String countryCode;

}
