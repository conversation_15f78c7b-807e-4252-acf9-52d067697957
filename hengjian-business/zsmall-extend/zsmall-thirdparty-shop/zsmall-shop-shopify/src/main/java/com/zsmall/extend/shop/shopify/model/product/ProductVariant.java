package com.zsmall.extend.shop.shopify.model.product;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import lombok.Data;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;

@Data
public class ProductVariant {
    @Alias(JsonConstants.ID)
    private Long id;

    @Alias(JsonConstants.TITLE)
    private String title;

    @<PERSON>as(JsonConstants.PRICE)
    private BigDecimal price;

    @Alias(JsonConstants.COMPARE_AT_PRICE)
    private BigDecimal compareAtPrice;

    /**
     * The custom properties that a shop owner uses to define product variants. You can define three options for a
     * product variant: option1, option2, option3. Default value: Default Title. The title field is a concatenation of
     * the option1, option2, and option3 fields. Updating the option fields updates the title field.
     */
    @Alias(JsonConstants.OPTION_1)
    private String option1;

    @<PERSON>as(JsonConstants.OPTION_2)
    private String option2;

    @Alias(JsonConstants.OPTION_3)
    private String option3;

    private String barcode;

    /**
     * The date and time (ISO 8601 format) when the product was created.
     */
    @Alias(JsonConstants.CREATED_AT)
    private ZonedDateTime createdAt;

    /**
     * The fulfillment service associated with the product variant. Valid values: manual or the handle of a fulfillment
     * service.
     */
    @Alias(JsonConstants.FULFILLMENT_SERVICE)
    private String fulfillmentService;

    /**
     * The weight of the product variant in grams.
     */
    private Integer grams;

    @Alias(JsonConstants.IMAGE_ID)
    private Long imageId;

    /**
     * The weight of the product variant in the unit system specified with weight_unit.
     */
    private Double weight;

    /**
     * The unit of measurement that applies to the product variant's weight. If you don't specify a value for
     * weight_unit, then the shop's default unit of measurement is applied. Valid values: g, kg, oz, and lb.
     */
    @Alias(JsonConstants.WEIGHT_UNIT)
    private String weightUnit;

    /**
     * The unique identifier for the inventory item, which is used in the Inventory API to query for inventory
     * information.
     */
    @Alias(JsonConstants.INVENTORY_ITEM_ID)
    private Long inventoryItemId;

    /**
     * The fulfillment service that tracks the number of items in stock for the product variant. Valid values:
     * <p>
     * shopify: You are tracking inventory yourself using the admin.
     * null: You aren't tracking inventory on the variant.
     * the handle of a fulfillment service that has inventory management enabled: This must be the same
     * fulfillment service referenced by the fulfillment_service property.
     */
    @Alias(JsonConstants.INVENTORY_MANAGEMENT)
    private String inventoryManagement;

    /**
     * Whether customers are allowed to place an order for the product variant when it's out of stock. Valid values:
     * <p>
     * deny: Customers are not allowed to place orders for the product variant if it's out of stock.
     * continue: Customers are allowed to place orders for the product variant if it's out of stock.
     * Default value: deny.
     */
    @Alias(JsonConstants.INVENTORY_POLICY)
    private String inventoryPolicy;

    /**
     * An aggregate of inventory across all locations. To adjust inventory at a specific location, use the
     * InventoryLevel resource.
     */
    @Alias(JsonConstants.INVENTORY_QUANTITY)
    private int inventoryQuantity;


    /**
     * This property is deprecated. Use the InventoryLevel resource instead.
     */
    @Alias(JsonConstants.OLD_INVENTORY_QUANTITY)
    private int oldInventoryQuantity;

    /**
     * This property is deprecated. Use the InventoryLevel resource instead.
     */
    @Alias(JsonConstants.INVENTORY_QUANTITY_ADJUSTMENT)
    private int inventoryQuantityAdjustment;

    /**
     * A list of the variant's presentment prices and compare-at prices in each of the shop's enabled presentment
     * currencies. Each price object has the following properties:
     * <p>
     * currency_code: The three-letter code (ISO 4217 format) for one of the shop's enabled presentment currencies.
     * amount: The variant's price or compare-at price in the presentment currency.
     * Requires the header 'X-Shopify-Api-Features': 'include-presentment-prices'.
     * [
     * {
     * "price": {
     * "currency_code": "USD",
     * "amount": "199.99"
     * },
     * "compare_at_price": {
     * "currency_code": "USD",
     * "amount": "249.99"
     * }
     * },
     * {
     * "price": {
     * "currency_code": "EUR",
     * "amount": "158.95"
     * },
     * "compare_at_price": {
     * "currency_code": "EUR",
     * "amount": "198.95"
     * }
     * }
     * ]
     */
    @Alias(JsonConstants.PRESENTMENT_PRICES)
    private List<PresentmentPrice> presentmentPrices;

    /**
     * The order of the product variant in the list of product variants. The first position in the list is 1. The
     * position of variants is indicated by the order in which they are listed.
     */
    private int position;

    /**
     * The unique numeric identifier for the product.
     */
    @Alias(JsonConstants.PRODUCT_ID)
    private Long productId;

    /**
     * "sku": "IPOD2008PINK"
     * A unique identifier for the product variant in the shop. Required in order to connect to a FulfillmentService.
     */
    private String sku;

    /**
     * Whether a tax is charged when the product variant is sold.
     */
    private boolean taxable;


    /**
     * This parameter applies only to the stores that have the Avalara AvaTax app installed. Specifies the Avalara tax
     * code for the product variant.
     */
    @Alias(JsonConstants.TAX_CODE)
    private String taxCode;

    /**
     * The date and time (ISO 8601 format) when the product was last modified.
     */
    @Alias(JsonConstants.UPDATED_AT)
    private ZonedDateTime updatedAt;

}
