package com.zsmall.extend.shop.shopify.model.order;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import com.zsmall.extend.shop.shopify.model.shop.PriceSet;
import com.zsmall.extend.shop.shopify.model.shop.TaxLine;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @date 2022-12-28 15:50
 */
@Data
@Accessors(chain = true)
public class ShippingLine {

    /**
     * A reference to the shipping method.
     * 运输方式的参考。
     */
    private String code;

    /**
     * The price of this shipping method in the shop currency. Can't be negative.
     * 此运输方式的价格（以商店货币为单位）。不能为负。
     */
    private String price;

    /**
     * The price of the shipping method in shop and presentment currencies.
     * 以商店和演示货币表示的运输方式的价格。
     */
    @Alias(JsonConstants.PRICE_SET)
    private PriceSet priceSet;

    /**
     * The price of the shipping method after line-level discounts have been applied. Doesn't reflect cart-level or
     * order-level discounts.
     * 应用行级折扣后的送货方式价格。不反映购物车级别或订单级别的折扣。
     */
    @Alias(JsonConstants.DISCOUNTED_PRICE)
    private String discountedPrice;

    /**
     * The price of the shipping method in both shop and presentment currencies after line-level discounts have been
     * applied.
     * 应用行级折扣后，以商店和演示货币表示的送货方式价格。
     */
    @Alias(JsonConstants.DISCOUNTED_PRICE_SET)
    private PriceSet discountedPriceSet;

    /**
     * The source of the shipping method.
     * 送货方式的来源。
     */
    private String source;

    /**
     * The title of the shipping method.
     * 送货方式的标题。
     */
    private String title;

    /**
     * A list of tax line objects, each of which details a tax applicable to this shipping line.
     * 税线对象的列表，每个对象都详细说明了适用于此运输线的税。
     */
    @Alias(JsonConstants.TAX_LINES)
    private List<TaxLine> taxLines;

    /**
     * A reference to the carrier service that provided the rate. Present when the rate was computed by a third-party
     * carrier service.
     * 对提供费率的运营商服务的引用。由第三方运营商服务计算费率时显示。
     */
    @Alias(JsonConstants.CARRIER_IDENTIFIER)
    private String carrierIdentifier;

    /**
     * A reference to the fulfillment service that is being requested for the shipping method. Present if the shipping
     * method requires processing by a third party fulfillment service; null otherwise.
     * 对运送方法所要求的履行服务的引用。如果运输方式需要第三方履行服务进行处理，请提供；null除此以外。
     */
    @Alias(JsonConstants.REQUESTED_FULFILLMENT_SERVICE_ID)
    private String requestedFulfillmentServiceId;

}
