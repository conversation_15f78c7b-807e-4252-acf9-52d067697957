package com.zsmall.extend.shop.shopify.model.order.transaction;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import com.zsmall.extend.shop.shopify.enums.TransactionErrorCode;
import com.zsmall.extend.shop.shopify.enums.TransactionSourceType;
import com.zsmall.extend.shop.shopify.enums.TransactionStatus;
import com.zsmall.extend.shop.shopify.model.fulfillment.Receipt;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;

/**
 * 交易
 * 为每笔订单创建交易，从而导致金钱的交换。
 * <p>
 * 注意
 * 一个订单最多只能关联100个交易。
 *
 * @date 2022-12-28 18:43
 */
@Data
@Accessors(chain = true)
public class Transaction {

    /**
     * 交易ID
     */
    private Long id;

    /**
     * 交易记录关联的订单的 ID
     */
    @Alias(JsonConstants.ORDER_ID)
    private Long orderId;

    /**
     * 金额
     */
    private String amount;

    /**
     * 总类
     */
    private String kind;

    /**
     * 网关
     * 事务通过发出网关的名称。网关列表可以在 Shopify 的支付网关页面上找到。
     */
    private String gateway;

    /**
     * 交易的状态。有效值pending、failure、success、error
     */
    private TransactionStatus status;

    /**
     * 由付款提供程序生成的字符串，包含有关交易成功或失败原因的其他信息。
     */
    private String message;

    /**
     * 创建退款的日期和时间（ISO 8601格式）。
     */
    @Alias(JsonConstants.CREATED_AT)
    private ZonedDateTime createdAt;

    /**
     * 交易是否为测试交易
     */
    private boolean test;

    /**
     * 认证码-clientId And Secret base64加密
     */
    private String authorization;

    /**
     * 货币
     */
    private String currency;

    /**
     * 处理交易的物理位置的 ID
     */
    @Alias(JsonConstants.LOCATION_ID)
    private Long locationId;

    /**
     * 处理订单时登录到 Shopify POS 设备的用户的 ID
     */
    @Alias(JsonConstants.USER_ID)
    private Long userId;

    /**
     * 关联交易的 ID
     */
    @Alias(JsonConstants.PARENT_ID)
    private int parentId;

    /**
     * 设备的 ID
     */
    @Alias(JsonConstants.DEVICE_ID)
    private String deviceId;

    private Receipt receipt;

    /**
     * 交易详情
     */
    @Alias(JsonConstants.PAYMENT_DETAILS)
    private PaymentDetails paymentDetails;

    /**
     * 货币兑换调整
     */
    @Alias(JsonConstants.CURRENCY_EXCHANGE_ADJUSTMENT)
    private CurrencyExchangeAdjustment currencyExchangeAdjustment;

    /**
     * 错误码
     */
    @Alias(JsonConstants.ERROR_CODE)
    private TransactionErrorCode error_code;

    /**
     * 交易记录的源。这是由 Shopify 设置的，无法覆盖。示例值：web、pos、iphone、android
     */
    @Alias(JsonConstants.SOURCE_NAME)
    private TransactionSourceType source_name;

}
