package com.zsmall.extend.shop.shopify.model.product;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 变体的展示价格和比较价格
 *
 * @date 2022-12-25 0:57
 */
@Data
@Accessors(chain = true)
public class PresentmentPrice {

    private VariantPrice price;

    @Alias(JsonConstants.COMPARE_AT_PRICE)
    private VariantPrice compareAtPrice;

}
