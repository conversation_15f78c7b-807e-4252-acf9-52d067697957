package com.zsmall.extend.shop.shopify.model.order.transaction;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 交易详情
 * <p>
 * 有关用于此交易的信用卡的信息。
 */
@Data
@Accessors(chain = true)
public class PaymentDetails {

    /**
     * 地址验证系统的响应代码。代码是单个字母。
     */
    @Alias(JsonConstants.AVS_RESULT_CODE)
    private String avsResultCode;

    /**
     * 发卡人识别号（IIN），以前称为客户信用卡的银行识别号（BIN）。这由信用卡号码的前几个数字组成。
     */
    @Alias(JsonConstants.CREDIT_CARD_BIN)
    private String creditCardBin;

    /**
     * 信用卡公司的响应代码，指示客户输入的信用卡安全代码或卡验证值是否正确。代码是单个字母或空字符串;
     */
    @Alias(JsonConstants.CVV_RESULT_CODE)
    private String cvvResultCode;

    /**
     * 客户的信用卡号，大部分领先数字都经过了编辑。
     */
    @Alias(JsonConstants.CREDIT_CARD_NUMBER)
    private String creditCardNumber;

    /**
     * 签发客户信用卡的公司名称。
     */
    @Alias(JsonConstants.CREDIT_CARD_COMPANY)
    private String creditCardCompany;

}
