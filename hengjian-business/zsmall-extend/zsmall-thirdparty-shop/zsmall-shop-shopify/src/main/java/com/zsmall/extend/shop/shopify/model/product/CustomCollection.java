package com.zsmall.extend.shop.shopify.model.product;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import com.zsmall.extend.shop.shopify.model.Image;
import lombok.Data;

import java.time.ZonedDateTime;

/**
 * 自定义集合
 *
 * <AUTHOR>
 */
@Data
public class CustomCollection {
    /**
     * 自定义集合的ID。
     * The ID for the custom collection.
     */
    @Alias(value = JsonConstants.ID)
    private long id;

    /**
     * 自定义集合的名称。（限制：255个字符）
     * The name of the custom collection. (limit: 255 characters)
     */
    @Alias(value = JsonConstants.TITLE)
    private String title;

    /**
     * 根据标题自动生成的自定义集合的人性化唯一字符串。Liquid模板语言在商店主题中使用它来引用自定义集合。（限制：255个字符）
     * A human-friendly unique string for the custom collection automatically generated from its title. This is used in
     * shop themes by the Liquid templating language to refer to the custom collection. (limit: 255 characters)
     */
    @Alias(value = JsonConstants.HANDLE)
    private String handle;

    /**
     * 自定义集合的描述，带有HTML标记。许多模板在其自定义集合页面上显示此内容。
     * The description of the custom collection, complete with HTML markup. Many templates display this on their custom
     * collection pages.
     */
    @Alias(value = JsonConstants.BODY_HTML)
    private String bodyHtml;

    /**
     * 与自定义集合关联的图像
     * Image associated with the custom collection.
     */
    @Alias(value = JsonConstants.IMAGE)
    private Image image;

    /**
     * 自定义集合是否已发布到在线商店频道。
     * Whether the custom collection is published to the Online Store channel.
     */
    private boolean published;

    /**
     * 使集合可见的时间和日期（ISO 8601格式）。返回null隐藏的自定义集合。
     * The time and date (ISO 8601 format) when the collection was made visible. Returns null for a hidden custom
     * collection.
     */
    @Alias(JsonConstants.PUBLISHED_AT)
//  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT-05:00")
    private ZonedDateTime publishedAt;

    /**
     * 该收藏集是否已发布到销售点渠道。有效值：
     * web：自定义收藏集已发布到“在线商店”渠道，但未发布到“销售点”渠道。
     * global：自定义收藏同时发布到在线商店渠道和销售点渠道。
     * Whether the collection is published to the Point of Sale channel.
     */
    @Alias(JsonConstants.PUBLISHED_SCOPE)
    private String publishedScope;

    /**
     * 自定义集合中产品的显示顺序。有效值：
     * <p>
     * alpha-asc：按字母顺序，按升序（A-Z）。
     * alpha-desc：按字母顺序，降序排列（Z-A）。
     * best-selling：通过畅销产品。
     * created：按创建日期的升序排列（从最旧到最新）。
     * created-desc：按创建日期的降序排列（最新-最旧）。
     * manual：由店主创建的订单。
     * price-asc：按价格升序排列（最低-最高）。
     * price-desc：按价格降序排列（最高-最低）。
     * <p>
     * The order in which products in the custom collection appear.
     */
    @Alias(JsonConstants.SORT_ORDER)
    private String sortOrder;

    /**
     * 使用的液体模板的后缀。例如，如果值为custom，则表示集合正在使用collection.custom.liquid模板。如果值为null，则该集合使用默认值collection.liquid。
     * The suffix of the liquid template being used. For example, if the value is custom, then the collection is using
     * the collection.custom.liquid template. If the value is null, then the collection is using the default collection
     * .liquid.
     */
    @Alias(JsonConstants.TEMPLATE_SUFFIX)
    private String templateSuffix;

    /**
     * 自定义集合的最新修改日期和时间（ISO 8601格式）。
     * The date and time (ISO 8601 format) when the custom collection was last modified.
     */
    @Alias(JsonConstants.UPDATED_AT)
    private ZonedDateTime updatedAt;

}
