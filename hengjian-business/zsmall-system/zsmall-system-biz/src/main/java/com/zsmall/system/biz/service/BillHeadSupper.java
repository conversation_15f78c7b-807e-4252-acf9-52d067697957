package com.zsmall.system.biz.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.utils.DateUtils;
import com.hengjian.common.json.utils.JsonUtils;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.hengjian.system.domain.vo.SysUserVo;
import com.hengjian.system.mapper.SysUserMapper;
import com.hengjian.system.service.ISysConfigService;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.common.enums.bill.RelationTypeEnum;
import com.zsmall.common.enums.transaction.TransactionStateEnum;
import com.zsmall.common.enums.transaction.TransactionSubTypeEnum;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.OrderItemPrice;
import com.zsmall.order.entity.domain.OrderRefund;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.mapper.OrderItemMapper;
import com.zsmall.order.entity.mapper.OrderItemPriceMapper;
import com.zsmall.order.entity.mapper.OrderRefundMapper;
import com.zsmall.order.entity.mapper.OrdersMapper;
import com.zsmall.system.entity.domain.*;
import com.zsmall.system.entity.domain.dto.BillConfirmedSendErpDTO;
import com.zsmall.system.entity.domain.vo.SiteCountryCurrencyVo;
import com.zsmall.system.entity.mapper.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class BillHeadSupper {
    private final BillRelationMapper billRelationMapper;
    private final BillRelationDetailMapper billRelationDetailMapper;
    private final OrderItemMapper orderItemMapper;
    private final BillDetailsMapper billDetailsMapper;
    private final BillAbstractMapper billAbstractMapper;
    private final BillAbstractDetailMapper billAbstractDetailMapper;
    private final BillMapper billMapper;
    private final BillHeadMapper billHeadMapper;
    private final OrdersMapper ordersMapper;
    private final OrderRefundMapper orderRefundMapper;
    private final BillTransactionReceiptMapper billTransactionReceiptMapper;
  //  private final TransactionReceiptMapper transactionReceiptMapper;
    private final TransactionRecordMapper transactionRecordMapper;
    private final OrderItemPriceMapper orderItemPriceMapper;
    private final ISysTenantService sysTenantService;
    private final SysUserMapper sysUserMapper;
    private final ISysConfigService sysConfigService;
    @Resource
    private RabbitTemplate rabbitTemplate;
    private final SiteCountryCurrencyMapper siteCountryCurrencyMapper;
    private final TenantSiteMapper tenantSiteMapper;
    /**
     * @description: 处理支付订单
     * @author: Len
     * @date: 2024/9/9 11:29
     * @param: selectOrdersByTenant 已支付订单集合
     * @param: billHead 账单头
     * @param: billType 账单业务类型 1 供应商 2分销商
     * @return: java.util.List<com.zsmall.system.entity.domain.BillDetails>
     **/
    public List<BillDetails> dealBillOrderItem(List<OrderItemSystem> selectOrdersByTenant, BillHead billHead,Integer billType){
        Set<Long> uniqueOrderIds = selectOrdersByTenant.stream()
                                                       .map(OrderItemSystem::getOrderId)
                                                       .collect(Collectors.toSet());

        //查询订单明细ID
        Set<Long> orderItemIds = selectOrdersByTenant.stream()
                                          .map(OrderItemSystem::getId)
                                          .collect(Collectors.toSet());
        Map<Long, Orders> ordersMap=new HashMap<>();
        if (CollectionUtil.isNotEmpty(uniqueOrderIds)){
            LambdaQueryWrapper<Orders> l=new LambdaQueryWrapper<>();
            l.in(Orders::getId,uniqueOrderIds);
            l.eq(Orders::getDelFlag,0);
            List<Orders> orders =    TenantHelper.ignore(()-> ordersMapper.selectList(l));
            ordersMap = orders.stream().collect(Collectors.toMap(Orders::getId, order -> order));
        }

        //查询订单orderItemPrice
        Map<Long, OrderItemPrice> orderItemPriceMap=new HashMap<>();
        if (CollUtil.isNotEmpty(orderItemIds)){
            LambdaQueryWrapper<OrderItemPrice> q = new LambdaQueryWrapper<>();
            q.in(OrderItemPrice::getOrderItemId,orderItemIds);
            List<OrderItemPrice> orderItemPrices =TenantHelper.ignore(()->orderItemPriceMapper.selectList(q));
            orderItemPriceMap = orderItemPrices.stream()
                                                .collect(Collectors.toMap(OrderItemPrice::getOrderItemId, orderItemPrice -> orderItemPrice));
        }

        List<BillDetails> billDetailsList=new ArrayList<>();
        for (OrderItemSystem orderItem : selectOrdersByTenant) {
            BillDetails billDetails=new BillDetails();
            billDetails.setId(IdUtil.getSnowflakeNextId());
            billDetails.setCurrencySymbol(billHead.getCurrencySymbol());
            billDetails.setCurrencyCode(billHead.getCurrencyCode());
            billDetails.setCountryCode(billHead.getCountryCode());
            billDetails.setSupportedLogistics(orderItem.getLogisticsType().name());
            OrderItemPrice orderItemPrice = orderItemPriceMap.get(orderItem.getId());
            if (ObjectUtil.isNotNull(orderItemPrice)){
                if (billType==1){
                    billDetails.setUnitPrice(orderItemPrice.getOriginalUnitPrice());
                }
                if (billType==2){
                    billDetails.setUnitPrice(orderItemPrice.getPlatformUnitPrice());
                }
            }
            billDetails.setBillNo(billHead.getBillNo());
            billDetails.setBillId(billHead.getId());
            billDetails.setTenantId(orderItem.getTenantId());
            billDetails.setSupperTenantId(orderItem.getSupplierTenantId());
            billDetails.setProductSkuCode(orderItem.getProductSkuCode());
            billDetails.setOrderStatus(1);
            billDetails.setDelFlag(0);
            billDetails.setOrderNo(orderItem.getOrderNo());
            billDetails.setCreateTime(new Date());
            billDetails.setUpdateTime(new Date());
            if (ObjectUtil.isNotNull(LoginHelper.getTenantId())){
                billDetails.setCreateBy(LoginHelper.getUserId());
                billDetails.setUpdateBy(LoginHelper.getUserId());
            }
            Orders orders1 = ordersMap.get(orderItem.getOrderId());
            if (ObjectUtil.isNull(orders1)|| ObjectUtil.isNull(orders1.getCreateTime())){
                throw new RuntimeException("[账单模块]未匹配到订单信息"+ JsonUtils.toJsonString(orderItem));
            }
            if (ObjectUtil.isNotNull(orders1)){
                billDetails.setChannelOrderNo(orders1.getChannelOrderNo());
                billDetails.setOrderExtendId(orders1.getOrderExtendId());
                billDetails.setOrderPaymentMethods(orders1.getPayType());
                billDetails.setOrderTime(orders1.getCreateTime());
                billDetails.setProductQuantity(orders1.getTotalQuantity());
                //订单总金额
                billDetails.setOrderTotalAmount(orders1.getOriginalActualTotalAmount());
                //供应商全部统计
                if (billType==1){
                    //单价
                    if (ObjectUtil.isNotNull(orders1.getOriginalPayableTotalAmount()) && ObjectUtil.isNotNull(orders1.getTotalQuantity())){
                        billDetails.setProductSkuPrice(orders1.getOriginalPayableTotalAmount().divide(BigDecimal.valueOf(orders1.getTotalQuantity()),2, RoundingMode.HALF_UP));
                    }
                    //操作费
                    billDetails.setOperationFee(orders1.getOriginalTotalOperationFee());
                    //尾程派送费
                    billDetails.setFinalDeliveryFee(orders1.getOriginalTotalFinalDeliveryFee());
                }
                //分销商 只统计发货总金额/总数量/退货总金额/总数量
                if (billType==2){
                    if (ObjectUtil.isNotNull(orders1.getOriginalActualTotalAmount())){
                        billDetails.setProductSkuPrice(orders1.getOriginalActualTotalAmount().divide(BigDecimal.valueOf(orders1.getTotalQuantity()),2, RoundingMode.HALF_UP));
                    }
                    //操作费
                    billDetails.setOperationFee(orders1.getPlatformTotalOperationFee());
                    //尾程派送费
                    billDetails.setFinalDeliveryFee(orders1.getPlatformTotalFinalDeliveryFee());
                }
            }
            billDetailsList.add(billDetails);
        }
        billDetailsSqlInsert(billDetailsList);
        return billDetailsList;
    }

    /**
     * 拼接sql插入账单明细
     * @param billDetailsList 账单明细集合
     */
    public void billDetailsSqlInsert(List<BillDetails> billDetailsList){
        if (CollectionUtil.isNotEmpty(billDetailsList)){
            StringBuilder sql = new StringBuilder("INSERT INTO bill_details (id, bill_id, bill_no, order_no,channel_order_no,order_extend_id, tenant_id, supper_tenant_id, order_refund_no, order_time, order_status, order_payment_methods, product_sku_code, product_quantity, operation_fee, final_delivery_fee, product_sku_price, order_total_amount, order_refund_total_amount, create_by, create_time, update_by, update_time, del_flag, currency_symbol, currency_code,country_code, supported_logistics, unit_price) VALUES ");
            for (BillDetails s : billDetailsList) {
                String orderTimeStr = s.getOrderTime() == null ? "null" : "'" + DateUtil.format(s.getOrderTime(), "yyyy-MM-dd HH:mm:ss") + "'";
                String createTimeStr = s.getCreateTime() == null ? "null" : "'" + DateUtil.format(s.getCreateTime(), "yyyy-MM-dd HH:mm:ss") + "'";
                String updateTimeStr = s.getUpdateTime() == null ? "null" : "'" + DateUtil.format(s.getUpdateTime(), "yyyy-MM-dd HH:mm:ss") + "'";
                sql.append(String.format("(%d, %d, '%s', '%s', '%s', '%s', '%s', '%s', '%s', %s, %d, '%s', '%s', %d, %s, %s, %s, %s, %s, %s, %s, %s, %s, %d, '%s', '%s', '%s', '%s', %s),",
                    s.getId(),
                    s.getBillId(),
                    s.getBillNo(),
                    s.getOrderNo(),
                    s.getChannelOrderNo(),
                    s.getOrderExtendId(),
                    s.getTenantId(),
                    s.getSupperTenantId(),
                    s.getOrderRefundNo(),
                    orderTimeStr,
                    s.getOrderStatus(),
                    s.getOrderPaymentMethods(),
                    s.getProductSkuCode(),
                    s.getProductQuantity(),
                    s.getOperationFee(),
                    s.getFinalDeliveryFee(),
                    s.getProductSkuPrice(),
                    s.getOrderTotalAmount(),
                    s.getOrderRefundTotalAmount(),
                    s.getCreateBy(),
                    createTimeStr,
                    s.getUpdateBy(),
                    updateTimeStr,
                    s.getDelFlag(),
                    s.getCurrencySymbol(),
                    s.getCurrencyCode(),
                    s.getCountryCode(),
                    s.getSupportedLogistics(),
                    s.getUnitPrice()
                ));
            }
            sql.deleteCharAt(sql.length() - 1);
            billDetailsMapper.insertBySql(sql.toString());
        }
    }


    /**
     * @description: 处理退款订单
     * @author: Len
     * @date: 2024/9/9 11:30
     * @param: orderRefunds  List<退款单>
     * @param: billHead 账单头
     * @param: billType 账单业务类型 1供应商 2 分销商
     * @return: java.util.List<com.zsmall.system.entity.domain.BillDetails>
     **/
    public List<BillDetails> dealBillOrderRefundItem(List<OrderRefund>  orderRefunds, BillHead billHead, Integer billType){
        if (CollectionUtil.isEmpty(orderRefunds)){
            return new ArrayList<>();
        }
        Set<Long> uniqueOrderIds = orderRefunds.stream()
                                               .map(OrderRefund::getOrderId)
                                               .collect(Collectors.toSet());
        //查找
        LambdaQueryWrapper<OrderItem> qq = new LambdaQueryWrapper<>();
        qq.eq(OrderItem::getDelFlag,0);
        qq.in(OrderItem::getOrderId,uniqueOrderIds);
        List<OrderItem> orderItems =TenantHelper.ignore(()->orderItemMapper.selectList(qq));

        Map<Long, OrderItem> orderItemMap = orderItems.stream()
                                                      .collect(Collectors.toMap(OrderItem::getOrderId, item -> item));
        LambdaQueryWrapper<Orders> q = new LambdaQueryWrapper<>();
        q.eq(Orders::getDelFlag,0);
        q.in(Orders::getId,uniqueOrderIds);
        List<Orders> orders = TenantHelper.ignore(()->ordersMapper.selectList(q));
        Map<Long, Orders> ordersMap = orders.stream().collect(Collectors.toMap(Orders::getId, items -> items));
//        Set<String> productSkuCodes = orderItems.stream()
//                                                .map(OrderItem::getProductSkuCode)
//                                                .collect(Collectors.toSet());
//        //查询所有的Product
//        Map<String, Product> productMap=new HashMap<>();
//        if (CollUtil.isNotEmpty(productSkuCodes)){
//            List<Product> products=TenantHelper.ignore(()->productMapper.getByProductSkuCodes(productSkuCodes));
//            productMap = products.stream().collect(Collectors.toMap(Product::getProductCode, product -> product));
//        }

        //查询订单orderItemPrice
        Set<Long> orderItemIds = orderItems.stream()
                                                     .map(OrderItem::getId)
                                                     .collect(Collectors.toSet());
        Map<Long, OrderItemPrice> orderItemPriceMap=new HashMap<>();
        if (CollUtil.isNotEmpty(orderItemIds)){
            LambdaQueryWrapper<OrderItemPrice> qs = new LambdaQueryWrapper<>();
            qs.in(OrderItemPrice::getOrderItemId,orderItemIds);
            List<OrderItemPrice> orderItemPrices =TenantHelper.ignore(()->orderItemPriceMapper.selectList(qs));
            orderItemPriceMap = orderItemPrices.stream()
                                               .collect(Collectors.toMap(OrderItemPrice::getOrderItemId, orderItemPrice -> orderItemPrice));
        }
        List<BillDetails> billDetailsList=new ArrayList<>();

        for (OrderRefund orderRefund : orderRefunds) {
            BillDetails billDetails=new BillDetails();
            billDetails.setId(IdUtil.getSnowflakeNextId());
            billDetails.setCurrencySymbol(billHead.getCurrencySymbol());
            billDetails.setCurrencyCode(billHead.getCurrencyCode());
            billDetails.setCountryCode(billHead.getCountryCode());
            billDetails.setBillId(billHead.getId());
            billDetails.setBillNo(billHead.getBillNo());
            billDetails.setOrderNo(orderRefund.getOrderNo());
            if (billType==1){
                billDetails.setOrderRefundTotalAmount(orderRefund.getOriginalRefundAmount());
            }
            if (billType==2){
                billDetails.setOrderRefundTotalAmount(orderRefund.getPlatformRefundAmount());
            }
            OrderItem orderItem = orderItemMap.get(orderRefund.getOrderId());
            if (ObjectUtil.isNotNull(orderItem)){
                billDetails.setTenantId(orderItem.getTenantId());
                billDetails.setSupperTenantId(orderItem.getSupplierTenantId());
            billDetails.setOrderRefundNo(orderRefund.getOrderRefundNo());
            billDetails.setProductSkuCode(orderItem.getProductSkuCode());
                billDetails.setSupportedLogistics(orderItem.getLogisticsType().name());
                OrderItemPrice orderItemPrice = orderItemPriceMap.get(orderItem.getId());
                if (ObjectUtil.isNotNull(orderItemPrice)){
                    if (billType==1){
                        billDetails.setUnitPrice(orderItemPrice.getOriginalUnitPrice());
                    }
                    if (billType==2){
                        billDetails.setUnitPrice(orderItemPrice.getPlatformUnitPrice());
                    }
                }
            }
            billDetails.setOrderStatus(2);
            billDetails.setDelFlag(0);
            billDetails.setCreateTime(new Date());
            billDetails.setUpdateTime(new Date());
            if (ObjectUtil.isNotNull(LoginHelper.getTenantId())){
                billDetails.setCreateBy(LoginHelper.getUserId());
                billDetails.setUpdateBy(LoginHelper.getUserId());
            }
            //订单总金额
            Orders orders1 = ordersMap.get(orderRefund.getOrderId());
            if (ObjectUtil.isNull(orders1)){
                throw new RuntimeException("[账单模块]未匹配到订单信息"+ JsonUtils.toJsonString(orderItem));
            } else{
                billDetails.setChannelOrderNo(orders1.getChannelOrderNo());
                billDetails.setOrderExtendId(orders1.getOrderExtendId());
                billDetails.setProductQuantity(orders1.getTotalQuantity());
                billDetails.setOrderPaymentMethods(orders1.getPayType());
                billDetails.setOrderTime(orders1.getCreateTime());
                billDetails.setOrderTotalAmount(orders1.getOriginalActualTotalAmount());
                //供应商全部统计
                if (billType==1){
                    if (ObjectUtil.isNotNull(orders1.getOriginalPayableTotalAmount()) && ObjectUtil.isNotNull(orders1.getTotalQuantity())){
                        billDetails.setProductSkuPrice(orders1.getOriginalPayableTotalAmount().divide(BigDecimal.valueOf(orders1.getTotalQuantity()),2, RoundingMode.HALF_UP));
                    }
                    //操作费
                    billDetails.setOperationFee(orders1.getOriginalTotalOperationFee());
                    //尾程派送费
                    billDetails.setFinalDeliveryFee(orders1.getOriginalTotalFinalDeliveryFee());
                }
                //分销商 只统计发货总金额/总数量/退货总金额/总数量
                if (billType==2){
                    if (ObjectUtil.isNotNull(orders1.getOriginalActualTotalAmount())){
                        billDetails.setProductSkuPrice(orders1.getOriginalActualTotalAmount().divide(BigDecimal.valueOf(orders1.getTotalQuantity()),2, RoundingMode.HALF_UP));
                    }
                    //操作费
                    billDetails.setOperationFee(orders1.getPlatformTotalOperationFee());
                    //尾程派送费
                    billDetails.setFinalDeliveryFee(orders1.getPlatformTotalFinalDeliveryFee());
                }

            }
            billDetailsList.add(billDetails);
        }
        billDetailsSqlInsert(billDetailsList);
        return billDetailsList;
    }

    /**
     * @description: 清洗供应商历史账单表头数据
     * @author: Len
     * @date: 2024/9/10 15:32
     * @param: bill
     * @param: billHead
     **/
    public void dealBillDetails(Bill bill, BillHead billHead) {
        LambdaQueryWrapper<BillRelation> q = new LambdaQueryWrapper<>();
        q.eq(BillRelation::getDelFlag,0);
        q.eq(BillRelation::getBillId,bill.getId());
        Long total = TenantHelper.ignore(()->billRelationMapper.selectCount(q));
        final int pageSize = 5000;
        // 计算总页数
        int totalPages = (int) Math.ceil((double) total / pageSize);
        // 分页查询
        for (int i = 1; i <= totalPages; i++) {
//            if (i==totalPages){
//                throw new RuntimeException("测试事务");
//            }
            Page<BillRelation> page = new Page<>(i, pageSize);
            List<BillRelation> billRelations = billRelationMapper.selectPage(page, q).getRecords();
            // 处理数据
            Set<Long> ids = billRelations.stream()
                                         .map(BillRelation::getId)
                                         .collect(Collectors.toSet());
            LambdaQueryWrapper<BillRelationDetail> dd = new LambdaQueryWrapper<>();
            dd.eq(BillRelationDetail::getDelFlag,0);
            dd.in(BillRelationDetail::getBillRelationId,ids);
            List<BillRelationDetail> billRelationDetails = billRelationDetailMapper.selectList(dd);
            Map<Long, List<BillRelationDetail>> billRelationDetailMap = billRelationDetails.stream()
                                                                                           .collect(Collectors.groupingBy(BillRelationDetail::getBillRelationId));
            List<BillDetails> billDetailsList=new ArrayList<>();
            billRelations.forEach(s->{
                BillDetails billDetails=new BillDetails();
                billDetails.setId(IdUtil.getSnowflakeNextId());
                billDetails.setBillId(billHead.getId());
                billDetails.setBillNo(billHead.getBillNo());
                if (s.getRelationType().equals(RelationTypeEnum.OrderItem)){
                    billDetails.setOrderStatus(1);
                }else if (s.getRelationType().equals(RelationTypeEnum.OrderRefund)){
                    billDetails.setOrderStatus(2);
                }
                List<BillRelationDetail> billRelationDetails1 = billRelationDetailMap.get(s.getId());
                billRelationDetails1.forEach(sr->{
                    switch (sr.getFieldType()){
                        case OrderNo:
                            billDetails.setOrderNo(sr.getFieldValue());
                            break;
                        case OrderRefundNo:
                            billDetails.setOrderRefundNo(sr.getFieldValue());
                            break;
                        case ItemNo:
                            billDetails.setProductSkuCode(sr.getFieldValue());
                            break;
                        case ProductQuantity:
                            billDetails.setProductQuantity(Integer.valueOf(sr.getFieldValue()));
                            break;
                        case ProductAmount:
                            billDetails.setProductSkuPrice(fromString(sr.getFieldValue()));
                            break;
                        case OperationFee:
                            billDetails.setOperationFee(fromString(sr.getFieldValue()));
                            break;
                        case FinalDeliveryFee:
                            billDetails.setFinalDeliveryFee(fromString(sr.getFieldValue()));
                            break;
                        case TotalAmount:
                            billDetails.setOrderTotalAmount(fromString(sr.getFieldValue()));
                            break;
                        case CreateDateTime:
                            billDetails.setOrderTime(DateUtil.parse(sr.getFieldValue(), "yyyy-MM-dd HH:mm:ss"));
                            break;
                    }
                    billDetails.setDelFlag(Integer.valueOf(sr.getDelFlag()));
                    billDetails.setCreateBy(sr.getCreateBy());
                    billDetails.setCreateTime(sr.getCreateTime());
                    billDetails.setUpdateBy(sr.getUpdateBy());
                    billDetails.setUpdateTime(sr.getUpdateTime());

                });
                billDetailsList.add(billDetails);
            });
            //处理分销商/租户ID
            Set<String> orderNos = billDetailsList.stream()
                                                  .map(BillDetails::getOrderNo)
                                                  .collect(Collectors.toSet());
            LambdaQueryWrapper<OrderItem> q1 = new LambdaQueryWrapper<>();
            q1.eq(OrderItem::getDelFlag,0);
            q1.in(CollectionUtil.isNotEmpty(orderNos),OrderItem::getOrderNo,orderNos);
            List<OrderItem> orderItems = orderItemMapper.selectListNotTenant(q1);
            Map<String, OrderItem> orderItemMap = orderItems.stream()
                                                            .collect(Collectors.toMap(OrderItem::getOrderNo, item -> item));
            billDetailsList.forEach(ss->{
                OrderItem orderItem = orderItemMap.get(ss.getOrderNo());
                if (ObjectUtil.isNotNull(orderItem)){
                    ss.setTenantId(orderItem.getTenantId());
                    ss.setSupperTenantId(orderItem.getSupplierTenantId());
                }
            });
            //billDetailsMapper.insertBatch(billDetailsList,5000);
            billDetailsMapper.batchInsertBillDetails(billDetailsList);
        }
    }

    /**
     * @description: 清洗供应商历史账单明细数据
     * @author: Len
     * @date: 2024/9/10 15:32
     * @param: bill
     * @return: com.zsmall.system.entity.domain.BillHead
     **/
    public BillHead dealBillHead(Bill bill){
        BillHead billHead=new BillHead();
        billHead.setPreviousBillId(bill.getPreviousBillId());
        billHead.setId(bill.getId());
        billHead.setBillType(1);
        billHead.setDelFlag(Integer.valueOf(bill.getDelFlag()));
        billHead.setSettlementState(bill.getBillState().getValue());
        billHead.setWithdrawalState(bill.getWithdrawalState().getValue());
        billHead.setTenantId(bill.getTenantId());
        billHead.setBillNo(bill.getBillNo());
        billHead.setBillStartTime(Date.from(bill.getSettlementCycleBegin().atZone(ZoneId.systemDefault()).toInstant()));
        billHead.setBillEndTime(Date.from(bill.getSettlementCycleEnd().atZone(ZoneId.systemDefault()).toInstant()));
        billHead.setCreateBy(bill.getCreateBy());
        billHead.setCreateTime(bill.getCreateTime());
        billHead.setUpdateBy(bill.getUpdateBy());
        billHead.setUpdateTime(bill.getUpdateTime());
        //查询
        LambdaQueryWrapper<BillAbstract> ba = new LambdaQueryWrapper<>();
        ba.eq(BillAbstract::getDelFlag,0);
        ba.eq(BillAbstract::getBillId,bill.getId());
        List<BillAbstract> billAbstracts= TenantHelper.ignore(()->billAbstractMapper.selectList(ba));
        for (BillAbstract billAbstract : billAbstracts) {
            switch (billAbstract.getAbstractType()){
                //收入
                case Income:
                    billHead.setTotalShippedAmount(billAbstract.getAbstractTotalAmount());
                    break;
                case Expenditure :
                    billHead.setTotalRefundAmount(billAbstract.getAbstractTotalAmount());
                    break;
            }
        }
        Set<Long> ids = billAbstracts.stream()
                                     .map(BillAbstract::getId)
                                     .collect(Collectors.toSet());
        LambdaQueryWrapper<BillAbstractDetail> bat = new LambdaQueryWrapper<>();
        bat.eq(BillAbstractDetail::getDelFlag,0);
        bat.in(BillAbstractDetail::getBillAbstractId,ids);
        List<BillAbstractDetail> billAbstractDetails =  TenantHelper.ignore(()->billAbstractDetailMapper.selectList(bat));
        billAbstractDetails.forEach(s->{
            switch (s.getFieldType()){
                case Total_ProductAmount:
                    billHead.setTotalProductAmount(s.getFieldValue());
                    break;
                case Total_OperationFee:
                    billHead.setTotalOperfeeAmount(s.getFieldValue());
                    break;
                case Total_FinalDeliveryFee:
                    billHead.setTotalDeliveryFeeAmount(s.getFieldValue());
                    break;
                case Total_RefundAmount:
                    billHead.setTotalRefundAmount(s.getFieldValue());
                    break;
            }
        });
        //本地订单总金额
        billHead.setCurrentOrderAmount(billHead.getTotalShippedAmount().subtract(billHead.getTotalRefundAmount()));
        //本期循环保证金
        billHead.setCurrentRevolvingOrderAmount(billHead.getCurrentOrderAmount().multiply(BigDecimal.valueOf(0.2)));
        if (ObjectUtil.isNotNull(bill.getPreviousBillId())){
            Bill bill1 = TenantHelper.ignore(()->billMapper.selectById(bill.getPreviousBillId()));
            //上期订单总金额
            if (ObjectUtil.isNotNull(bill1)){
                BigDecimal currentIncome = ObjectUtil.isNull(bill1.getCurrentIncome())?BigDecimal.ZERO:bill1.getCurrentIncome();
                BigDecimal currentExpenditure = bill1.getCurrentExpenditure()==null?BigDecimal.ZERO:bill1.getCurrentExpenditure();
                billHead.setPreviousOrderAmount(currentIncome.subtract(currentExpenditure));
            }
        }
        //上期循环保证金
        billHead.setPreviousRevolvingOrderAmount(billHead.getPreviousOrderAmount().multiply(BigDecimal.valueOf(0.2)));
        //本期总金额
        billHead.setTotalAmount(billHead.getTotalShippedAmount().subtract(billHead.getTotalRefundAmount())
                                        .subtract(billHead.getCurrentRevolvingOrderAmount())
                                        .add(billHead.getPreviousRevolvingOrderAmount()));
        billHeadMapper.insert(billHead);
        return billHead;
    }


    /**
     * @description: 生成供应商账单信息
     * @author: Len
     * @date: 2024/9/10 16:57
     * @param: isJob 是否是定时任务
     * @param: tenantId 供应商租户ID
     * @param: startTimeStr 开始时间月/日
     * @param: endTimeStr 结束时间月/日
     * @param: startTime 开始时间
     * @param: endTime  结束时间
     **/
    public void generatedSupplierBillByTenant(Object tenantId,Boolean isJob,String startTimeStr, String endTimeStr,String startTime,String endTime) {
        List<SiteCountryCurrencyVo> list = siteCountryCurrencyMapper.getCurrencyList();
        list.forEach(s->{
        BillHead billHead = new BillHead();
            billHead.setCurrencyCode(s.getCurrencyCode());
            billHead.setCurrencySymbol(s.getCurrencySymbol());
            billHead.setCountryCode(s.getCountryCode());
        billHead.setTenantId(String.valueOf(tenantId));
        //构建表头
        if (isJob){
            String year = String.valueOf(DateUtil.year(DateUtil.date()));
            String[] billDates = getBillDates(DateUtil.dayOfMonth(new Date()));
            billHead.setBillNo(tenantId +s.getCurrencyCode()+year+ billDates[0] + billDates[1]);
            billHead.setBillStartTime(DateUtil.parse(billDates[2]));
            billHead.setBillEndTime(DateUtil.parse(billDates[3]));
        }else {
            //年份来自输入的
            DateTime parse = DateUtil.parse(startTime);
            String year = String.valueOf(DateUtil.year(parse));
            billHead.setBillNo(tenantId+s.getCurrencyCode()+year+startTimeStr+endTimeStr);
            billHead.setBillStartTime(DateUtil.parse(startTime));
            billHead.setBillEndTime(DateUtil.parse(endTime));
        }
        billHead.setBillType(1);
        billHead.setSettlementState(1);
        billHead.setWithdrawalState(0);
        billHead.setDelFlag(0);
        billHead.setId(IdUtil.getSnowflakeNextId());
        //查询当前租户上期的账单ID
        LambdaQueryWrapper<BillHead> b = new LambdaQueryWrapper<>();
        b.eq(BillHead::getTenantId, tenantId);
        b.eq(BillHead::getDelFlag,0);
        b.orderByDesc(BillHead::getId);
        b.last("limit 1");
        BillHead billHead1 = TenantHelper.ignore(()->billHeadMapper.selectOne(b));
        if (ObjectUtil.isNotNull(billHead1)) {
            billHead.setPreviousBillId(billHead1.getId());
            //上期订单总金额
            billHead.setPreviousOrderAmount(billHead.getCurrentOrderAmount());
            //上期循环保证金
            billHead.setCurrentRevolvingOrderAmount(billHead.getCurrentRevolvingOrderAmount());
        }
        TenantHelper.ignore(() -> billHeadMapper.insert(billHead));
        //分页查询交易记录表
        int total = TenantHelper.ignore(() -> billDetailsMapper.selectCountOrdersByTenant(billHead.getBillStartTime()
                , billHead.getBillEndTime(), String.valueOf(tenantId),billHead.getBillType(),s.getCurrencyCode()));
        if (total == 0) {
            return ;
        }
        // 一次查询的数据量
        int pageSize = 5000;
        // 总页数
        int totalPage = total % pageSize == 0 ? total / pageSize : (total / pageSize + 1);
        //发货总数量
        int totalShippedQuantity = 0;
        //退货总数量
        int totalRefundQuantity=0;
        //退款总金额(本期支出)
        BigDecimal totalRefundAmount=BigDecimal.ZERO;
        //产品总金额
        BigDecimal totalProductAmount = BigDecimal.ZERO;
        //本期订单总金额
        BigDecimal currentOrderAmount = BigDecimal.ZERO;
        //操作费总金额
        BigDecimal totalOperfeeAmount = BigDecimal.ZERO;
        //尾程派送费总金额
        BigDecimal totalDeliveryFeeAmount = BigDecimal.ZERO;
        for (int currentPage = 1; currentPage <= totalPage; currentPage++) {
            // 计算当前页的起始行
            int startRow = (currentPage - 1) * pageSize;
            // 如果是最后一页且不足pageSize条数据，调整pageSize为剩余的数据量
            if (currentPage == totalPage && total % pageSize != 0) {
                pageSize = total % pageSize;
            }
            int finalPageSize = pageSize;
            List<OrderItemSystem> selectOrdersByTenant = TenantHelper.ignore(() ->
                billDetailsMapper.selectOrdersByTenant(billHead.getBillStartTime(), billHead.getBillEndTime(),
                        String.valueOf(tenantId), startRow, finalPageSize,billHead.getBillType(),s.getCurrencyCode()));
            if (CollectionUtil.isNotEmpty(selectOrdersByTenant)){
                //处理收货单
                List<BillDetails> billDetailsList = dealBillOrderItem(selectOrdersByTenant, billHead, billHead.getBillType());
                //汇总
                for (BillDetails billDetails : billDetailsList) {
                    totalShippedQuantity = totalShippedQuantity + billDetails.getProductQuantity();
                    //本期订单总金额
                    currentOrderAmount = currentOrderAmount.add(billDetails.getOrderTotalAmount());
                    //1 供应商 2分销商
                    if (billHead.getBillType() == 1) {
                        //产品总金额
                        totalProductAmount = totalProductAmount.add(billDetails.getProductSkuPrice());
                        //操作费
                        totalOperfeeAmount = totalOperfeeAmount.add(billDetails.getOperationFee());
                        //围城派送费
                        totalDeliveryFeeAmount = totalDeliveryFeeAmount.add(billDetails.getFinalDeliveryFee());
                    }
                }
                //处理退款单-查询已支付订单里面退款的订单
                Set<String> uniqueOrderIds = billDetailsList.stream()
                                                            .map(BillDetails::getOrderNo)
                                                            .collect(Collectors.toSet());
                LambdaQueryWrapper<OrderRefund> oo = new LambdaQueryWrapper<>();
                oo.in(OrderRefund::getOrderNo, uniqueOrderIds);
//                oo.eq(OrderRefund::getRefundType, "Refund");
                oo.eq(OrderRefund::getRefundAmountState, "Refunded");
                oo.eq(OrderRefund::getDelFlag,0);
                List<OrderRefund> orderRefunds = TenantHelper.ignore(() -> orderRefundMapper.selectList(oo));
                if (CollectionUtil.isNotEmpty(orderRefunds)){
                    //处理退款单
                    List<BillDetails> billDetailsRefund = dealBillOrderRefundItem(orderRefunds, billHead, billHead.getBillType());
                    for (BillDetails billDetails : billDetailsRefund) {
                        //退货数量
                        totalRefundQuantity=totalRefundQuantity+billDetails.getProductQuantity();
                        //退款金额
                        totalRefundAmount=totalRefundAmount.add(billDetails.getOrderRefundTotalAmount());
                    }
                }
            }
        }

        //订单总金额
        billHead.setCurrentOrderAmount(currentOrderAmount);
        //退货总数量
        billHead.setTotalRefundQuantity(totalRefundQuantity);
        //发货总数量
        billHead.setTotalShippedQuantity(totalShippedQuantity);
        //本期收入
        billHead.setTotalShippedAmount(totalOperfeeAmount.add(totalDeliveryFeeAmount).add(totalProductAmount));
        //本期支出
        billHead.setTotalRefundAmount(totalRefundAmount);
        if (billHead.getBillType() == 1){
            //操作费
            billHead.setTotalOperfeeAmount(totalOperfeeAmount);
            //尾程
            billHead.setTotalDeliveryFeeAmount(totalDeliveryFeeAmount);
            //产品总金额
            billHead.setTotalProductAmount(totalProductAmount);
            //本期订单总金额
            billHead.setCurrentOrderAmount(billHead.getTotalShippedAmount().subtract(billHead.getTotalRefundAmount()));
            //本期循环保证金
            billHead.setCurrentRevolvingOrderAmount(billHead.getCurrentOrderAmount().multiply(BigDecimal.valueOf(0.2)));
            //本期总金额
            billHead.setTotalAmount(billHead.getTotalShippedAmount().subtract(billHead.getTotalRefundAmount())
                                            .subtract(billHead.getCurrentRevolvingOrderAmount()).add(billHead.getPreviousRevolvingOrderAmount()));
        }
        TenantHelper.ignore(()->{
            billHeadMapper.updateById(billHead);
        });
        });
    }


    /**
     * @description: 生成分销商账单
     * @author: Len
     * @date: 2024/9/10 16:53
     * @param: isJob 是否是JOB任务
     * @param: tenantId 供应商租户ID
     * @param: startTimeStr 开始时间月/日
     * @param: endTimeStr 结束时间月/日
     * @param: startTime 开始时间
     * @param: endTime  结束时间
     **/
    public void generatedDistributorBillByTenant( Object tenantId,Boolean isJob,String startTimeStr, String endTimeStr,String startTime,String endTime) {
        List<SiteCountryCurrencyVo> list = siteCountryCurrencyMapper.getCurrencyList();
        list.forEach(s->{
        //月份从0开始
        BillHead billHead=new BillHead();
            billHead.setCurrencyCode(s.getCurrencyCode());
            billHead.setCurrencySymbol(s.getCurrencySymbol());
            billHead.setCountryCode(s.getCountryCode());
        billHead.setTenantId(String.valueOf(tenantId));

        if (isJob){
            //0205新增年份拼接
            String year = String.valueOf(DateUtil.year(DateUtil.date()));
            String firstDayStr = DateUtils.getLastMonthFirstDayFormatted();
            String lastDayStr = DateUtils.getLastMonthLastDayFormatted();
            String firstDayStartStr = DateUtils.getLastMonthFirstDayStartFormatted();
            String firstDayEndStr = DateUtils.getLastMonthFirstDayEndFormatted();
            billHead.setBillNo(tenantId +s.getCurrencyCode()+year+ firstDayStr + lastDayStr);
            billHead.setBillStartTime(DateUtil.parse(firstDayStartStr, "yyyy-MM-dd HH:mm:ss"));
            billHead.setBillEndTime(DateUtil.parse(firstDayEndStr, "yyyy-MM-dd HH:mm:ss"));
        }else {
            //年份来自输入的
            DateTime parse = DateUtil.parse(startTime);
            String year = String.valueOf(DateUtil.year(parse));
            billHead.setBillNo(tenantId +s.getCurrencyCode()+year+ startTimeStr + endTimeStr);
            billHead.setBillStartTime(DateUtil.parse(startTime, "yyyy-MM-dd HH:mm:ss"));
            billHead.setBillEndTime(DateUtil.parse(endTime, "yyyy-MM-dd HH:mm:ss"));
        }
        billHead.setBillType(2);
        billHead.setBillStatus(1);
        billHead.setId(IdUtil.getSnowflakeNextId());
        billHead.setDelFlag(0);
        TenantHelper.ignore(()->billHeadMapper.insert(billHead));
        //分页查询交易记录表
        int total = TenantHelper.ignore(() -> billDetailsMapper.selectCountOrdersByTenant(billHead.getBillStartTime()
                , billHead.getBillEndTime(), String.valueOf(tenantId),billHead.getBillType(),s.getCurrencyCode()));
        if (total == 0) {
            return;
        }
        // 一次查询的数据量
        int pageSize = 5000;
        // 总页数
        int totalPage = total % pageSize == 0 ? total / pageSize : (total / pageSize + 1);
        //发货总数量
        int totalShippedQuantity = 0;
        //退货总数量
        int totalRefundQuantity=0;
        //发货总金额(本期收入)=应付金额总和
        BigDecimal totalShippedAmount = BigDecimal.ZERO;
        //退款总金额(本期支出)
        BigDecimal totalRefundAmount=BigDecimal.ZERO;
        //产品总金额
        BigDecimal totalProductAmount = BigDecimal.ZERO;
        for (int currentPage = 1; currentPage <= totalPage; currentPage++) {
            // 计算当前页的起始行
            int startRow = (currentPage - 1) * pageSize;
            // 如果是最后一页且不足pageSize条数据，调整pageSize为剩余的数据量
            if (currentPage == totalPage && total % pageSize != 0) {
                pageSize = total % pageSize;
            }
            int finalPageSize = pageSize;
            List<OrderItemSystem> selectOrdersByTenant = TenantHelper.ignore(() ->
                billDetailsMapper.selectOrdersByTenant(billHead.getBillStartTime(), billHead.getBillEndTime(),
                        String.valueOf(tenantId), startRow, finalPageSize,billHead.getBillType(),s.getCurrencyCode()));
            if (CollectionUtil.isNotEmpty(selectOrdersByTenant)){
                //处理收货单
                List<BillDetails> billDetailsList = dealBillOrderItem(selectOrdersByTenant, billHead, billHead.getBillType());
                //汇总
                for (BillDetails billDetails : billDetailsList) {
                    totalShippedQuantity = totalShippedQuantity + billDetails.getProductQuantity();
                    //产品总金额(本期收入)
                    totalProductAmount = totalProductAmount.add(billDetails.getProductSkuPrice());
                    //发货总金额
                    totalShippedAmount=totalShippedAmount.add(billDetails.getOrderTotalAmount());
                }
                //处理退款单-查询已支付订单里面退款的订单
                Set<String> uniqueOrderIds = billDetailsList.stream()
                                                            .map(BillDetails::getOrderNo)
                                                            .collect(Collectors.toSet());
                LambdaQueryWrapper<OrderRefund> oo = new LambdaQueryWrapper<>();
                oo.in(OrderRefund::getOrderNo, uniqueOrderIds);
//                oo.eq(OrderRefund::getRefundType, "Refund");
                oo.eq(OrderRefund::getRefundAmountState, "Refunded");
                oo.eq(OrderRefund::getDelFlag,0);
                List<OrderRefund> orderRefunds = TenantHelper.ignore(() -> orderRefundMapper.selectList(oo));
                if (CollectionUtil.isNotEmpty(orderRefunds)) {
                    //处理退款单
                    List<BillDetails> billDetailsRefund = dealBillOrderRefundItem(orderRefunds, billHead, billHead.getBillType());
                    for (BillDetails billDetails : billDetailsRefund) {
                        //退货数量
                        totalRefundQuantity=totalRefundQuantity+billDetails.getProductQuantity();
                        //退款金额
                        totalRefundAmount=totalRefundAmount.add(billDetails.getOrderRefundTotalAmount());
                    }
                }

            }
        }
        billHead.setTotalRefundQuantity(totalRefundQuantity);
        billHead.setTotalRefundAmount(totalRefundAmount);
        billHead.setTotalShippedQuantity(totalShippedQuantity);
        //产品总金额
        billHead.setTotalProductAmount(totalProductAmount);
        billHead.setTotalShippedAmount(totalShippedAmount);
            //合计金额
        billHead.setTotal(totalShippedAmount.subtract(totalRefundAmount));
        TenantHelper.ignore(()->{
            billHeadMapper.updateById(billHead);
        });
        });
    }

    public static BigDecimal fromString(String value) {
        return retainTwoDecimals(new BigDecimal(value));
    }

    public static BigDecimal retainTwoDecimals(BigDecimal value) {
        if (value == null) {
            return null;
        }
        // 如果已经小于1，则不需要调用setScale，直接返回
        if (value.compareTo(BigDecimal.ONE) < 0) {
            return value;
        }
        // 保留两位小数，使用HALF_UP模式进行四舍五入
        return value.setScale(2, RoundingMode.HALF_UP);
    }

    static String[]  getBillDates(int type){
        String startTime = null;
        String endTime=null;
        if (type==1){
            //上个月的16号到最后一天
            endTime = DateUtils.getLastMonthFirstDayEndFormatted();
            DateTime dateTime = DateUtil.parse(endTime);
            int year = DateUtil.year(dateTime);
            int month = DateUtil.month(dateTime)+1;
            String strMon=null;
            if (month<10){
                strMon="0"+month;
            }else {
                strMon= String.valueOf(month);
            }
            startTime=year+"-"+strMon+"-16"+" 00:00:00";
        }else if (type==16){
            //当前月1号到15号
            DateTime date = DateUtil.date();
            int year = DateUtil.year(date);
            int month = DateUtil.month(date)+1;
            String strMon=null;
            if (month<10){
                strMon="0"+month;
            }else {
                strMon= String.valueOf(month);
            }
            endTime=year+"-"+strMon+"-15"+" 23:59:59";
            startTime=year+"-"+strMon+"-01"+" 00:00:00";
        }
        String startMonth = String.valueOf(DateUtil.month(DateUtil.parse(startTime)) + 1);
        String startDay = String.valueOf(DateUtil.dayOfMonth(DateUtil.parse(startTime)));
        String endMonth = String.valueOf(DateUtil.month(DateUtil.parse(endTime)) + 1);
        String endDay = String.valueOf(DateUtil.dayOfMonth(DateUtil.parse(endTime)));
        if (Integer.valueOf(startMonth)<10 ){
            startMonth="0"+startMonth;
        }
        if (Integer.valueOf(endMonth)<10 ){
            endMonth="0"+endMonth;
        }
        if (Integer.valueOf(startDay)<10){
            startDay="0"+startDay;
        }
        if (Integer.valueOf(endDay)<10){
            endDay="0"+endDay;
        }
        System.out.println(startMonth+startDay);
        System.out.println(endMonth+endDay);
        System.out.println(startTime);
        System.out.println(endTime);
        String[] dateStr=new String[]{startMonth+startDay,endMonth+endDay,startTime,endTime};
        return dateStr;
    }

    /**
     * @description: 生成分销商钱包账单,
     * @author: Len
     * @date: 2024/9/23 16:53
     * @param: isJob 是否是JOB任务
     * @param: tenantId 供应商租户ID
     * @param: startTimeStr 开始时间月/日
     * @param: endTimeStr 结束时间月/日
     * @param: startTime 开始时间
     * @param: endTime  结束时间
     **/
    public void generatedTransactionReceipt(Object tenantId,Boolean isJob,String startTimeStr, String endTimeStr,String startTime,String endTime) {
        //查询所有的站点
        List<SiteCountryCurrencyVo> currencyList = siteCountryCurrencyMapper.getCurrencyList();
        currencyList.forEach(s->{
        BillTransactionReceipt billTransactionReceipt=new BillTransactionReceipt();
        billTransactionReceipt.setTenantId(String.valueOf(tenantId));
            billTransactionReceipt.setCurrencyCode(s.getCurrencyCode());
            billTransactionReceipt.setCurrencySymbol(s.getCurrencySymbol());
        if (isJob){
            //0205新增年份拼接
            String year = String.valueOf(DateUtil.year(DateUtil.date()));
            String firstDayStr = DateUtils.getLastMonthFirstDayFormatted();
            String lastDayStr = DateUtils.getLastMonthLastDayFormatted();
            String firstDayStartStr = DateUtils.getLastMonthFirstDayStartFormatted();
            String firstDayEndStr = DateUtils.getLastMonthFirstDayEndFormatted();
            billTransactionReceipt.setBillNo(tenantId +s.getCurrencyCode()+year+ firstDayStr + lastDayStr);
            billTransactionReceipt.setBillStartTime(DateUtil.parse(firstDayStartStr, "yyyy-MM-dd HH:mm:ss"));
            billTransactionReceipt.setBillEndTime(DateUtil.parse(firstDayEndStr, "yyyy-MM-dd HH:mm:ss"));
        }else {
            //年份来自输入的
            DateTime parse = DateUtil.parse(startTime);
            String year = String.valueOf(DateUtil.year(parse));
            billTransactionReceipt.setBillNo(tenantId +s.getCurrencyCode()+year+ startTimeStr + endTimeStr);
            billTransactionReceipt.setBillStartTime(DateUtil.parse(startTime, "yyyy-MM-dd HH:mm:ss"));
            billTransactionReceipt.setBillEndTime(DateUtil.parse(endTime, "yyyy-MM-dd HH:mm:ss"));
        }
        LocalDate localDate = billTransactionReceipt.getBillStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        YearMonth yearMonth = YearMonth.from(localDate);
        //当月充值
            BigDecimal rechargeTotalAmount = TenantHelper.ignore(()-> transactionRecordMapper.sumTotalAmountBySubType(tenantId,s.getCurrencyCode(), List.of(TransactionSubTypeEnum.Recharge,TransactionSubTypeEnum.PlatformRemit),yearMonth.toString()));
        rechargeTotalAmount=ObjectUtil.isNull(rechargeTotalAmount)?BigDecimal.ZERO:rechargeTotalAmount;
        billTransactionReceipt.setRechargeTotalAmount(rechargeTotalAmount);
        //当月消费
            BigDecimal expenditureTotalAmount =  TenantHelper.ignore(()->transactionRecordMapper.sumTotalAmountBySubType(tenantId,s.getCurrencyCode(), List.of(TransactionSubTypeEnum.OrderPay,TransactionSubTypeEnum.PlatformDeduct),yearMonth.toString()));
        expenditureTotalAmount=ObjectUtil.isNull(expenditureTotalAmount)?BigDecimal.ZERO:expenditureTotalAmount;
            BigDecimal orderRefundTotalAmount =  TenantHelper.ignore(()->transactionRecordMapper.sumTotalAmountBySubType(tenantId,s.getCurrencyCode(), List.of(TransactionSubTypeEnum.OrderRefund),yearMonth.toString()));
        orderRefundTotalAmount=ObjectUtil.isNull(orderRefundTotalAmount)?BigDecimal.ZERO:orderRefundTotalAmount;
        billTransactionReceipt.setExpenditureTotalAmount(expenditureTotalAmount.subtract(orderRefundTotalAmount));
        //先查询当前租户是否有交易记录
        LambdaQueryWrapper<TransactionRecord> tt = new LambdaQueryWrapper<>();
        tt.eq(TransactionRecord::getTenantId,tenantId);
        tt.eq(TransactionRecord::getCurrency,s.getCurrencyCode());
        tt.eq(TransactionRecord::getTransactionState, TransactionStateEnum.Success.name());
        tt.eq(TransactionRecord::getDelFlag,0);
        tt.le(TransactionRecord::getCreateTime,billTransactionReceipt.getBillEndTime());
        Long ignore = TenantHelper.ignore(() -> transactionRecordMapper.selectCount(tt));
        if (ignore==0){
            billTransactionReceipt.setWalletBalanceMonthStart(BigDecimal.ZERO);
            billTransactionReceipt.setWalletBalanceMonthEnd(BigDecimal.ZERO);
        }else {
            //查询当前租户在之前月份是否有账单
            LambdaQueryWrapper<BillTransactionReceipt> bb = new LambdaQueryWrapper<>();
            bb.eq(BillTransactionReceipt::getTenantId,tenantId);
            bb.eq(BillTransactionReceipt::getCurrencyCode,s.getCurrencyCode());
            boolean exists = billTransactionReceiptMapper.exists(bb);
            BillTransactionReceipt billTransactionReceiptDB=new BillTransactionReceipt();
            if (exists) {
                //如果存在，取上个月的钱包余额
                LambdaQueryWrapper<BillTransactionReceipt> trs = new LambdaQueryWrapper<>();
                trs.eq(BillTransactionReceipt::getTenantId, tenantId);
                trs.eq(BillTransactionReceipt::getCurrencyCode,s.getCurrencyCode());
                trs.orderByDesc(BillTransactionReceipt::getId);
                trs.last("limit 1");
                billTransactionReceiptDB= billTransactionReceiptMapper.selectOne(trs);
            }
            //获取月初钱包余额
                BigDecimal walletBalanceMonthStart = getWalletBalanceMonthStart(String.valueOf(tenantId), yearMonth,exists,billTransactionReceiptDB,s.getCurrencyCode());
            billTransactionReceipt.setWalletBalanceMonthStart(walletBalanceMonthStart);
            //月末钱包余额
                BigDecimal walletBalanceMonthEnd = getWalletBalanceMonthEnd(String.valueOf(tenantId), yearMonth,exists,billTransactionReceiptDB,s.getCurrencyCode());
            billTransactionReceipt.setWalletBalanceMonthEnd(walletBalanceMonthEnd);
        }
        //查询交易记录
        billTransactionReceiptMapper.insert(billTransactionReceipt);
        });
    }
    /**
     * @description: 获取月初钱包余额
     * @author: Len
     * @date: 2024/9/26 09:47
     * @param: tenantId
     * @param: billStartTime
     * @return: java.math.BigDecimal
     **/
    public  BigDecimal getWalletBalanceMonthStart(String tenantId,YearMonth yearMonth,Boolean exists,BillTransactionReceipt billTransactionReceipt,String countryCode){
        YearMonth finalYearMonth = yearMonth;
        //先查询当月的月初
        BigDecimal walletBalanceMonthStart =  TenantHelper.ignore(()->transactionRecordMapper.selectWalletBalanceMonthStart(String.valueOf(tenantId), finalYearMonth.toString(),countryCode));
        if (ObjectUtil.isNull(walletBalanceMonthStart)) {
            if (exists) {
                if (ObjectUtil.isNotNull(billTransactionReceipt)) {
                    return billTransactionReceipt.getWalletBalanceMonthStart();
                }
            }else {
                //循环查找上个月的月末金额直到查到为止
                while (ObjectUtil.isNull(walletBalanceMonthStart)) {
                    yearMonth = yearMonth.minusMonths(1);
                    YearMonth finalYearMonth1 = yearMonth;
                    walletBalanceMonthStart = TenantHelper.ignore(() -> transactionRecordMapper.selectWalletBalanceMonthEnd(String.valueOf(tenantId), finalYearMonth1.toString(),countryCode));
                }
            }
        }
        return walletBalanceMonthStart;
    }
    /**
     * @description: 获取月末钱包余额
     * @author: Len
     * @date: 2024/9/26 09:47
     * @param: tenantId
     * @param: billStartTime
     * @return: java.math.BigDecimal
     **/
    public  BigDecimal getWalletBalanceMonthEnd(String tenantId,YearMonth yearMonth,Boolean exists,BillTransactionReceipt billTransactionReceipt,String countryCode){
        YearMonth finalYearMonth = yearMonth;
        //查询当月的月末
        BigDecimal walletBalanceMonthStart =  TenantHelper.ignore(()->transactionRecordMapper.selectWalletBalanceMonthEnd(String.valueOf(tenantId), finalYearMonth.toString(),countryCode));
        if (ObjectUtil.isNull(walletBalanceMonthStart)){
            if (exists) {
                if (ObjectUtil.isNotNull(billTransactionReceipt)) {
                    return billTransactionReceipt.getWalletBalanceMonthStart();
                }
            }else {
                //循环查找上个月的月末金额
                while (ObjectUtil.isNull(walletBalanceMonthStart)){
                    yearMonth = yearMonth.minusMonths(1);
                    YearMonth finalYearMonth1 = yearMonth;
                    walletBalanceMonthStart=  TenantHelper.ignore(()->transactionRecordMapper.selectWalletBalanceMonthEnd(String.valueOf(tenantId), finalYearMonth1.toString(),countryCode));
                }
            }
        }
        return walletBalanceMonthStart;
    }

    /**
     * 推送账单到ERP
     * @param billNos 账单编号
     * @param orderNo 订单编号
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    public void sendBillToErpByConfirmed(String startTime,String endTime,String billNos,String orderNo){
        log.info("[账单确认推送ERP],接收到推送参数,开始时间:{},结束时间:{},账单编号:{},订单号:{}",startTime,endTime,billNos,orderNo);
        if (StrUtil.isEmpty(billNos) && StrUtil.isNotEmpty(orderNo)){
            throw new RuntimeException(" [账单确认推送ERP],账单编号和订单编号不能同时为空");
        }
        String cacheMapValue = sysConfigService.selectConfigByKey("Send_Bill_To_Erp_Ignore_TenantId");
        List<String> ignore= JSONUtil.toList(cacheMapValue, String.class);
        LambdaQueryWrapper<BillHead> q = new LambdaQueryWrapper<>();
        q.eq(BillHead::getDelFlag,0);
        if (StrUtil.isNotEmpty(billNos)){
            List<String>  split = List.of(billNos.split(","));
            q.in(CollUtil.isNotEmpty(split),BillHead::getBillNo,split);
        }
        if (CollUtil.isNotEmpty(ignore)){
            q.eq(BillHead::getBillType,2);
            q.notIn(BillHead::getTenantId,ignore);
        }
        if (StrUtil.isNotEmpty(startTime)){
            q.ge(BillHead::getBillStartTime,DateUtil.parse(startTime, "yyyy-MM-dd HH:mm:ss"));
        }
        if (StrUtil.isNotEmpty(endTime)){
            q.le(BillHead::getBillEndTime,DateUtil.parse(endTime, "yyyy-MM-dd HH:mm:ss"));
        }
        List<BillHead> billHeads = billHeadMapper.selectList(q);
        if (CollUtil.isEmpty(billHeads)){
            throw new RuntimeException(StrUtil.format("[账单确认推送ERP],未找到账单信息,开始时间:{},结束时间:{},账单编号:{},订单号:{}",startTime,endTime,billNos,orderNo));
        }
//        if (billHead.getBillStatus()!=3){
//            log.error("[账单确认推送ERP],账单状态不是已确认"+billNo);
//            throw new RuntimeException("[账单确认推送ERP],账单状态不是已确认"+billNo);
//        }
        List<TenantSite> tenantSites = tenantSiteMapper.selectList(new LambdaQueryWrapper<>());
        Map<String, TenantSite> tenantVoMap = tenantSites.stream()
                                                     .collect(Collectors.toMap(
                                                         // Key Mapper: tenantId + countryCode
                                                         site -> site.getTenantId() + site.getCountryCode(),
                                                         Function.identity(),
                                                         (existingValue, newValue) -> newValue));
        List<SysUserVo> sysUserVos = sysUserMapper.selectVoList();
        Map<String, SysUserVo> userMap = sysUserVos.stream()
                                                   .collect(Collectors.toMap(SysUserVo::getTenantId, user -> user, (existing, replacement) -> existing));
        for (BillHead billHead : billHeads) {
            LambdaQueryWrapper<BillDetails> qq = new LambdaQueryWrapper<>();
            if (StrUtil.isNotEmpty(orderNo)){
                qq.eq(BillDetails::getOrderExtendId,orderNo);
            }
            qq.eq(BillDetails::getBillId,billHead.getId());
            qq.eq(BillDetails::getDelFlag,0);
            List<BillDetails> billDetails = billDetailsMapper.selectList(qq);
            if (CollectionUtil.isEmpty(billDetails)){
                continue;
            }
            AtomicInteger counter = new AtomicInteger(0);
            for (BillDetails s : billDetails) {
                try{
                    BillConfirmedSendErpDTO b=new BillConfirmedSendErpDTO();
                    b.setIsLastData(counter.getAndIncrement() == billDetails.size()  - 1);
                    b.setBillConfirmedTime(billHead.getBillEndTime());
                    TenantSite tenantSite = tenantVoMap.get(billHead.getTenantId() + s.getCountryCode());
                    if (ObjectUtil.isNotNull(tenantSite)){
                        b.setThirdChannelFlag(tenantSite.getChannelFlag());
                    }else {
                        b.setThirdChannelFlag(tenantSite.getChannelFlag());
                    }
                    b.setBillNo(billHead.getBillNo());
                    b.setCurrencyCode(billHead.getCurrencyCode());
                    b.setTotal(billHead.getTotal());
                    BillConfirmedSendErpDTO.BillDetailsConfirmedSendErpDTO bd = BeanUtil.copyProperties(s, BillConfirmedSendErpDTO.BillDetailsConfirmedSendErpDTO.class);
                    if (bd.getOrderStatus()!=2){
                        bd.setOrderRefundTotalAmount(null);
                    }
                    bd.setOrderNo(s.getOrderExtendId());
                    bd.setSupplierTenantId(s.getSupperTenantId());
                    bd.setBillConfirmedTime(billHead.getBillEndTime());
                    SysUserVo sysUserVo = userMap.get(bd.getSupplierTenantId());
                    if (ObjectUtil.isNotNull(sysUserVo)){
                        bd.setNickName(sysUserVo.getNickName());
                    }else {
                        bd.setNickName("");
                    }
                    b.setBillDetailsConfirmedSendErpDTOS(bd);
                    log.info("[账单确认推送ERP],发送消息,内容:{}", JSON.toJSONString(b));
                    rabbitTemplate.convertAndSend(RabbitMqConstant.MULTICHANNEL_SEND_EXCHANGE, RabbitMqConstant.BILL_CONFIRMED_SEND_ERP_REQUEST, JSON.toJSONString(b));
                    //更新明细发送时间
                    TenantHelper.ignore(()  -> {
                        UpdateWrapper<BillDetails> wrapper = new UpdateWrapper<>();
                        wrapper.set("is_send_erp",  1)
                               .set("send_erp_time",new Date())
                               .eq("id", s.getId());
                        billDetailsMapper.update(null,  wrapper);
                    });
                }catch (Exception e){
                    log.error(StrUtil.format("[账单确认推送ERP]异常,账单编号:{},订单类型:{},订单号:{},退款单号:{},异常原因:{}"), billNos,s.getOrderStatus(),s.getOrderNo(),s.getOrderRefundNo(),e.getMessage());
                }
            }
            log.info(StrUtil.format("[账单确认推送ERP],账单信息推送完成,开始时间:{},结束时间:{},账单编号:{},订单号:{}",startTime,endTime,billHead.getBillNo(),orderNo));
        }

    }

}
