package com.zsmall.system.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.excel.utils.ExcelUtil;
import com.hengjian.common.idempotent.annotation.RepeatSubmit;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.log.enums.BusinessType;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.web.core.BaseController;
import com.zsmall.system.entity.domain.bo.receipt.TenantReceiptAccountAndPasswordBo;
import com.zsmall.system.entity.domain.bo.receipt.TenantReceiptAccountBo;
import com.zsmall.system.entity.domain.vo.receipt.TenantReceiptAccountVo;
import com.zsmall.system.entity.iservice.ITenantReceiptAccountService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 收款账户
 *
 * <AUTHOR> Li
 * @date 2023-07-04
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/receiptAccount")
public class TenantReceiptAccountController extends BaseController {

    private final ITenantReceiptAccountService tenantReceiptAccountService;

    /**
     * 查询收款账户列表
     */
    @SaCheckPermission("supplier:receiptAccount:list")
    @GetMapping("/list")
    public TableDataInfo<TenantReceiptAccountVo> list(TenantReceiptAccountBo bo, PageQuery pageQuery) {
        return tenantReceiptAccountService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询收款账户列表
     */
    @SaCheckPermission("supplier:receiptAccount:list")
    @GetMapping("/list/{accountType}")
    public R<List<TenantReceiptAccountVo>> list(@PathVariable String accountType) {
        TenantReceiptAccountBo bo = new TenantReceiptAccountBo();
        bo.setAccountType(accountType);
        List<TenantReceiptAccountVo> list = tenantReceiptAccountService.queryList(bo);
        return R.ok(list);
    }

    /**
     * 导出收款账户列表
     */
    @SaCheckPermission("supplier:receiptAccount:export")
    @Log(title = "收款账户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TenantReceiptAccountBo bo, HttpServletResponse response) {
        List<TenantReceiptAccountVo> list = tenantReceiptAccountService.queryList(bo);
        ExcelUtil.exportExcel(list, "收款账户", TenantReceiptAccountVo.class, response, false);
    }

    /**
     * 获取收款账户详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("supplier:receiptAccount:query")
    @GetMapping("/{id}")
    public R<TenantReceiptAccountVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(tenantReceiptAccountService.queryById(id));
    }

    /**
     * 新增收款账户
     */
    @SaCheckPermission("supplier:receiptAccount:add")
    @Log(title = "收款账户", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TenantReceiptAccountBo bo) throws RStatusCodeException {
        return toAjax(tenantReceiptAccountService.insertByBo(bo));
    }

    /**
     * 修改收款账户
     */
    @SaCheckPermission("supplier:receiptAccount:edit")
    @Log(title = "收款账户", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TenantReceiptAccountBo bo) {
        return toAjax(tenantReceiptAccountService.updateByBo(bo));
    }

    /**
     * 删除收款账户
     *
     * @param bo 账号和密码实体
     */
    @SaCheckPermission("supplier:receiptAccount:remove")
    @Log(title = "收款账户", businessType = BusinessType.DELETE)
    @DeleteMapping("")
    public R<Void> remove(@RequestBody TenantReceiptAccountAndPasswordBo bo) throws RStatusCodeException {
        return toAjax(tenantReceiptAccountService.delete(bo));
    }

    /**
     * 获取收款账户完整账户名
     *
     * @param bo
     */
    @SaCheckPermission("supplier:receiptAccount:query")
    @PostMapping("/getFullAccountNumber")
    public R<String> getInfo(@RequestBody TenantReceiptAccountAndPasswordBo bo) throws RStatusCodeException {
        return R.ok("success", tenantReceiptAccountService.getFullAccountNumber(bo));
    }

}
