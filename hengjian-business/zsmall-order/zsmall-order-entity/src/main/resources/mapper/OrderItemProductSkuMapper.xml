<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.order.entity.mapper.OrderItemProductSkuMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.order.entity.domain.OrderItemProductSku">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="supplierTenantId" column="supplier_tenant_id" jdbcType="VARCHAR"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="orderItemId" column="order_item_id" jdbcType="BIGINT"/>
        <result property="orderItemNo" column="order_item_no" jdbcType="VARCHAR"/>
        <result property="channelType" column="channel_type" jdbcType="VARCHAR"/>
        <result property="channelId" column="channel_id" jdbcType="BIGINT"/>
        <result property="channelVariantId" column="channel_variant_id" jdbcType="BIGINT"/>
        <result property="channelWarehouseCode" column="channel_warehouse_code" jdbcType="VARCHAR"/>
        <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
        <result property="productSkuCode" column="product_sku_code" jdbcType="VARCHAR"/>
        <result property="sku" column="sku" jdbcType="VARCHAR"/>
        <result property="upc" column="upc" jdbcType="VARCHAR"/>
        <result property="erpSku" column="erp_sku" jdbcType="VARCHAR"/>
        <result property="mappingSku" column="mapping_sku" jdbcType="VARCHAR"/>
        <result property="productName" column="product_name" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="imageOssId" column="image_oss_id" jdbcType="BIGINT"/>
        <result property="imageSavePath" column="image_save_path" jdbcType="VARCHAR"/>
        <result property="imageShowUrl" column="image_show_url" jdbcType="VARCHAR"/>
        <result property="specComposeName" column="spec_compose_name" jdbcType="VARCHAR"/>
        <result property="specValName" column="spec_val_name" jdbcType="VARCHAR"/>
        <result property="specifyWarehouse" column="specify_warehouse" jdbcType="VARCHAR"/>
        <result property="warehouseSystemCode" column="warehouse_system_code" jdbcType="VARCHAR"/>
        <result property="activityType" column="activity_type" jdbcType="VARCHAR"/>
        <result property="activityCode" column="activity_code" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,supplier_tenant_id,
        order_no,order_item_id,order_item_no,
        channel_type,channel_id,channel_variant_id,
        channel_warehouse_code,product_code,supplier_product_code,
        product_sku_code,sku,upc,
        erp_sku,mapping_sku,product_name,
        description,image_oss_id,image_save_path,
        image_show_url,spec_compose_name,spec_val_name,
        specify_warehouse,warehouse_system_code,activity_type,
        activity_code,del_flag,create_by,
        create_time,update_by,update_time
    </sql>

    <update id="updateDelFlagByOrderItemIdList">
        update order_item_product_sku
        set del_flag = #{delFlag}
        where
        order_item_id in
        <foreach collection="orderItemIdList" item="orderItemId" open="(" close=")" separator=",">
            #{orderItemId}
        </foreach>
    </update>

    <select id="queryByWrapperNoTenant" resultMap="BaseResultMap">
        select oips.* FROM order_item_product_sku oips ${ew.getCustomSqlSegment}
    </select>
</mapper>
