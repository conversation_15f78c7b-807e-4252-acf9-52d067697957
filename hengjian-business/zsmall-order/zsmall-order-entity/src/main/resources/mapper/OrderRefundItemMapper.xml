<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.order.entity.mapper.OrderRefundItemMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.order.entity.domain.OrderRefundItem">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="orderId" column="order_id" jdbcType="BIGINT"/>
        <result property="orderItemId" column="order_item_id" jdbcType="BIGINT"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="orderItemNo" column="order_item_no" jdbcType="VARCHAR"/>
        <result property="orderRefundId" column="order_refund_id" jdbcType="BIGINT"/>
        <result property="orderRefundNo" column="order_refund_no" jdbcType="VARCHAR"/>
        <result property="orderRefundItemNo" column="order_refund_item_no" jdbcType="VARCHAR"/>
        <result property="refundQuantity" column="refund_quantity" jdbcType="INTEGER"/>
        <result property="productSkuCode" column="product_sku_code" jdbcType="VARCHAR"/>
        <result property="activityType" column="activity_type" jdbcType="VARCHAR"/>
        <result property="activityCode" column="activity_code" jdbcType="VARCHAR"/>
        <result property="originalPayableTotalAmount" column="original_payable_total_amount" jdbcType="DECIMAL"/>
        <result property="originalPrepaidTotalAmount" column="original_prepaid_total_amount" jdbcType="DECIMAL"/>
        <result property="originalActualTotalAmount" column="original_actual_total_amount" jdbcType="DECIMAL"/>
        <result property="platformPayableTotalAmount" column="platform_payable_total_amount" jdbcType="DECIMAL"/>
        <result property="platformPrepaidTotalAmount" column="platform_prepaid_total_amount" jdbcType="DECIMAL"/>
        <result property="platformActualTotalAmount" column="platform_actual_total_amount" jdbcType="DECIMAL"/>
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_id,order_item_id,
        order_no,order_item_no,order_refund_id,
        order_refund_no,order_refund_item_no,refund_quantity,
        product_sku_code,activity_type,activity_code,
        original_payable_total_amount,original_prepaid_total_amount,original_actual_total_amount,
        platform_payable_total_amount,platform_prepaid_total_amount,platform_actual_total_amount,
        del_flag,create_by,create_time,
        update_by,update_time
    </sql>


    <select id="existsOrderRefundItemNo" resultType="java.lang.Boolean">
        SELECT COUNT(ori.id)
        FROM order_refund_item ori
        WHERE ori.order_refund_item_no = #{orderRefundItemNo}
    </select>

    <select id="countByInProgress" resultType="java.lang.Integer">
        SELECT COUNT(ori.id)
        FROM order_refund_item ori
                 JOIN order_refund ro on ori.order_refund_id = ro.id AND ro.del_flag = '0'
                 JOIN order_item oi on ori.order_item_id = oi.id AND oi.del_flag = '0'
        WHERE ori.del_flag = 0
          AND ori.order_item_no = #{orderItemNo}
          AND ro.refund_state IN ('ManagerVerifying', 'Verifying', 'Refunding')
    </select>

    <select id="getOrderTotalRefundNum" resultType="java.lang.Integer">
        SELECT SUM(ori.refund_quantity)
        FROM order_refund_item ori
                 JOIN order_refund ro ON ro.id = ori.order_refund_id
        WHERE ro.del_flag = '0'
          AND ori.del_flag = '0'
          AND ori.order_id = #{orderId}
          AND ro.refund_state != 'Reject'
    </select>
    <select id="getOrderItemTotalRefundNum" resultType="java.lang.Integer">
        SELECT SUM(ori.refund_quantity)
        FROM order_refund_item ori
                 JOIN order_refund ro ON ro.id = ori.order_refund_id
        WHERE ro.del_flag = '0'
          AND ori.del_flag = '0'
          AND ori.order_item_id = #{orderItemId}
          AND ro.refund_state != 'Reject'
    </select>

    <select id="getByOrderRefundItemNoAndState" resultMap="BaseResultMap">
        select ori.*
        from order_refund_item ori
                 join order_refund o on ori.order_refund_id = o.id
        where ori.del_flag = '0'
          and o.del_flag = '0'
          and ori.order_refund_item_no = #{orderItemNo}
          and o.refund_state = #{state}
    </select>


</mapper>
