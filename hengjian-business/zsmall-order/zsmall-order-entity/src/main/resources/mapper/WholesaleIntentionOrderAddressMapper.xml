<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.order.entity.mapper.WholesaleIntentionOrderAddressMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.order.entity.domain.WholesaleIntentionOrderAddress">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="wholesaleIntentionOrderId" column="wholesale_intention_order_id" jdbcType="BIGINT"/>
            <result property="recipientCountryCode" column="recipient_country_code" jdbcType="VARCHAR"/>
            <result property="recipientCountry" column="recipient_country" jdbcType="VARCHAR"/>
            <result property="recipientStateCode" column="recipient_state_code" jdbcType="VARCHAR"/>
            <result property="recipientState" column="recipient_state" jdbcType="VARCHAR"/>
            <result property="recipientCity" column="recipient_city" jdbcType="VARCHAR"/>
            <result property="recipientAddress1" column="recipient_address1" jdbcType="VARCHAR"/>
            <result property="recipientAddress2" column="recipient_address2" jdbcType="VARCHAR"/>
            <result property="recipientZipCode" column="recipient_zip_code" jdbcType="VARCHAR"/>
            <result property="recipientName" column="recipient_name" jdbcType="VARCHAR"/>
            <result property="recipientPhone" column="recipient_phone" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,wholesale_intention_order_id,recipient_country,recipient_country_code,
        recipient_state,recipient_state_code,recipient_city,recipient_address1,
        recipient_address2,recipient_zip_code,recipient_name,
        recipient_phone,del_flag,create_by,
        create_time,update_by,update_time
    </sql>
</mapper>
