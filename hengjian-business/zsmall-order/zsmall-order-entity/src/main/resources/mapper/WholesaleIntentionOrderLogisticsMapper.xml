<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.order.entity.mapper.WholesaleIntentionOrderLogisticsMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.order.entity.domain.WholesaleIntentionOrderLogistics">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="wholesaleIntentionOrderId" column="wholesale_intention_order_id" jdbcType="BIGINT"/>
            <result property="deliveryType" column="delivery_type" jdbcType="VARCHAR"/>
            <result property="appointmentTime" column="appointment_time" jdbcType="VARCHAR"/>
            <result property="logisticsTemplateNo" column="logistics_template_no" jdbcType="VARCHAR"/>
            <result property="warehouseSystemCode" column="warehouse_system_code" jdbcType="VARCHAR"/>
            <result property="warehouseCountryCode" column="warehouse_country_code" jdbcType="VARCHAR"/>
            <result property="warehouseCountry" column="warehouse_country" jdbcType="VARCHAR"/>
            <result property="warehouseStateCode" column="warehouse_state_code" jdbcType="VARCHAR"/>
            <result property="warehouseState" column="warehouse_state" jdbcType="VARCHAR"/>
            <result property="warehouseCity" column="warehouse_city" jdbcType="VARCHAR"/>
            <result property="warehouseAddress1" column="warehouse_address1" jdbcType="VARCHAR"/>
            <result property="warehouseAddress2" column="warehouse_address2" jdbcType="VARCHAR"/>
            <result property="warehouseZipCode" column="warehouse_zip_code" jdbcType="VARCHAR"/>
            <result property="warehouseContact" column="warehouse_contact" jdbcType="VARCHAR"/>
            <result property="warehousePhone" column="warehouse_phone" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,wholesale_intention_order_id,delivery_type,
        appointment_time,logistics_template_no,warehouse_system_code,
        warehouse_country_code,warehouse_country,warehouse_state_code,warehouse_state,warehouse_city,
        warehouse_address1,warehouse_address2,warehouse_zip_code,
        warehouse_contact,warehouse_phone,del_flag,
        create_by,create_time,update_by,
        update_time
    </sql>
</mapper>
