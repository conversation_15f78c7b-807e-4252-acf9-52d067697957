<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.order.entity.mapper.AirwallexRequestInfoMapper">

    <sql id="Common_Column_List">
        id,request_id,tenant_id,pay_type,`type`,order_no,return_info,is_success,exception_message,aes_key,commit_id
    </sql>

    <insert id="addAirwallexRequestLog">
        insert into airwallex_request_log (request_id,return_info,create_time)
        values (#{airwallexRequestLog.requestId},#{airwallexRequestLog.returnInfo},#{airwallexRequestLog.createTime})
    </insert>

    <select id="getByRequestId" resultType="com.zsmall.order.entity.domain.AirwallexRequestInfo">
        select <include refid="Common_Column_List"/>
        from airwallex_request_info
        where
            del_flag = '0'
        and request_id = #{requestId}
        order by id desc
        limit 1
    </select>

    <select id="getByCommitId" resultType="com.zsmall.order.entity.domain.AirwallexRequestInfo">
        select <include refid="Common_Column_List"/>
        from airwallex_request_info
        where
            del_flag = '0'
        and commit_id = #{commitId}
        order by id desc
        limit 1
    </select>

</mapper>
