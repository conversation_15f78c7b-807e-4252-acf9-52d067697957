<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.order.entity.mapper.TemporaryTableMapper">

    <select id="createTemporaryTable" flushCache="true">
        CREATE TEMPORARY TABLE ${tableName} (
        `value1` bigint DEFAULT NULL,
        `value2` bigint DEFAULT NULL,
        `value3` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
        `value4` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
        UNIQUE KEY `idx_value_1` (`value1`),
        UNIQUE KEY `idx_value_2` (`value2`),
        KEY `idx_value_3` (`value3`),
        KEY `idx_value_4` (`value4`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='临时表'
    </select>

    <insert id="batchInsertTemporaryTable" parameterType="java.util.List">
        INSERT INTO ${tableName} (value1, value2, value3, value4)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.value1,jdbcType=BIGINT},#{item.value2,jdbcType=BIGINT},#{item.value3,jdbcType=VARCHAR},#{item.value4,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="dropTemporaryTable" flushCache="true">
        DROP TABLE IF EXISTS ${tableName}
    </select>

    <update id="cleanTemporaryTable">
        truncate table ${tableName}
    </update>
</mapper>
