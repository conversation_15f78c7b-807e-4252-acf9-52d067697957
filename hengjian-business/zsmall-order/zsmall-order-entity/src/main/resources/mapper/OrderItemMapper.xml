<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.order.entity.mapper.OrderItemMapper">

    <!--    <resultMap id="BaseResultMap" type="com.zsmall.order.entity.domain.OrderItem">-->
    <!--            <id property="id" column="id" jdbcType="BIGINT"/>-->
    <!--            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>-->
    <!--            <result property="supplierTenantId" column="supplier_tenant_id" jdbcType="VARCHAR"/>-->
    <!--            <result property="orderId" column="order_id" jdbcType="BIGINT"/>-->
    <!--            <result property="orderItemNo" column="order_item_no" jdbcType="VARCHAR"/>-->
    <!--            <result property="channelType" column="channel_type" jdbcType="VARCHAR"/>-->
    <!--            <result property="channelId" column="channel_id" jdbcType="BIGINT"/>-->
    <!--            <result property="channelItemNo" column="channel_item_no" jdbcType="VARCHAR"/>-->
    <!--            <result property="channelFulfillmentId" column="channel_fulfillment_id" jdbcType="VARCHAR"/>-->
    <!--            <result property="channelFulfillmentMessage" column="channel_fulfillment_message" jdbcType="VARCHAR"/>-->
    <!--            <result property="stockManager" column="stock_manager" jdbcType="VARCHAR"/>-->
    <!--            <result property="stockState" column="stock_state" jdbcType="VARCHAR"/>-->
    <!--            <result property="stockMessage" column="stock_message" jdbcType="VARCHAR"/>-->
    <!--            <result property="logisticsType" column="logistics_type" jdbcType="VARCHAR"/>-->
    <!--            <result property="logisticsTemplateNo" column="logistics_template_no" jdbcType="VARCHAR"/>-->
    <!--            <result property="shippingOrderState" column="shipping_order_state" jdbcType="VARCHAR"/>-->
    <!--            <result property="shippingOrderMessage" column="shipping_order_message" jdbcType="VARCHAR"/>-->
    <!--            <result property="fulfillmentProgress" column="fulfillment_progress" jdbcType="VARCHAR"/>-->
    <!--            <result property="dispatchedTime" column="dispatched_time" jdbcType="TIMESTAMP"/>-->
    <!--            <result property="fulfillmentTime" column="fulfillment_time" jdbcType="TIMESTAMP"/>-->
    <!--            <result property="orderState" column="order_state" jdbcType="VARCHAR"/>-->
    <!--            <result property="productSkuCode" column="product_sku_code" jdbcType="VARCHAR"/>-->
    <!--            <result property="activityType" column="activity_type" jdbcType="VARCHAR"/>-->
    <!--            <result property="activityCode" column="activity_code" jdbcType="VARCHAR"/>-->
    <!--            <result property="totalQuantity" column="total_quantity" jdbcType="INTEGER"/>-->
    <!--            <result property="restockQuantity" column="restock_quantity" jdbcType="INTEGER"/>-->
    <!--            <result property="supplierIncomeEarned" column="supplier_income_earned" jdbcType="DECIMAL"/>-->
    <!--            <result property="originalPayableUnitPrice" column="original_payable_unit_price" jdbcType="DECIMAL"/>-->
    <!--            <result property="originalPayableTotalAmount" column="original_payable_total_amount" jdbcType="DECIMAL"/>-->
    <!--            <result property="originalActualUnitPrice" column="original_actual_unit_price" jdbcType="DECIMAL"/>-->
    <!--            <result property="originalActualTotalAmount" column="original_actual_total_amount" jdbcType="DECIMAL"/>-->
    <!--            <result property="originalRefundExecutableAmount" column="original_refund_executable_amount" jdbcType="DECIMAL"/>-->
    <!--            <result property="platformPayableUnitPrice" column="platform_payable_unit_price" jdbcType="DECIMAL"/>-->
    <!--            <result property="platformPayableTotalAmount" column="platform_payable_total_amount" jdbcType="DECIMAL"/>-->
    <!--            <result property="platformActualUnitPrice" column="platform_actual_unit_price" jdbcType="DECIMAL"/>-->
    <!--            <result property="platformActualTotalAmount" column="platform_actual_total_amount" jdbcType="DECIMAL"/>-->
    <!--            <result property="platformRefundExecutableAmount" column="platform_refund_executable_amount" jdbcType="DECIMAL"/>-->
    <!--            <result property="channelSaleUnitPrice" column="channel_sale_unit_price" jdbcType="DECIMAL"/>-->
    <!--            <result property="channelSaleTotalAmount" column="channel_sale_total_amount" jdbcType="DECIMAL"/>-->
    <!--            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>-->
    <!--            <result property="createBy" column="create_by" jdbcType="BIGINT"/>-->
    <!--            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>-->
    <!--            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>-->
    <!--            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>-->
    <!--    </resultMap>-->

    <!--    <sql id="Base_Column_List">-->
    <!--        id,tenant_id,supplier_tenant_id,-->
    <!--        order_id,order_item_no,channel_type,-->
    <!--        channel_id,channel_item_no,channel_fulfillment_id,-->
    <!--        channel_fulfillment_message,stock_manager,stock_state,-->
    <!--        stock_message,logistics_type,logistics_template_no,-->
    <!--        shipping_order_state,shipping_order_message,fulfillment_progress,-->
    <!--        dispatched_time,fulfillment_time,order_state,-->
    <!--        product_sku_code,activity_type,activity_code,-->
    <!--        total_quantity,restock_quantity,supplier_income_earned,-->
    <!--        original_payable_unit_price,original_payable_total_amount,original_actual_unit_price,-->
    <!--        original_actual_total_amount,original_refund_executable_amount,platform_payable_unit_price,-->
    <!--        platform_payable_total_amount,platform_actual_unit_price,platform_actual_total_amount,-->
    <!--        platform_refund_executable_amount,channel_sale_unit_price,channel_sale_total_amount,-->
    <!--        del_flag,create_by,create_time,-->
    <!--        update_by,update_time-->
    <!--    </sql>-->

    <select id="getOrderItemListByShipped" resultType="com.zsmall.order.entity.domain.OrderItem">
        SELECT oi.* FROM order_item oi
        JOIN orders o ON o.id = oi.order_id
        WHERE oi.del_flag = '0'
        AND o.del_flag = '0'
        AND oi.fulfillment_progress = 'Dispatched' AND oi.order_state = 'Paid'
        AND oi.order_item_no IN
        <foreach collection="orderItemNoList" index="index" item="item" open="(" separator="," close=")">#{item}
        </foreach>
    </select>

    <select id="getAllUnDispatchedItems" resultType="com.zsmall.order.entity.domain.OrderItem">
        SELECT oi.*
        FROM order_item oi
                 JOIN orders o ON o.id = oi.order_id
        WHERE o.del_flag = '0'
          AND oi.del_flag = '0'
          AND o.order_no = #{orderNo}
          AND oi.fulfillment_progress = #{fulfillmentProcess}
          AND oi.order_state = #{orderState}
    </select>

    <select id="countSoldNumber" resultType="java.lang.Integer">
        SELECT SUM(oi.total_quantity - oi.restock_quantity)
        FROM order_item oi
        JOIN orders o ON o.id = oi.order_id
        JOIN order_item_product_sku ops ON oi.id = ops.order_item_id
        WHERE oi.del_flag = '0'
        AND o.del_flag = '0'
        AND ops.del_flag = '0'
        AND oi.order_state = #{orderState}
        AND o.order_state = #{orderState}
        <if test="productCode != null and productCode != ''">
            AND ops.product_code = #{productCode}
        </if>
        <if test="sku != null and sku != ''">
            AND ops.sku = #{sku}
        </if>
    </select>

    <select id="sumPlatformPayableTotalAmount" resultType="java.math.BigDecimal">
        SELECT SUM(oi.platform_payable_total_amount)
        from order_item oi
                 JOIN orders o on oi.order_id = o.id
        where o.order_type != 'Wholesale'
          AND oi.fulfillment_progress = 'Fulfilled'
        <if test="startDate != null">
            AND oi.fulfillment_time &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND oi.fulfillment_time &lt;= #{endDate}
        </if>
    </select>

    <select id="queryValidQuantityByActivity" resultType="java.lang.Integer">
        SELECT IFNULL(SUM(oi.total_quantity), 0)
        FROM order_item oi
        WHERE oi.fulfillment_progress IN ('Dispatched', 'Fulfilled')
          AND oi.activity_code = #{activityCode}
    </select>
    <select id="getSysTenantByThirdChannelFlag" resultType="com.hengjian.system.domain.SysTenant">
        select * from sys_tenant where third_channel_flag  = #{channelFlag} and tenant_id = #{tenantId}

    </select>
</mapper>
