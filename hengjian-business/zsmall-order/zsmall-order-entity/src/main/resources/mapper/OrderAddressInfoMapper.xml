<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.order.entity.mapper.OrderAddressInfoMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.order.entity.domain.OrderAddressInfo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="orderId" column="order_id" jdbcType="BIGINT"/>
            <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
            <result property="addressType" column="address_type" jdbcType="VARCHAR"/>
            <result property="recipient" column="recipient" jdbcType="VARCHAR"/>
            <result property="country" column="country" jdbcType="VARCHAR"/>
            <result property="countryCode" column="country_code" jdbcType="VARCHAR"/>
            <result property="state" column="state" jdbcType="VARCHAR"/>
            <result property="stateCode" column="state_code" jdbcType="VARCHAR"/>
            <result property="city" column="city" jdbcType="VARCHAR"/>
            <result property="address1" column="address1" jdbcType="VARCHAR"/>
            <result property="address2" column="address2" jdbcType="VARCHAR"/>
            <result property="email" column="email" jdbcType="VARCHAR"/>
            <result property="phoneNumber" column="phone_number" jdbcType="VARCHAR"/>
            <result property="zipCode" column="zip_code" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_id,order_no,
        address_type,recipient,country,
        country_code,state,state_code,
        city,address1,address2,
        email,phone_number,zip_code,
        del_flag,create_by,create_time,
        update_by,update_time
    </sql>
</mapper>
