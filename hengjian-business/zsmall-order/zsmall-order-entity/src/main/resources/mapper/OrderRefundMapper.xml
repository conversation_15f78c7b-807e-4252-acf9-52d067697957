<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.order.entity.mapper.OrderRefundMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.order.entity.domain.OrderRefund">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="supplierTenantId" column="supplier_tenant_id" jdbcType="VARCHAR"/>
        <result property="orderId" column="order_id" jdbcType="BIGINT"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="orderRefundNo" column="order_refund_no" jdbcType="VARCHAR"/>
        <result property="originalRefundAmount" column="original_refund_amount" jdbcType="DECIMAL"/>
        <result property="platformRefundAmount" column="platform_refund_amount" jdbcType="DECIMAL"/>
        <result property="refundApplyTime" column="refund_apply_time" jdbcType="TIMESTAMP"/>
        <result property="refundType" column="refund_type" jdbcType="VARCHAR"/>
        <result property="refundQuantity" column="refund_quantity" jdbcType="INTEGER"/>
        <result property="refundRuleNo" column="refund_rule_no" jdbcType="VARCHAR"/>
        <result property="refundRuleReason" column="refund_rule_reason" jdbcType="OTHER"/>
        <result property="refundDescription" column="refund_description" jdbcType="VARCHAR"/>
        <result property="refundAmountState" column="refund_amount_state" jdbcType="VARCHAR"/>
        <result property="refundState" column="refund_state" jdbcType="VARCHAR"/>
        <result property="refundCompletionTime" column="refund_completion_time" jdbcType="TIMESTAMP"/>
        <result property="refundDisputeState" column="refund_dispute_state" jdbcType="VARCHAR"/>
        <result property="messagingAppId" column="messaging_app_id" jdbcType="VARCHAR"/>
        <result property="messagingAppType" column="messaging_app_type" jdbcType="VARCHAR"/>
        <result property="managerUserId" column="manager_user_id" jdbcType="VARCHAR"/>
        <result property="managerReviewTime" column="manager_review_time" jdbcType="TIMESTAMP"/>
        <result property="managerReviewOpinion" column="manager_review_opinion" jdbcType="VARCHAR"/>
        <result property="supplierUserId" column="supplier_user_id" jdbcType="VARCHAR"/>
        <result property="supplierReviewTime" column="supplier_review_time" jdbcType="TIMESTAMP"/>
        <result property="supplierReviewOpinion" column="supplier_review_opinion" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,supplier_tenant_id,
        order_id,order_no,order_refund_no,
        original_refund_amount,platform_refund_amount,refund_apply_time,
        refund_type,refund_quantity,refund_rule_no,
        refund_rule_reason,refund_description,refund_amount_state,
        refund_state,refund_completion_time,refund_dispute_state,
        messaging_app_id,messaging_app_type,manager_user_id,
        manager_review_time,manager_review_opinion,supplier_user_id,
        supplier_review_time,supplier_review_opinion,del_flag,
        create_by,create_time,update_by,
        update_time
    </sql>

    <select id="queryPageList" resultType="com.zsmall.order.entity.domain.vo.RefundApplyVo">
        SELECT
        ro.id,
        ro.refund_state,
        ro.refund_quantity,
        ro.order_refund_no,
        ro.refund_dispute_state,
        oai.recipient AS name,
        o.channel_type,
        ro.original_refund_amount,
        ro.platform_refund_amount AS total,
        ro.refund_apply_time,
        ro.refund_amount_state,
        ro.order_no,
        ro.tenant_id,
        ro.supplier_tenant_id,
        ori.product_sku_code AS itemNo,
        ro.messaging_app_id,
        ro.messaging_app_type,
        ro.create_time,
        ro.currency_code,
        ro.currency_symbol,
        o.fulfillment_progress
        FROM order_refund ro
        LEFT JOIN orders o ON o.id = ro.order_id
        LEFT JOIN order_address_info oai ON oai.order_id = o.id AND oai.del_flag = '0' AND oai.address_type =
        'ShipAddress'
        LEFT JOIN order_refund_item ori ON ori.order_refund_id = ro.id AND ori.del_flag = '0'
        WHERE ro.del_flag = '0'
        AND o.del_flag = '0'
        <if test="dto.tenantType == 'Distributor'">
            AND ro.tenant_id = #{dto.tenantId}
        </if>
        <if test="dto.tenantType == 'Supplier'">
            AND ro.supplier_tenant_id = #{dto.tenantId}
        </if>
        <if test="dto.orderNo != null and dto.orderNo != ''">
            AND ro.order_no = #{dto.orderNo}
        </if>
<!--        <if test="dto.orderRefundNo != null and dto.orderRefundNo != ''">-->
<!--            AND ro.order_refund_no = #{dto.orderRefundNo}-->
<!--        </if>-->
        <if test="dto.orderRefundNos != null and dto.orderRefundNos.size > 0">
            AND ro.order_refund_no IN
            <foreach collection="dto.orderRefundNos" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.refundDisputeState != null and dto.refundDisputeState != ''">
            AND ro.refund_dispute_state = #{dto.refundDisputeState}
        </if>
        <if test="dto.channelType != null and dto.channelType != ''">
            AND o.channel_type = #{dto.channelType}
        </if>

        <if test="dto.startDate != null and dto.startDate != ''">
            AND ro.refund_apply_time &gt;= #{dto.startDate}
        </if>
        <if test="dto.endDate!= null and dto.endDate!= ''">
            AND ro.refund_apply_time &lt;= #{dto.endDate}
        </if>
        <if test="dto.orderRefundNo != null and dto.orderRefundNo != ''">
            AND ro.order_refund_no LIKE CONCAT('%', #{dto.orderRefundNo}, '%')
        </if>
        <if test="dto.currencyCode != null and dto.currencyCode != ''">
            AND ro.currency_code = #{dto.currencyCode}
        </if>

        <if test="dto.refundStates != null and dto.refundStates.size > 0">
            AND ro.refund_state IN
            <foreach collection="dto.refundStates" index="index" item="item" open="(" separator="," close=")">#{item}
            </foreach>
        </if>
        GROUP BY ro.id
        order by ro.id desc
    </select>

    <select id="countByInProgress" resultType="java.lang.Integer">
        SELECT COUNT(odr.id)
        FROM order_refund odr
        WHERE odr.order_id = #{orderId}
          AND odr.del_flag = '0'
          AND EXISTS(SELECT 1
                     FROM order_refund_item ori
                     WHERE ori.del_flag = '0'
                       AND ori.order_refund_id = odr.id
                       AND odr.refund_state IN ('Verifying', 'Refunding'))
    </select>

    <select id="existsOrderRefundNo" resultType="java.lang.Boolean">
        SELECT COUNT(ro.id)
        FROM order_refund ro
        WHERE ro.order_refund_no = #{orderRefundNo}
    </select>

    <select id="sumPlatformRefundAmountForTenant" resultType="com.zsmall.order.entity.domain.vo.OrderRefundVo">
        SELECT SUM(platform_refund_amount) as platform_refund_amount
        FROM order_refund
        WHERE refund_amount_state = 'Refunded'
    </select>

    <select id="queryByOrderItemAndState" resultType="com.zsmall.order.entity.domain.OrderRefund">
        SELECT ord.*
        FROM order_refund ord
        WHERE ord.del_flag = '0'
          AND ord.refund_state = #{refundState}
          AND EXISTS(SELECT 1
                     FROM order_refund_item ori
                     WHERE ori.order_refund_id = ord.id
                       AND ori.del_flag = '0'
                       AND ori.order_item_id = #{orderItemId})
    </select>

    <select id="sumByRefundPrice" resultType="java.math.BigDecimal">
        SELECT ifnull(if(#{isSupplier},
                         SUM(CAST(
                             IFNULL(ord.original_refund_amount,
                                    ord.platform_refund_amount) AS DECIMAL(20, 2))),
                         SUM(CAST(ord.platform_refund_amount AS DECIMAL(20, 2)))), 0)
        FROM order_refund ord
        JOIN orders o ON ord.order_id = o.id
        WHERE ord.del_flag = '0'
          AND ord.order_id = #{orderId}
          AND ord.refund_state = 'Refunded'
    </select>

    <select id="sumPlatformRefundAmount" resultType="java.math.BigDecimal">
        SELECT
        SUM(o.platform_refund_amount) as total
        FROM order_refund o
        WHERE o.refund_state = 'Refunded' and o.del_flag = '0'
          and exists (select 1 from orders o1 where o1.id = o.order_id and o1.fulfillment_progress = 'Fulfilled' )
        <if test="startDate != null">
            AND o.refund_completion_time &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND o.refund_completion_time &lt;= #{endDate}
        </if>
    </select>

    <select id="queryOrderRefundByRefunded" resultMap="BaseResultMap">
        SELECT o.* FROM order_refund o
        WHERE o.del_flag = '0'
          AND o.order_id = #{orderId}
          <if test="excludeRefundId != null">
              AND o.id != #{excludeRefundId}
          </if>
        AND EXISTS (SELECT 1 FROM order_refund_item ori WHERE ori.del_flag = '0' AND ori.order_refund_id = o.id AND o.refund_state = #{refundState})
    </select>
    <select id="queryOrderByRefundNo" resultType="com.zsmall.order.entity.domain.Orders">
        select * from orders where order_no in (select order_no from order_refund where order_refund_no = #{orderRefundNo});
    </select>

</mapper>
