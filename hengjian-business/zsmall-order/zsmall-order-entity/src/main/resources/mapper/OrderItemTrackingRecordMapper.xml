<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.order.entity.mapper.OrderItemTrackingRecordMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.order.entity.domain.OrderItemTrackingRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
            <result property="orderItemNo" column="order_item_no" jdbcType="VARCHAR"/>
            <result property="sku" column="sku" jdbcType="VARCHAR"/>
            <result property="productSkuCode" column="product_sku_code" jdbcType="VARCHAR"/>
            <result property="quantity" column="quantity" jdbcType="INTEGER"/>
            <result property="dispatchedTime" column="dispatched_time" jdbcType="TIMESTAMP"/>
            <result property="fulfillmentTime" column="fulfillment_time" jdbcType="TIMESTAMP"/>
            <result property="logisticsCarrier" column="logistics_carrier" jdbcType="VARCHAR"/>
            <result property="logisticsService" column="logistics_service" jdbcType="VARCHAR"/>
            <result property="logisticsTrackingNo" column="logistics_tracking_no" jdbcType="VARCHAR"/>
            <result property="logisticsProgress" column="logistics_progress" jdbcType="VARCHAR"/>
            <result property="warehouseCode" column="warehouse_code" jdbcType="VARCHAR"/>
            <result property="warehouseSystemCode" column="warehouse_system_code" jdbcType="VARCHAR"/>
            <result property="systemManaged" column="system_managed" jdbcType="TINYINT"/>
            <result property="callingApi" column="calling_api" jdbcType="INTEGER"/>
            <result property="confirmDate" column="confirm_date" jdbcType="VARCHAR"/>
            <result property="lastQueryTime" column="last_query_time" jdbcType="TIMESTAMP"/>
            <result property="thirdPartyCode" column="third_party_code" jdbcType="VARCHAR"/>
            <result property="thirdPartyMessage" column="third_party_message" jdbcType="VARCHAR"/>
            <result property="thirdPartyDateTime" column="third_party_date_time" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_no,
        order_item_no,sku,product_sku_code,
        quantity,dispatched_time,fulfillment_time,
        logistics_carrier,logistics_service,logistics_tracking_no,
        logistics_progress,warehouse_code,warehouse_system_code,
        system_managed,calling_api,confirm_date,
        last_query_time,third_party_code,third_party_message,
        third_party_date_time,del_flag,create_by,
        create_time,update_by,update_time
    </sql>

    <select id="queryPage" resultType="com.zsmall.order.entity.domain.vo.OrderItemTrackingRecordVo">
        SELECT
        t.id AS id,
        o.order_no AS orderNo,
        o.create_time AS orderTime,
        o.channel_order_time AS channelOrderTime,
        IFNULL(o.channel_alias, tsc.channel_name) AS channelAlias,
        o.order_state AS orderState,
        oi.order_state AS itemState,
        o.logistics_type AS logisticsType,
        oi.fulfillment_progress AS fulfillmentProgress,
        ops.sku AS sku,
        ops.product_sku_code AS productSkuCode,
        oi.total_quantity AS quantity,
        IF(IFNULL(o.logistics_type, true), 'DropShipping', o.logistics_type ) AS logisticsTypeEnum,
        t.logistics_carrier,
        oli.logistics_service_name as logisticsService,
        t.logistics_tracking_no,
        t.logistics_progress,
        o.channel_type,
        o.channel_id,
        t.third_party_code,
        t.third_party_message,
        t.third_party_date_time,
        o.channel_order_no AS channelOrderId
        FROM
        order_item_tracking_record t
        LEFT JOIN order_item oi on oi.order_item_no = t.order_item_no
        LEFT JOIN order_item_product_sku ops ON ops.order_item_id = oi.id
        LEFT JOIN orders o on o.id = oi.order_id
        LEFT JOIN tenant_sales_channel tsc on tsc.id = o.channel_id
        JOIN order_logistics_info oli on oli.order_id = o.id
        WHERE o.order_state != 'Voided'
        AND o.del_flag = '0'
        AND oi.del_flag = '0'
        AND ops.del_flag = '0'
        AND t.del_flag = '0'
        <if test="query.channelOrderId != null and query.channelOrderId != ''">
            AND o.channel_order_no = #{query.channelOrderId}
        </if>
        <if test="query.channelType != null and query.channelType != ''">
            AND o.channel_type = #{query.channelType}
        </if>
        <if test="query.startDate != null and query.endDate != null">
            AND o.create_time BETWEEN #{query.startDate} AND #{query.endDate}
        </if>
        <if test="query.logisticsType != null and query.logisticsType != ''">
            AND o.logistics_type = #{query.logisticsType}
        </if>
        <if test="query.logisticsCarrier != null and query.logisticsCarrier != ''">
            AND t.logistics_carrier = #{query.logisticsCarrier}
        </if>
        <if test="query.logisticsProgress != null and query.logisticsProgress != ''">
            AND t.logistics_progress = #{query.logisticsProgress}
        </if>
        <if test="query.orderNo != null and query.orderNo != ''">
            AND o.order_no LIKE CONCAT('%', #{query.orderNo}, '%')
        </if>
        <if test="query.logisticsTrackingNo != null and query.logisticsTrackingNo != ''">
            AND t.logistics_tracking_no LIKE CONCAT('%', #{query.logisticsTrackingNo}, '%')
        </if>
        <if test="query.productSkuCode != null and query.productSkuCode != ''">
            AND ops.product_sku_code LIKE CONCAT('%', #{query.productSkuCode}, '%')
        </if>
        <if test="query.Sku != null and query.Sku != ''">
            AND ops.sku LIKE CONCAT('%', #{query.Sku}, '%')
        </if>
        <if test="query.tenantType == 'Supplier'">
            AND oi.supplier_tenant_id = #{query.tenantId}
        </if>
        <if test="query.tenantType == 'Distributor'">
            AND o.tenant_id = #{query.tenantId}
        </if>
        ORDER BY orderTime DESC, id DESC
    </select>

    <select id="queryInTransitTrackingRecord"
            resultType="com.zsmall.order.entity.domain.OrderItemTrackingRecord">
        SELECT *
        FROM order_item_tracking_record tr
        WHERE tr.logistics_progress != #{fulfillment}
          AND tr.logistics_tracking_no = #{trackingNo}
          AND tr.logistics_carrier = #{carrier}
          AND EXISTS (SELECT 1
                      FROM order_item oi
                      WHERE oi.order_item_no = tr.order_item_no
                        AND oi.order_state = #{orderState}
                        AND oi.fulfillment_progress != #{fulfillment})
          AND tr.del_flag = '0'
    </select>

</mapper>
