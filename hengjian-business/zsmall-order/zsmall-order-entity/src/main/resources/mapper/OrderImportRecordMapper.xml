<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.order.entity.mapper.OrderImportRecordMapper">

    <resultMap id="QueryPageMap"
               type="com.zsmall.order.entity.domain.vo.orderImport.OrderImportRecordVo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="importRecordNo" column="import_record_no" jdbcType="VARCHAR"/>
        <result property="importFileName" column="import_file_name" jdbcType="VARCHAR"/>
        <result property="importOrders" column="import_orders" jdbcType="INTEGER"/>
        <result property="importMessage" column="import_message" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="importState" column="import_state" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="existImportRecordNo" resultType="java.lang.Boolean">
        SELECT COUNT(oir.id) FROM order_import_record oir WHERE oir.import_record_no = #{importRecordNo}
    </select>

    <select id="queryPage" resultMap="QueryPageMap" resultType="com.zsmall.order.entity.domain.vo.orderImport.OrderImportRecordVo">
        SELECT oir.*
        FROM order_import_record oir
        WHERE oir.del_flag = '0' AND oir.import_type = 'Excel'
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryValue)">
            AND EXISTS (SELECT 1 FROM order_import_temp oit WHERE oit.del_flag = '0' AND oit.record_id = oir.id AND
            oit.order_no LIKE CONCAT('%', #{queryValue}, '%'))
        </if>
        ORDER BY oir.create_time DESC
    </select>
</mapper>
