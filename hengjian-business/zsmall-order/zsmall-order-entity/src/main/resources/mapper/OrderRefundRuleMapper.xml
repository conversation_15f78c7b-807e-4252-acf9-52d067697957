<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.order.entity.mapper.OrderRefundRuleMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.order.entity.domain.OrderRefundRule">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="refundRuleNo" column="refund_rule_no" jdbcType="VARCHAR"/>
            <result property="applicableFulfillment" column="applicable_fulfillment" jdbcType="VARCHAR"/>
            <result property="refundReasonZhCn" column="refund_reason_zh_CN" jdbcType="VARCHAR"/>
            <result property="refundReasonEnUs" column="refund_reason_en_US" jdbcType="VARCHAR"/>
            <result property="whetherReviewMd" column="whether_review_md" jdbcType="INTEGER"/>
            <result property="whetherSupportModifyAmount" column="whether_support_modify_amount" jdbcType="INTEGER"/>
            <result property="whetherProvidePictures" column="whether_provide_pictures" jdbcType="INTEGER"/>
            <result property="whetherRestoreInventory" column="whether_restore_inventory" jdbcType="INTEGER"/>
            <result property="sort" column="sort" jdbcType="INTEGER"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,refund_rule_no,applicable_fulfillment,
        refund_reason_zh_CN,refund_reason_en_US,whether_review_md,
        whether_support_modify_amount,whether_provide_pictures,whether_restore_inventory,
        sort,del_flag,create_by,
        create_time,update_by,update_time
    </sql>

    <select id="queryApplicableFulfillment" resultType="java.lang.String">
        SELECT DISTINCT orr.applicable_fulfillment FROM order_refund_rule orr WHERE orr.del_flag = '0'
    </select>
</mapper>
