<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.order.entity.mapper.WholesaleIntentionOrderMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.order.entity.domain.WholesaleIntentionOrder">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
            <result property="wholesaleIntentionOrderNo" column="wholesale_intention_order_no" jdbcType="VARCHAR"/>
            <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
            <result property="totalQuantity" column="total_quantity" jdbcType="INTEGER"/>
            <result property="estimatedOperationFee" column="estimated_operation_fee" jdbcType="DECIMAL"/>
            <result property="estimatedShippingFee" column="estimated_shipping_fee" jdbcType="DECIMAL"/>
            <result property="estimatedHandleTime" column="estimated_handle_time" jdbcType="INTEGER"/>
            <result property="reservedTime" column="reserved_time" jdbcType="INTEGER"/>
            <result property="productAmount" column="product_amount" jdbcType="DECIMAL"/>
            <result property="productAmountPlatform" column="product_amount_platform" jdbcType="DECIMAL"/>
            <result property="depositRatio" column="deposit_ratio" jdbcType="DECIMAL"/>
            <result property="productBalanceAmount" column="product_balance_amount" jdbcType="DECIMAL"/>
            <result property="productBalanceAmountPlatform" column="product_balance_amount_platform" jdbcType="DECIMAL"/>
            <result property="finalOperationFee" column="final_operation_fee" jdbcType="DECIMAL"/>
            <result property="finalOperationFeePlatform" column="final_operation_fee_platform" jdbcType="DECIMAL"/>
            <result property="finalShippingFee" column="final_shipping_fee" jdbcType="DECIMAL"/>
            <result property="finalShippingFeePlatform" column="final_shipping_fee_platform" jdbcType="DECIMAL"/>
            <result property="finalHandleTime" column="final_handle_time" jdbcType="INTEGER"/>
            <result property="orderDepositAmount" column="order_deposit_amount" jdbcType="DECIMAL"/>
            <result property="orderDepositAmountPlatform" column="order_deposit_amount_platform" jdbcType="DECIMAL"/>
            <result property="orderBalanceAmount" column="order_balance_amount" jdbcType="DECIMAL"/>
            <result property="orderBalanceAmountPlatform" column="order_balance_amount_platform" jdbcType="DECIMAL"/>
            <result property="orderTotalAmount" column="order_total_amount" jdbcType="DECIMAL"/>
            <result property="orderTotalAmountPlatform" column="order_total_amount_platform" jdbcType="DECIMAL"/>
            <result property="orderPayableAmount" column="order_payable_amount" jdbcType="DECIMAL"/>
            <result property="orderStage" column="order_stage" jdbcType="INTEGER"/>
            <result property="orderStatus" column="order_status" jdbcType="INTEGER"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,order_no,
        wholesale_intention_order_no,product_code,total_quantity,
        estimated_operation_fee,estimated_shipping_fee,estimated_handle_time,
        reserved_time,product_amount,product_amount_platform,
        deposit_ratio,product_balance_amount,product_balance_amount_platform,
        final_operation_fee,final_operation_fee_platform,final_shipping_fee,
        final_shipping_fee_platform,final_handle_time,order_deposit_amount,
        order_deposit_amount_platform,order_balance_amount,order_balance_amount_platform,
        order_total_amount,order_total_amount_platform,order_payable_amount,
        order_stage,order_status,del_flag,
        create_by,create_time,update_by,
        update_time
    </sql>

    <select id="queryPage" resultMap="BaseResultMap">
        SELECT * FROM wholesale_intention_order wio
        WHERE wio.del_flag = '0'
        <if test="queryDTO.status != null">
            AND wio.order_status = #{queryDTO.status}
        </if>
        <if test="queryDTO.supplierTenantId != null and queryDTO.supplierTenantId != ''">
            AND exists (select 1 from wholesale_intention_order_item wioi where wioi.wholesale_intention_order_id = wio.id AND wioi.del_flag = '0' AND wioi.supplier_tenant_id = #{queryDTO.supplierTenantId})
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryDTO.queryType, 'wiOrderNo')">
            AND wio.wholesale_intention_order_no = #{queryDTO.queryValue}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryDTO.queryType, 'itemNo')">
            AND exists (select 1 from wholesale_intention_order_item wioi where wioi.wholesale_intention_order_id = wio.id AND wioi.del_flag = '0' AND wioi.product_sku_code = #{queryDTO.queryValue})
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryDTO.queryType, 'spuId')">
            AND wio.product_code = #{queryDTO.queryValue}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryDTO.queryType, 'productName')">
            AND exists (select 1 from wholesale_intention_order_item wioi where wioi.wholesale_intention_order_id = wio.id AND wioi.del_flag = '0' AND wioi.product_name LIKE CONCAT('%', #{queryDTO.queryValue}, '%'))
        </if>
        ORDER BY wio.create_time DESC, wio.id DESC
    </select>

    <select id="existsWholesaleIntentionOrderNo" resultType="java.lang.Boolean">
        SELECT COUNT(wio.id)
        FROM wholesale_intention_order wio
        WHERE wio.wholesale_intention_order_no = #{wholesaleIntentionOrderNo}
    </select>
</mapper>
