<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.order.entity.mapper.OrderRefundLogisticsMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.order.entity.domain.OrderRefundLogistics">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="orderRefundId" column="order_refund_id" jdbcType="BIGINT"/>
            <result property="orderRefundItemId" column="order_refund_item_id" jdbcType="BIGINT"/>
            <result property="logisticsReturnType" column="logistics_return_type" jdbcType="VARCHAR"/>
            <result property="logisticsCarrier" column="logistics_carrier" jdbcType="VARCHAR"/>
            <result property="logisticsTrackingNo" column="logistics_tracking_no" jdbcType="VARCHAR"/>
            <result property="logisticsPickupAddress" column="logistics_pickup_address" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_refund_id,order_refund_item_id,
        logistics_return_type,logistics_carrier,logistics_tracking_no,
        logistics_pickup_address,del_flag,create_by,
        create_time,update_by,update_time
    </sql>
</mapper>
