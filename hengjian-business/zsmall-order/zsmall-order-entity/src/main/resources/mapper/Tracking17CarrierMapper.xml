<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.order.entity.mapper.Tracking17CarrierMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.order.entity.domain.Tracking17Carrier">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="carrierKey" column="carrier_key" jdbcType="INTEGER"/>
        <result property="carrierCountry" column="carrier_country" jdbcType="INTEGER"/>
        <result property="carrierCountryIso" column="carrier_country_iso" jdbcType="VARCHAR"/>
        <result property="carrierUrl" column="carrier_url" jdbcType="VARCHAR"/>
        <result property="carrierName" column="carrier_name" jdbcType="VARCHAR"/>
        <result property="carrierNameZhCn" column="carrier_name_zh_cn" jdbcType="VARCHAR"/>
        <result property="carrierNameZhHk" column="carrier_name_zh_hk" jdbcType="VARCHAR"/>
        <result property="carrierGroup" column="carrier_name_zh_hk" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
        create_date_time,
        update_date_time,
        carrier_key,
        carrier_country,
        carrier_country_iso,
        carrier_url,
        carrier_name,
        carrier_name_zh_cn,
        carrier_name_zh_hk,
        carrier_group,
        delete_mark
    </sql>
</mapper>
