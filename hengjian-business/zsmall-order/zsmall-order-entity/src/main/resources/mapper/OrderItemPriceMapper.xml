<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.order.entity.mapper.OrderItemPriceMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.order.entity.domain.OrderItemPrice">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="orderItemId" column="order_item_id" jdbcType="BIGINT"/>
            <result property="orderItemNo" column="order_item_no" jdbcType="VARCHAR"/>
            <result property="productSkuCode" column="product_sku_code" jdbcType="VARCHAR"/>
            <result property="logisticsTypeEnum" column="logistics_type" jdbcType="VARCHAR"/>
            <result property="totalQuantity" column="total_quantity" jdbcType="INTEGER"/>
            <result property="originalUnitPrice" column="original_unit_price" jdbcType="DECIMAL"/>
            <result property="originalOperationFee" column="original_operation_fee" jdbcType="DECIMAL"/>
            <result property="originalFinalDeliveryFee" column="original_final_delivery_fee" jdbcType="DECIMAL"/>
            <result property="originalPickUpPrice" column="original_pick_up_price" jdbcType="DECIMAL"/>
            <result property="originalDropShippingPrice" column="original_drop_shipping_price" jdbcType="DECIMAL"/>
            <result property="originalDepositUnitPrice" column="original_deposit_unit_price" jdbcType="DECIMAL"/>
            <result property="originalBalanceUnitPrice" column="original_balance_unit_price" jdbcType="DECIMAL"/>
            <result property="platformUnitPrice" column="platform_unit_price" jdbcType="DECIMAL"/>
            <result property="platformOperationFee" column="platform_operation_fee" jdbcType="DECIMAL"/>
            <result property="platformFinalDeliveryFee" column="platform_final_delivery_fee" jdbcType="DECIMAL"/>
            <result property="platformPickUpPrice" column="platform_pick_up_price" jdbcType="DECIMAL"/>
            <result property="platformDropShippingPrice" column="platform_drop_shipping_price" jdbcType="DECIMAL"/>
            <result property="platformDepositUnitPrice" column="platform_deposit_unit_price" jdbcType="DECIMAL"/>
            <result property="platformBalanceUnitPrice" column="platform_balance_unit_price" jdbcType="DECIMAL"/>
            <result property="msrp" column="msrp" jdbcType="DECIMAL"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_item_id,order_item_no,
        product_sku_code,logistics_type,total_quantity,
        original_unit_price,original_operation_fee,original_final_delivery_fee,
        original_pick_up_price,original_drop_shipping_price,original_deposit_unit_price,
        original_balance_unit_price,platform_unit_price,platform_operation_fee,
        platform_final_delivery_fee,platform_pick_up_price,platform_drop_shipping_price,
        platform_deposit_unit_price,platform_balance_unit_price,msrp,
        del_flag,create_by,create_time,
        update_by,update_time
    </sql>

    <update id="updateDelFlagByOrderItemIdList">
        update order_item_price
        set del_flag = #{delFlag}
        where
        order_item_id in
        <foreach collection="orderItemIdList" item="orderItemId" open="(" close=")" separator=",">
             #{orderItemId}
        </foreach>
    </update>

</mapper>
