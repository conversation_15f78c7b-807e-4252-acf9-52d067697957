<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.order.entity.mapper.WholesaleIntentionOrderItemMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.order.entity.domain.WholesaleIntentionOrderItem">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="supplierTenantId" column="supplier_tenant_id" jdbcType="VARCHAR"/>
            <result property="orderItemNo" column="order_item_no" jdbcType="VARCHAR"/>
            <result property="wholesaleIntentionOrderId" column="wholesale_intention_order_id" jdbcType="BIGINT"/>
            <result property="wholesaleIntentionOrderItemNo" column="wholesale_intention_order_item_no" jdbcType="VARCHAR"/>
            <result property="ownSpec" column="own_spec" jdbcType="VARCHAR"/>
            <result property="productName" column="product_name" jdbcType="VARCHAR"/>
            <result property="productSkuCode" column="product_sku_code" jdbcType="VARCHAR"/>
            <result property="imageShowUrl" column="image_show_url" jdbcType="VARCHAR"/>
            <result property="imageSavePath" column="image_save_path" jdbcType="VARCHAR"/>
            <result property="quantity" column="quantity" jdbcType="INTEGER"/>
            <result property="depositRatio" column="deposit_ratio" jdbcType="DECIMAL"/>
            <result property="unitPrice" column="unit_price" jdbcType="DECIMAL"/>
            <result property="unitPricePlatform" column="unit_price_platform" jdbcType="DECIMAL"/>
            <result property="depositUnitPrice" column="deposit_unit_price" jdbcType="DECIMAL"/>
            <result property="depositUnitPricePlatform" column="deposit_unit_price_platform" jdbcType="DECIMAL"/>
            <result property="balanceUnitPrice" column="balance_unit_price" jdbcType="DECIMAL"/>
            <result property="balanceUnitPricePlatform" column="balance_unit_price_platform" jdbcType="DECIMAL"/>
            <result property="depositTotalAmount" column="deposit_total_amount" jdbcType="DECIMAL"/>
            <result property="depositTotalAmountPlatform" column="deposit_total_amount_platform" jdbcType="DECIMAL"/>
            <result property="balanceTotalAmount" column="balance_total_amount" jdbcType="DECIMAL"/>
            <result property="balanceTotalAmountPlatform" column="balance_total_amount_platform" jdbcType="DECIMAL"/>
            <result property="totalAmount" column="total_amount" jdbcType="DECIMAL"/>
            <result property="totalAmountPlatform" column="total_amount_platform" jdbcType="DECIMAL"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,supplier_tenant_id,
        order_item_no,wholesale_intention_order_id,wholesale_intention_order_item_no,
        own_spec,product_name,product_sku_code,
        image_show_url,image_save_path,quantity,
        deposit_ratio,unit_price,unit_price_platform,
        deposit_unit_price,deposit_unit_price_platform,balance_unit_price,
        balance_unit_price_platform,deposit_total_amount,deposit_total_amount_platform,
        balance_total_amount,balance_total_amount_platform,total_amount,
        total_amount_platform,del_flag,create_by,
        create_time,update_by,update_time
    </sql>

    <select id="existsWholesaleIntentionOrderItemNo" resultType="java.lang.Boolean">
        SELECT COUNT(wioi.id)
        FROM wholesale_intention_order_item wioi
        WHERE wioi.wholesale_intention_order_item_no = #{wholesaleIntentionOrderNo}
    </select>

    <select id="existsProcessingOrderItem" resultMap="BaseResultMap">
        SELECT *
        FROM wholesale_intention_order_item wioi
        LEFT JOIN wholesale_intention_order wio on wioi.wholesale_intention_order_id = wio.id
        WHERE wio.del_flag = '0'
        AND wioi.del_flag = '0'
        AND wio.order_status = 1
        AND wioi.product_sku_code IN
        <foreach collection="productSkuCodeList" item="item" index="index" open="(" separator="," close=")">#{item}</foreach>
    </select>

</mapper>
