package com.zsmall.order.entity.mapper;

import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.order.entity.domain.OrderRefundRule;
import com.zsmall.order.entity.domain.vo.OrderRefundRuleVo;

import java.util.List;

/**
 * 售后规则Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-08
 */
public interface OrderRefundRuleMapper extends BaseMapperPlus<OrderRefundRule, OrderRefundRuleVo> {

    List<String> queryApplicableFulfillment();

}
