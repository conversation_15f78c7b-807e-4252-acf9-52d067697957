package com.zsmall.order.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.order.entity.domain.Orders;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/11/4 11:59
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderCodeGeneratorV2 {
    final private IOrdersService iOrdersService;
    private static final String PREFIX = "ZD";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMdd");
    private static final int RANDOM_NUMBER_LENGTH = 6;
    private static final Random RANDOM = new Random();
    private static final Lock lock = new ReentrantLock();

    /**
     * 功能描述：生成订单号
     *
     * @param businessCodeEnum 业务代码枚举
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/11/04
     */
    public String generateOrderNumber(BusinessCodeEnum businessCodeEnum) {
        String dateStr = DATE_FORMAT.format(new Date());
        String orderNumber = null;

        lock.lock();
        try {
            while (true) {
                if(BusinessCodeEnum.NewOrderNo.equals(businessCodeEnum)){
                    int randomNumber = RANDOM.nextInt((int) Math.pow(10, RANDOM_NUMBER_LENGTH)) + (int) Math.pow(10, RANDOM_NUMBER_LENGTH - 1);
                    String randomNumberStr = String.format("%06d", randomNumber);
                    orderNumber = PREFIX + dateStr + randomNumberStr;
                    LambdaQueryWrapper<Orders> wrapper = new LambdaQueryWrapper<Orders>().eq(Orders::getOrderNo, orderNumber);
                    List<Orders> orders = iOrdersService.list(wrapper);
                    List<String> orderNos = orders.stream().map(Orders::getOrderNo).collect(Collectors.toList());
                    // 检查订单号是否已存在
                    if (!orderNos.contains(orderNumber)) {
                        break;
                    }
                }

            }
        } finally {
            lock.unlock();
        }

        return orderNumber;
    }
}
