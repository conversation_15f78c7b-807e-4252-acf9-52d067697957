package com.zsmall.order.entity.domain.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/4/15 11:24
 */
@Data
public class OrderRefundMsgDTO {

    // 基础信息
    private String storeName;               // 3.2 店铺名称
    private Date returnDate;           // 3.3 退货日期
    private String returnStatus;            // 3.4 退货状态
    private Date orderCreateTime;            // 3.6 订单创建日期

    // 物流信息
    private Date shippingDate;         // 3.7 发货日期
    private String logisticsType;          // 3.9 发货方式
    private String labelSource;             // 3.10 面单来源
    private String currency;                // 3.16 币种

    // 退款信息
    private BigDecimal refundAllAmount;       // 3.17 退款总金额


    // 订单金额
    private BigDecimal orderAllAmount;         // 3.22 订单金额总额
    private Integer orderQuantity;          // 3.23 订单商品总数量
    private String channelOrderNo;          // 渠道订单号
    // 注意：处理重复编号字段（按上下文推测）
    private String channelType;             // 4.2 渠道类型（如temu）
    private String channelStore;            // 4.3 渠道店铺

//    private String categoryThreeLevel;    // 4.6 商品分类（三级分类）
    private String buyerName;               // 4.7 买家姓名
    private String refundMethod;            // 4.8 退款方式
    private BigDecimal refundTotalAmount;   // 5.0 退款总金额
    private String refundStatus;            // 5.1 退款状态

    private List<OrderRefundMsgDetailDTO> orderRefundMsgDetailDTOList; // 5.2 退款详情列表

    /**
     * 功能描述：将订单退款消息详细数据列表
     *
     * @param orderRefundMsgDetailDTOList 订单退款消息详细数据列表
     * @return {@link List }<{@link OrderRefundMsgDetailDTO }>
     * <AUTHOR>
     * @date 2025/04/15
     */
    private List<OrderRefundMsgDetailDTO> putOrderRefundMsgDetailDTOList(List<OrderRefundMsgDetailDTO> orderRefundMsgDetailDTOList){
        this.orderRefundMsgDetailDTOList = orderRefundMsgDetailDTOList;
        return orderRefundMsgDetailDTOList;
    }
}
