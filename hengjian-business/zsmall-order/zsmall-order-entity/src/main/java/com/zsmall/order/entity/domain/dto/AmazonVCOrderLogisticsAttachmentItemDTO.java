package com.zsmall.order.entity.domain.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024年10月29日  16:05
 * @description:amazonVC订单获取面单信息item类
 */
@Data
@Accessors(chain = true)
public class AmazonVCOrderLogisticsAttachmentItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String deliverOrderNo;

    private Long deliverOrderId;

    private String channelOrderItemId;

    private BigDecimal length;

    private BigDecimal width;

    private BigDecimal height;

    private BigDecimal weight;

    private Integer quantity;
}
