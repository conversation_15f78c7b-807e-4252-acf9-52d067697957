package com.zsmall.order.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.order.OrderExceptionEnum;
import com.zsmall.order.entity.domain.OrderLogisticsInfo;
import com.zsmall.order.entity.domain.bo.OrderLogisticsInfoBo;
import com.zsmall.order.entity.domain.vo.OrderLogisticsInfoVo;
import com.zsmall.order.entity.mapper.OrderLogisticsInfoMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 主订单物流信息Service接口
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@RequiredArgsConstructor
@Service
public class IOrderLogisticsInfoService extends ServiceImpl<OrderLogisticsInfoMapper, OrderLogisticsInfo> {

    private final OrderLogisticsInfoMapper baseMapper;


    /**
     * 查询主订单物流信息
     */
    public OrderLogisticsInfoVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询主订单物流信息列表
     */
    public TableDataInfo<OrderLogisticsInfoVo> queryPageList(OrderLogisticsInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OrderLogisticsInfo> lqw = buildQueryWrapper(bo);
        Page<OrderLogisticsInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询主订单物流信息列表
     */
    public List<OrderLogisticsInfoVo> queryList(OrderLogisticsInfoBo bo) {
        LambdaQueryWrapper<OrderLogisticsInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OrderLogisticsInfo> buildQueryWrapper(OrderLogisticsInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrderLogisticsInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getOrderId() != null, OrderLogisticsInfo::getOrderId, bo.getOrderId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), OrderLogisticsInfo::getOrderNo, bo.getOrderNo());
        lqw.eq(StringUtils.isNotBlank(bo.getLogisticsType()), OrderLogisticsInfo::getLogisticsType, bo.getLogisticsType());
        // lqw.eq(StringUtils.isNotBlank(bo.getLogisticsErrorMessage()), OrderLogisticsInfo::getLogisticsErrorMessage, bo.getLogisticsErrorMessage());
        lqw.like(StringUtils.isNotBlank(bo.getLogisticsCompanyName()), OrderLogisticsInfo::getLogisticsCompanyName, bo.getLogisticsCompanyName());
        lqw.like(StringUtils.isNotBlank(bo.getLogisticsServiceName()), OrderLogisticsInfo::getLogisticsServiceName, bo.getLogisticsServiceName());
        lqw.eq(StringUtils.isNotBlank(bo.getLogisticsAccount()), OrderLogisticsInfo::getLogisticsAccount, bo.getLogisticsAccount());
        lqw.eq(StringUtils.isNotBlank(bo.getLogisticsAccountZipCode()), OrderLogisticsInfo::getLogisticsAccountZipCode, bo.getLogisticsAccountZipCode());
        lqw.eq(bo.getShippingLabelExist() != null, OrderLogisticsInfo::getShippingLabelExist, bo.getShippingLabelExist());
        lqw.like(StringUtils.isNotBlank(bo.getShippingLabelFileName()), OrderLogisticsInfo::getShippingLabelFileName, bo.getShippingLabelFileName());
        lqw.eq(bo.getShippingLabelOssId() != null, OrderLogisticsInfo::getShippingLabelOssId, bo.getShippingLabelOssId());
        lqw.eq(StringUtils.isNotBlank(bo.getShippingLabelSavePath()), OrderLogisticsInfo::getShippingLabelSavePath, bo.getShippingLabelSavePath());
        lqw.eq(StringUtils.isNotBlank(bo.getShippingLabelShowUrl()), OrderLogisticsInfo::getShippingLabelShowUrl, bo.getShippingLabelShowUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getLogisticsZipCode()), OrderLogisticsInfo::getLogisticsZipCode, bo.getLogisticsZipCode());
        lqw.eq(StringUtils.isNotBlank(bo.getZipCode()), OrderLogisticsInfo::getZipCode, bo.getZipCode());
        return lqw;
    }

    /**
     * 新增主订单物流信息
     */
    public Boolean insertByBo(OrderLogisticsInfoBo bo) {
        OrderLogisticsInfo add = MapstructUtils.convert(bo, OrderLogisticsInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改主订单物流信息
     */
    public Boolean updateByBo(OrderLogisticsInfoBo bo) {
        OrderLogisticsInfo update = MapstructUtils.convert(bo, OrderLogisticsInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrderLogisticsInfo entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除主订单物流信息
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据orderID获取主订单物流信息
     * @param orderId
     * @return
     */
    public OrderLogisticsInfo getByOrderId(Long orderId) {
        LambdaQueryWrapper<OrderLogisticsInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderLogisticsInfo::getOrderId, orderId);
        return baseMapper.selectOne(lqw);
    }

    /**
     * 根据订单编号获取主订单物流信息
     * @param orderNo
     * @return
     */
    public OrderLogisticsInfo getByOrderNo(String orderNo) {
        LambdaQueryWrapper<OrderLogisticsInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderLogisticsInfo::getOrderNo, orderNo);
        return baseMapper.selectOne(lqw);
    }

    /**
     * 功能描述：更新或设置为空
     *
     * @param orderNo              订单号
     * @param logisticsCarrierCode 物流公司
     * @param logisticsCode
     * <AUTHOR>
     * @date 2024/08/20
     */
    public void updateOrSetNull(String orderNo, String logisticsCarrierCode, String logisticsCode) {
        LambdaUpdateWrapper<OrderLogisticsInfo> wrapper = new LambdaUpdateWrapper<OrderLogisticsInfo>();
        if(ObjectUtil.isNotEmpty(logisticsCarrierCode)){
            wrapper
                .eq(OrderLogisticsInfo::getOrderNo, orderNo)
                .set(OrderLogisticsInfo::getLogisticsCarrierCode, logisticsCarrierCode)
                .set(OrderLogisticsInfo::getLogisticsCompanyName, logisticsCarrierCode);

            if(ObjectUtil.isNotEmpty(logisticsCode)){
                wrapper
                    .eq(OrderLogisticsInfo::getOrderNo, orderNo)
                    .set(OrderLogisticsInfo::getLogisticsServiceName, logisticsCode);
            }
            TenantHelper.ignore(() -> update(wrapper));
        }else {
            wrapper
                .eq(OrderLogisticsInfo::getOrderNo, orderNo)
                .set(OrderLogisticsInfo::getLogisticsCarrierCode, null)
                .set(OrderLogisticsInfo::getLogisticsServiceName, null);
            TenantHelper.ignore(() -> update(null,wrapper));
        }

    }

    public void updateBatchOrSetNull(List<OrderLogisticsInfo> orderLogisticsInfos, HashMap<String, Integer> codesMap) {
        if(CollUtil.isNotEmpty(orderLogisticsInfos)){
            updateBatchById(orderLogisticsInfos);
        }
        //updateOrderList转换为map,key是orderNo,value是对应元素
        Map<String, OrderLogisticsInfo> orderMap = orderLogisticsInfos.stream()
                                                      .collect(Collectors.toMap(OrderLogisticsInfo::getOrderNo, Function.identity()));
        List<Long> errorOrders = new ArrayList<>();
        List<OrderLogisticsInfo> updateOrders = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : codesMap.entrySet()) {
            // 考虑一点,库存异常实际也是没有尾程派送费的 后续会补充 tag lty todo 自提的库存异常是不用额外操作的
            if (OrderExceptionEnum.final_delivery_fee_exception.getValue().equals(entry.getValue())
                ||OrderExceptionEnum.out_of_stock_exception.getValue().equals(entry.getValue())
                ||OrderExceptionEnum.measurement_anomaly.getValue().equals(entry.getValue())) {
                errorOrders.add(orderMap.get(entry.getKey()).getId());
            }else {
                updateOrders.add(orderMap.get(entry.getKey()));
            }
        }
        if(CollUtil.isNotEmpty(orderLogisticsInfos)){
            LambdaUpdateWrapper<OrderLogisticsInfo> updateWrapper = new LambdaUpdateWrapper<>();
            if(ObjectUtil.isNotEmpty(errorOrders)){
                updateWrapper
                    .in(OrderLogisticsInfo::getId, errorOrders)
                    .set(OrderLogisticsInfo::getLogisticsCarrierCode, null)
                    .set(OrderLogisticsInfo::getLogisticsServiceName, null);
                TenantHelper.ignore(() -> update(null, updateWrapper));
            }
            if(ObjectUtil.isNotEmpty(updateOrders)){
                TenantHelper.ignore(()->baseMapper.updateBatchById(updateOrders));
            }

        }

    }
    public void updateBatchOrSetNull(List<OrderLogisticsInfo> orderLogisticsInfos, Integer exceptionCode) {
        if(OrderExceptionEnum.final_delivery_fee_exception.getValue().equals(exceptionCode)
            ||OrderExceptionEnum.measurement_anomaly.getValue().equals(exceptionCode)
            ||OrderExceptionEnum.out_of_stock_exception.getValue().equals(exceptionCode)){
            LambdaUpdateWrapper<OrderLogisticsInfo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(OrderLogisticsInfo::getLogisticsServiceName,null);
            updateWrapper.set(OrderLogisticsInfo::getLogisticsCompanyName, null);
            updateWrapper.set(OrderLogisticsInfo::getLogisticsCarrierCode, null);
            updateWrapper.in(OrderLogisticsInfo::getId,orderLogisticsInfos.stream().map(OrderLogisticsInfo::getId).collect(Collectors.toList()));
            TenantHelper.ignore(() -> baseMapper.update(null,updateWrapper));
        }else {
            if(CollUtil.isNotEmpty(orderLogisticsInfos)&&OrderExceptionEnum.normal.getValue().equals(exceptionCode)){
                baseMapper.updateBatchById(orderLogisticsInfos);
            }
        }

    }

    public Map<String, OrderLogisticsInfo> getByOrderNos(List<String> orderNos) {
        LambdaQueryWrapper<OrderLogisticsInfo> lqw = Wrappers.lambdaQuery();
        lqw.in(OrderLogisticsInfo::getOrderNo, orderNos);
        List<OrderLogisticsInfo> list = list(lqw);

        return list.stream()
                   .collect(Collectors.toMap(OrderLogisticsInfo::getOrderNo, Function.identity()));
    }
}
