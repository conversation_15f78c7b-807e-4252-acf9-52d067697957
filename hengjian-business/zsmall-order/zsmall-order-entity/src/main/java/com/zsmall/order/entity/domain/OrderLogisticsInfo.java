package com.zsmall.order.entity.domain;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.zsmall.common.domain.SortEntity;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * 主订单物流信息对象 order_logistics_info
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "order_logistics_info", autoResultMap = true)
public class OrderLogisticsInfo extends SortEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 订单表主键
     */
    private Long orderId;

    /**
     * 主订单编号
     */
    private String orderNo;

    /**
     * 物流类型：PickUp-自提，DropShipping-代发
     */
    private LogisticsTypeEnum logisticsType;

//    "Standard-48 Hours" ：标准（48小时发货）
//        "Same Day Ship Out" :（加急当日发货）不填为标准
    private String shipServiceLevel;
    /**
     * 物流错误信息
     */
    @TableField(typeHandler = JacksonTypeHandler.class, updateStrategy = FieldStrategy.IGNORED)
    private JSONObject logisticsErrorMessage;

    /**
     * 物流公司名称（等于承运商）
     */
    @TableField(fill = FieldFill.UPDATE)
    private String logisticsCompanyName;

    /**
     * 物流服务名称
     */
    @TableField(fill = FieldFill.UPDATE)
    private String logisticsServiceName;

    /**
     * erp返回物流
     */
    @TableField(fill = FieldFill.UPDATE)
    private String logisticsCarrierCode;
    /**
     * 物流账户
     */
    @TableField(fill = FieldFill.UPDATE)
    private String logisticsAccount;

    /**
     * 物流账户邮政编码
     */
    @TableField (fill = FieldFill.UPDATE)
    private String logisticsAccountZipCode;

    /**
     * 是否存在运输标签（0-否，1-是）
     */
    private Boolean shippingLabelExist = false;

    /**
     * 运输标签源文件名
     */
    private String shippingLabelFileName;

    /**
     * 运输标签存储对象主键
     */
    private Long shippingLabelOssId;

    /**
     * 运输标签存储地址
     */
    private String shippingLabelSavePath;

    /**
     * 运输标签展示地址
     */
    private String shippingLabelShowUrl;

    /**
     * 物流用邮编（匹配物流模板只需要用到五位数的邮编，但是美国有可能会出现xxxxx-xxxx的格式，此处存放的就是切割后前半部分的五位数邮编）
     */
    private String logisticsZipCode;

    /**
     * 物流用国家代号（两位英文大写）
     */
    private String logisticsCountryCode;

    /**
     * 完整邮编
     */
    private String zipCode;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
