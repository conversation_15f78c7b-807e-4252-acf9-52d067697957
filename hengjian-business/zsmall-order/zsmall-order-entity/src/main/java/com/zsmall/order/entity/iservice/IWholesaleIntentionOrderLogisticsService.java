package com.zsmall.order.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.order.entity.domain.WholesaleIntentionOrderLogistics;
import com.zsmall.order.entity.mapper.WholesaleIntentionOrderLogisticsMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【wholesale_intention_order_logistics(国外现货批发意向订单物流信息表)】的数据库操作Service实现
* @createDate 2023-07-24 10:46:41
*/
@Service
public class IWholesaleIntentionOrderLogisticsService extends ServiceImpl<WholesaleIntentionOrderLogisticsMapper, WholesaleIntentionOrderLogistics> {


    @InMethodLog(value = "根据主订单id查询子批发意向订单物流")
    public WholesaleIntentionOrderLogistics queryWIOrderId(Long wiOrderId) {
        return lambdaQuery().eq(WholesaleIntentionOrderLogistics::getWholesaleIntentionOrderId, wiOrderId).one();
    }
}




