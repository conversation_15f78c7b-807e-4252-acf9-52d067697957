package com.zsmall.order.entity.domain.dto;

import com.zsmall.common.annotaion.ExcelFieldAnnotation;
import com.zsmall.common.domain.dto.ExcelBaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 批量发货导入DTO
 *
 * <AUTHOR>
 * @date 2023/6/8
 */
@Data
@EqualsAndHashCode(callSuper=false)
@AllArgsConstructor
@NoArgsConstructor
public class DispatchedImportDTO extends ExcelBaseDTO {

    @ExcelFieldAnnotation(column = "Order ID")
    private String orderID;
    @ExcelFieldAnnotation(column = "Carrier Code")
    private String carrierCode;
    @ExcelFieldAnnotation(column = "Logistics Service")
    private String logisticsService;
    @ExcelFieldAnnotation(column = "Tracking No.")
    private String trackingNo;


}
