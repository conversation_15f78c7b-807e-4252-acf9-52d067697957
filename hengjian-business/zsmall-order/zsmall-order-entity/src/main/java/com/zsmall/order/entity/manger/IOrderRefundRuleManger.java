package com.zsmall.order.entity.manger;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.zsmall.common.annotaion.Manager;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.enums.order.LogisticsProgress;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.order.entity.domain.OrderItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.Objects;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/3/12 14:48
 */
@RequiredArgsConstructor
@Manager
@Slf4j
public class IOrderRefundRuleManger {
    private final BusinessParameterService businessParameterService;
    public Boolean compareRefundRules(OrderItem orderItem, Boolean existsOrderRefund) {
        if (!existsOrderRefund) {
            return false;
        }
        Boolean canRefund = true;
        if (Objects.equals(orderItem.getFulfillmentProgress(), LogisticsProgress.Fulfilled)) {
            Date fulfillmentTime = orderItem.getFulfillmentTime();
            if (fulfillmentTime != null) {
                Integer refundDays = businessParameterService.getValueFromInteger(BusinessParameterType.REFUND_DAYS);
                int between =(int) DateUtil.between(fulfillmentTime, new Date(), DateUnit.DAY);
                canRefund = refundDays.compareTo(between) >= 0;
            } else {
                canRefund = false;
            }
        }
        return canRefund;
    }
}
