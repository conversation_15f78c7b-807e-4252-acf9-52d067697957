package com.zsmall.order.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.order.OrderExceptionEnum;
import com.zsmall.order.entity.domain.OrderItemProductSku;
import com.zsmall.order.entity.domain.bo.OrderItemProductSkuBo;
import com.zsmall.order.entity.domain.vo.OrderItemProductSkuVo;
import com.zsmall.order.entity.mapper.OrderItemProductSkuMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 子订单商品SKU信息Service接口
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@RequiredArgsConstructor
@Service
public class IOrderItemProductSkuService extends ServiceImpl<OrderItemProductSkuMapper, OrderItemProductSku> {

    private final OrderItemProductSkuMapper baseMapper;

    /**
     * 查询子订单商品SKU信息
     */
    public OrderItemProductSkuVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询子订单商品SKU信息列表
     */
    public TableDataInfo<OrderItemProductSkuVo> queryPageList(OrderItemProductSkuBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OrderItemProductSku> lqw = buildQueryWrapper(bo);
        Page<OrderItemProductSkuVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询子订单商品SKU信息列表
     */
    public List<OrderItemProductSkuVo> queryList(OrderItemProductSkuBo bo) {
        LambdaQueryWrapper<OrderItemProductSku> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OrderItemProductSku> buildQueryWrapper(OrderItemProductSkuBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrderItemProductSku> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getSupplierTenantId()), OrderItemProductSku::getSupplierTenantId, bo.getSupplierTenantId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), OrderItemProductSku::getOrderNo, bo.getOrderNo());
        lqw.eq(bo.getOrderItemId() != null, OrderItemProductSku::getOrderItemId, bo.getOrderItemId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderItemNo()), OrderItemProductSku::getOrderItemNo, bo.getOrderItemNo());
        lqw.eq(StringUtils.isNotBlank(bo.getChannelType()), OrderItemProductSku::getChannelType, bo.getChannelType());
        lqw.eq(bo.getChannelId() != null, OrderItemProductSku::getChannelId, bo.getChannelId());
        lqw.eq(bo.getChannelVariantId() != null, OrderItemProductSku::getChannelVariantId, bo.getChannelVariantId());
        lqw.eq(StringUtils.isNotBlank(bo.getChannelWarehouseCode()), OrderItemProductSku::getChannelWarehouseCode, bo.getChannelWarehouseCode());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), OrderItemProductSku::getProductCode, bo.getProductCode());
        lqw.eq(StringUtils.isNotBlank(bo.getProductSkuCode()), OrderItemProductSku::getProductSkuCode, bo.getProductSkuCode());
        lqw.eq(StringUtils.isNotBlank(bo.getSku()), OrderItemProductSku::getSku, bo.getSku());
        lqw.eq(StringUtils.isNotBlank(bo.getUpc()), OrderItemProductSku::getUpc, bo.getUpc());
        lqw.eq(StringUtils.isNotBlank(bo.getErpSku()), OrderItemProductSku::getErpSku, bo.getErpSku());
        lqw.eq(StringUtils.isNotBlank(bo.getMappingSku()), OrderItemProductSku::getMappingSku, bo.getMappingSku());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), OrderItemProductSku::getProductName, bo.getProductName());
        lqw.eq(StringUtils.isNotBlank(bo.getDescription()), OrderItemProductSku::getDescription, bo.getDescription());
        lqw.eq(bo.getImageOssId() != null, OrderItemProductSku::getImageOssId, bo.getImageOssId());
        lqw.eq(StringUtils.isNotBlank(bo.getImageSavePath()), OrderItemProductSku::getImageSavePath, bo.getImageSavePath());
        lqw.eq(StringUtils.isNotBlank(bo.getImageShowUrl()), OrderItemProductSku::getImageShowUrl, bo.getImageShowUrl());
        lqw.like(StringUtils.isNotBlank(bo.getSpecComposeName()), OrderItemProductSku::getSpecComposeName, bo.getSpecComposeName());
        lqw.like(StringUtils.isNotBlank(bo.getSpecValName()), OrderItemProductSku::getSpecValName, bo.getSpecValName());
        lqw.eq(StringUtils.isNotBlank(bo.getSpecifyWarehouse()), OrderItemProductSku::getSpecifyWarehouse, bo.getSpecifyWarehouse());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseSystemCode()), OrderItemProductSku::getWarehouseSystemCode, bo.getWarehouseSystemCode());
        lqw.eq(StringUtils.isNotBlank(bo.getActivityType()), OrderItemProductSku::getActivityType, bo.getActivityType());
        lqw.eq(StringUtils.isNotBlank(bo.getActivityCode()), OrderItemProductSku::getActivityCode, bo.getActivityCode());
        lqw.eq(OrderItemProductSku::getDelFlag,0);
        return lqw;
    }

    /**
     * 根据orderItemId子订单SKU信息
     *
     * @param orderItemId
     * @return
     */
    public OrderItemProductSku getByOrderItemId(Long orderItemId) {
        String tenantType = LoginHelper.getTenantType();
        LambdaQueryWrapper<OrderItemProductSku> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderItemProductSku::getOrderItemId, orderItemId).eq(OrderItemProductSku::getDelFlag,0);
        if (!TenantType.Distributor.name().equals(tenantType)) {
            return baseMapper.queryByWrapperNoTenant(lqw);
        } else {
            return baseMapper.selectOne(lqw);
        }
    }

    /**
     * 根据orderItemId子订单SKU信息
     *
     * @param orderItemId
     * @return
     */
    public OrderItemProductSku getByOrderItemIdNoTenant(Long orderItemId) {
        LambdaQueryWrapper<OrderItemProductSku> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderItemProductSku::getOrderItemId, orderItemId).eq(OrderItemProductSku::getDelFlag,0);
        return baseMapper.queryByWrapperNoTenant(lqw);
    }


    /**
     * 根据订单明细id修改删除标识
     *
     * @param delFlag
     * @param orderItemIdList
     */
    public void updateDelFlagByOrderItemIdList(List<Long> orderItemIdList, Integer delFlag){
        if(CollUtil.isNotEmpty(orderItemIdList) && null != delFlag){
            baseMapper.updateDelFlagByOrderItemIdList(orderItemIdList,delFlag);
        }
    }

    public void updateOrSetNull(String orderNo, String warehouseSystemCode, String warehouseCode) {
        if(ObjectUtil.isNotEmpty(warehouseSystemCode)){
            LambdaUpdateWrapper<OrderItemProductSku> wrapper = new LambdaUpdateWrapper<OrderItemProductSku>()
                .eq(OrderItemProductSku::getOrderNo, orderNo)
                .set(OrderItemProductSku::getWarehouseSystemCode, warehouseSystemCode)
                .set(OrderItemProductSku::getSpecifyWarehouse,warehouseCode);
            TenantHelper.ignore(() -> update(wrapper));
        }else {
            LambdaUpdateWrapper<OrderItemProductSku> wrapper = new LambdaUpdateWrapper<OrderItemProductSku>()
                .eq(OrderItemProductSku::getOrderNo, orderNo)
                .set(OrderItemProductSku::getWarehouseSystemCode, null);
            TenantHelper.ignore(() -> update(null,wrapper));
        }

    }
    public void updateOrSetNull(List<OrderItemProductSku> productSkus, Integer exceptionCode) {
        if(OrderExceptionEnum.final_delivery_fee_exception.getValue().equals(exceptionCode)
            ||OrderExceptionEnum.measurement_anomaly.getValue().equals(exceptionCode)
            ||OrderExceptionEnum.out_of_stock_exception.getValue().equals(exceptionCode)){
            LambdaUpdateWrapper<OrderItemProductSku> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(OrderItemProductSku::getWarehouseSystemCode,null);
            updateWrapper.set(OrderItemProductSku::getSpecifyWarehouse,null);
            updateWrapper.in(OrderItemProductSku::getId,productSkus.stream().map(OrderItemProductSku::getId).collect(Collectors.toList()));
            TenantHelper.ignore(() -> baseMapper.update(null,updateWrapper));
        }else {
            if(CollUtil.isNotEmpty(productSkus)&&OrderExceptionEnum.normal.getValue().equals(exceptionCode)){
                TenantHelper.ignore(()->baseMapper.updateBatchById(productSkus));
            }
        }

    }

    /**
     * 根据订单编号查询子订单商品sku信息根据订单号查询订单明细SKU
     * @param orderNo
     * @return
     */
    public List<OrderItemProductSku> queryListByOrderNo(String orderNo) {
        LambdaQueryWrapper<OrderItemProductSku> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderItemProductSku::getOrderNo, orderNo);
        return baseMapper.selectList(lqw);
    }

    /**
     * 功能描述：获取订单号和产品sku图
     *
     * @param orderNos 订单编号
     * @return {@link Map }<{@link String }, {@link OrderItemProductSku }>
     * <AUTHOR>
     * @date 2025/03/31
     */
    public Map<String, OrderItemProductSku> getOrderNoAndProductSkuMap(List<String> orderNos) {
        LambdaQueryWrapper<OrderItemProductSku> lqw = Wrappers.lambdaQuery();
        lqw.in(OrderItemProductSku::getOrderNo, orderNos);
        lqw.eq(OrderItemProductSku::getDelFlag,0);
        List<OrderItemProductSku> productSkus = baseMapper.selectList(lqw);
        if(CollUtil.isNotEmpty(productSkus)){
            return productSkus.stream()
                              .collect(Collectors.toMap(OrderItemProductSku::getOrderNo, Function.identity()));
        }
        return null;
    }
}
