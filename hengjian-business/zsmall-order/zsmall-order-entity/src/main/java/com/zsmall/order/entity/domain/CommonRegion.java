package com.zsmall.order.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.zsmall.common.enums.order.GlobalConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Access;
import javax.persistence.AccessType;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2023/12/12 17:37
 */
@Getter
@Setter
@Accessors(chain = true)
@Access(AccessType.FIELD)
@JsonInclude(JsonInclude.Include.USE_DEFAULTS)
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "common_regions")
public class CommonRegion {

    private Integer depth =0;

    private Integer parentId;

    /**
     * 限定范围
     */

    private int scope;

    /**
     * 唯一代码标识
     */

    private String code;

    /**
     * 标识别名
     */

    private String codeAlias;

    /**
     * 标题名称
     */

    private String name;

    /**
     * 标题名称英文
     */

    private String nameEn;

    /**
     * 说明备注
     */

    private String description;

    private Integer disabledAt;

    private Integer disabledBy = 0;


    private String disabledName = GlobalConstant.NONE_VALUE;

}
