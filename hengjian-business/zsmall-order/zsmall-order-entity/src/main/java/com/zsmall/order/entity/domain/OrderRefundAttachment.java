package com.zsmall.order.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.domain.SortEntity;
import com.zsmall.common.enums.order.OrderAttachmentTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 售后申请主单附件对象 order_attachment
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("order_refund_attachment")
public class OrderRefundAttachment extends SortEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 存储对象主键
     */
    private Long ossId;

    /**
     * 退款主单编号
     */
    private String orderRefundNo;

    /**
     * 附件名称
     */
    private String attachmentName;

    /**
     * 附件原名
     */
    private String attachmentOriginalName;

    /**
     * 附件后缀
     */
    private String attachmentSuffix;

    /**
     * 附件存放路径
     */
    private String attachmentSavePath;

    /**
     * 附件展示地址
     */
    private String attachmentShowUrl;

    /**
     * 附件排序
     */
    private Integer attachmentSort;

    /**
     * 附件类型
     */
    private OrderAttachmentTypeEnum attachmentType;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
