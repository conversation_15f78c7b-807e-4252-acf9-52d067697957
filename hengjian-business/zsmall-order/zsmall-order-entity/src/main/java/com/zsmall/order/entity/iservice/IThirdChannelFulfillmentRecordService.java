package com.zsmall.order.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.FulfillmentPushStateEnum;
import com.zsmall.order.entity.domain.ThirdChannelFulfillmentRecord;
import com.zsmall.order.entity.mapper.ThirdChannelFulfillmentRecordMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 第三方渠道履约记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-30
 */
@RequiredArgsConstructor
@Service
public class IThirdChannelFulfillmentRecordService extends ServiceImpl<ThirdChannelFulfillmentRecordMapper, ThirdChannelFulfillmentRecord> {

    @InMethodLog("根据销售渠道查询等待推送的履约记录")
    public List<ThirdChannelFulfillmentRecord> queryByChannelTypeAndWaitPush(ChannelTypeEnum channelType) {
        return this.lambdaQuery()
            .eq(ThirdChannelFulfillmentRecord::getChannelType, channelType)
            .eq(ThirdChannelFulfillmentRecord::getFulfillmentPushState, FulfillmentPushStateEnum.WaitPush).list();
    }


}
