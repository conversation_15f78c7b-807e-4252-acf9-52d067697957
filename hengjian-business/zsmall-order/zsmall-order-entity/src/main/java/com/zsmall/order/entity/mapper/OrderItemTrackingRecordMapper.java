package com.zsmall.order.entity.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.order.entity.domain.OrderItemTrackingRecord;
import com.zsmall.order.entity.domain.dto.TrackingPageDTO;
import com.zsmall.order.entity.domain.vo.OrderItemTrackingRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 子订单物流跟踪单Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-06
 */
public interface OrderItemTrackingRecordMapper extends BaseMapperPlus<OrderItemTrackingRecord, OrderItemTrackingRecordVo> {

    Page<OrderItemTrackingRecordVo> queryPage(Page<OrderItemTrackingRecord> page, @Param("query") TrackingPageDTO query);
    List<OrderItemTrackingRecordVo> queryPage(@Param("query") TrackingPageDTO query);

    List<OrderItemTrackingRecord> queryInTransitTrackingRecord(@Param("carrier") String carrier,
                                                               @Param("trackingNo") String trackingNo,
                                                               @Param("orderState") String orderState,
                                                               @Param("fulfillment") String fulfillment);

}
