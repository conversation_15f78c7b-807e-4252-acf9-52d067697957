package com.zsmall.order.entity.mapper;

import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.order.entity.domain.OrderItemPrice;
import com.zsmall.order.entity.domain.vo.OrderItemPriceVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 子订单价格Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
public interface OrderItemPriceMapper extends BaseMapperPlus<OrderItemPrice, OrderItemPriceVo> {

    /**
     * 根据订单明细id修改删除标识
     *
     * @param orderItemIdList
     * @param delFlag
     */
    void updateDelFlagByOrderItemIdList(@Param("orderItemIdList") List<Long> orderItemIdList, @Param("delFlag") Integer delFlag);

}
