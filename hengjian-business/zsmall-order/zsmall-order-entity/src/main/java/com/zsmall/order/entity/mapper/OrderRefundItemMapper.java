package com.zsmall.order.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.order.entity.domain.OrderRefundItem;
import com.zsmall.order.entity.domain.vo.OrderRefundItemVo;
import org.springframework.data.repository.query.Param;

/**
 * 售后申请子单Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-08
 */
public interface OrderRefundItemMapper extends BaseMapperPlus<OrderRefundItem, OrderRefundItemVo> {

    @InterceptorIgnore(tenantLine = "true")
    boolean existsOrderRefundItemNo(@Param("orderRefundItemNo") String orderRefundItemNo);

    Integer countByInProgress(@Param("orderItemNo") String orderItemNo);

    Integer getOrderTotalRefundNum(@Param("orderId") Long orderId);

    Integer getOrderItemTotalRefundNum(@Param("orderItemId") Long orderItemId);

    OrderRefundItem getByOrderRefundItemNoAndState(@Param("orderItemNo") String orderItemNo, @Param("state") String state);

}
