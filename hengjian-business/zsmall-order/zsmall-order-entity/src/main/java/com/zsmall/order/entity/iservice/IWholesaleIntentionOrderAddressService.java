package com.zsmall.order.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.order.entity.domain.WholesaleIntentionOrderAddress;
import com.zsmall.order.entity.mapper.WholesaleIntentionOrderAddressMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【wholesale_intention_order_address(国外现货批发意向订单地址信息表)】的数据库操作Service实现
 * @createDate 2023-07-24 10:46:40
 */
@Service
public class IWholesaleIntentionOrderAddressService extends ServiceImpl<WholesaleIntentionOrderAddressMapper, WholesaleIntentionOrderAddress> {


    @InMethodLog(value = "根据主订单id查询子批发意向订单地址")
    public WholesaleIntentionOrderAddress queryWIOrderId(Long wiOrderId) {
            return lambdaQuery().eq(WholesaleIntentionOrderAddress::getWholesaleIntentionOrderId, wiOrderId).one();
    }


}




