package com.zsmall.order.entity.domain.event;

import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 设置主订单履约状态事件
 *
 * <AUTHOR>
 * @date 2023/7/10
 */
@Getter
@Setter
public class SetOrderFulfillmentProgressEvent {

    private Set<Long> orderIdList = new HashSet<>();

    public SetOrderFulfillmentProgressEvent(List<Long> orderIdList) {
        this.orderIdList.addAll(orderIdList);
    }

    public SetOrderFulfillmentProgressEvent(Long orderId) {
        this.orderIdList.add(orderId);
    }
}
