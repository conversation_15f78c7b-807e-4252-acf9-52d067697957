package com.zsmall.order.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.order.LogisticsProgress;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.order.OrderExceptionEnum;
import com.zsmall.common.enums.order.OrderStateType;
import com.zsmall.common.enums.orderItem.ShippingOrderStateEnum;
import com.zsmall.common.enums.product.StockManagerEnum;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.bo.OrderItemBo;
import com.zsmall.order.entity.domain.vo.OrderItemVo;
import com.zsmall.order.entity.mapper.OrderItemMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 子订单Service接口
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class IOrderItemService extends ServiceImpl<OrderItemMapper, OrderItem> {

    private final OrderItemMapper baseMapper;



    /**
     * 查询子订单
     */
    public OrderItemVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    @InMethodLog("根据ID查询子订单（无视租户）")
    public OrderItem queryByIdNotTenant(Long id) {
        return TenantHelper.ignore(() -> baseMapper.selectById(id));
    }

    public Boolean updateBatchByIdNoTenant(List<OrderItem> orderItemList) {

        return TenantHelper.ignore(() -> baseMapper.updateBatchById(orderItemList));
    }

    /**
     * 查询子订单列表
     */
    public TableDataInfo<OrderItemVo> queryPageList(OrderItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OrderItem> lqw = buildQueryWrapper(bo);
        Page<OrderItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询子订单列表
     */
    public List<OrderItemVo> queryList(OrderItemBo bo) {
        LambdaQueryWrapper<OrderItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OrderItem> buildQueryWrapper(OrderItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrderItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getSupplierTenantId()), OrderItem::getSupplierTenantId, bo.getSupplierTenantId());
        lqw.eq(bo.getOrderId() != null, OrderItem::getOrderId, bo.getOrderId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderItemNo()), OrderItem::getOrderItemNo, bo.getOrderItemNo());
        lqw.eq(StringUtils.isNotBlank(bo.getChannelType()), OrderItem::getChannelType, bo.getChannelType());
        lqw.eq(bo.getChannelId() != null, OrderItem::getChannelId, bo.getChannelId());
        lqw.eq(StringUtils.isNotBlank(bo.getChannelItemNo()), OrderItem::getChannelItemNo, bo.getChannelItemNo());
        lqw.eq(StringUtils.isNotBlank(bo.getStockManager()), OrderItem::getStockManager, bo.getStockManager());
        lqw.eq(StringUtils.isNotBlank(bo.getLogisticsType()), OrderItem::getLogisticsType, bo.getLogisticsType());
        lqw.eq(StringUtils.isNotBlank(bo.getShippingOrderState()), OrderItem::getShippingOrderState, bo.getShippingOrderState());
        lqw.eq(StringUtils.isNotBlank(bo.getFulfillmentProgress()), OrderItem::getFulfillmentProgress, bo.getFulfillmentProgress());
        lqw.eq(bo.getDispatchedTime() != null, OrderItem::getDispatchedTime, bo.getDispatchedTime());
        lqw.eq(bo.getFulfillmentTime() != null, OrderItem::getFulfillmentTime, bo.getFulfillmentTime());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderState()), OrderItem::getOrderState, bo.getOrderState());
        lqw.eq(StringUtils.isNotBlank(bo.getProductSkuCode()), OrderItem::getProductSkuCode, bo.getProductSkuCode());
        lqw.eq(StringUtils.isNotBlank(bo.getActivityType()), OrderItem::getActivityType, bo.getActivityType());
        lqw.eq(StringUtils.isNotBlank(bo.getActivityCode()), OrderItem::getActivityCode, bo.getActivityCode());
        lqw.eq(bo.getTotalQuantity() != null, OrderItem::getTotalQuantity, bo.getTotalQuantity());
        lqw.eq(bo.getRestockQuantity() != null, OrderItem::getRestockQuantity, bo.getRestockQuantity());
        lqw.eq(bo.getSupplierIncomeEarned() != null, OrderItem::getSupplierIncomeEarned, bo.getSupplierIncomeEarned());
        lqw.eq(bo.getOriginalPayableUnitPrice() != null, OrderItem::getOriginalPayableUnitPrice, bo.getOriginalPayableUnitPrice());
        lqw.eq(bo.getOriginalPayableTotalAmount() != null, OrderItem::getOriginalPayableTotalAmount, bo.getOriginalPayableTotalAmount());
        lqw.eq(bo.getOriginalActualUnitPrice() != null, OrderItem::getOriginalActualUnitPrice, bo.getOriginalActualUnitPrice());
        lqw.eq(bo.getOriginalActualTotalAmount() != null, OrderItem::getOriginalActualTotalAmount, bo.getOriginalActualTotalAmount());
        lqw.eq(bo.getOriginalRefundExecutableAmount() != null, OrderItem::getOriginalRefundExecutableAmount, bo.getOriginalRefundExecutableAmount());
        lqw.eq(bo.getPlatformPayableUnitPrice() != null, OrderItem::getPlatformPayableUnitPrice, bo.getPlatformPayableUnitPrice());
        lqw.eq(bo.getPlatformPayableTotalAmount() != null, OrderItem::getPlatformPayableTotalAmount, bo.getPlatformPayableTotalAmount());
        lqw.eq(bo.getPlatformActualUnitPrice() != null, OrderItem::getPlatformActualUnitPrice, bo.getPlatformActualUnitPrice());
        lqw.eq(bo.getPlatformActualTotalAmount() != null, OrderItem::getPlatformActualTotalAmount, bo.getPlatformActualTotalAmount());
        lqw.eq(bo.getPlatformRefundExecutableAmount() != null, OrderItem::getPlatformRefundExecutableAmount, bo.getPlatformRefundExecutableAmount());
        lqw.eq(bo.getChannelSaleUnitPrice() != null, OrderItem::getChannelSaleUnitPrice, bo.getChannelSaleUnitPrice());
        lqw.eq(bo.getChannelSaleTotalAmount() != null, OrderItem::getChannelSaleTotalAmount, bo.getChannelSaleTotalAmount());
        return lqw;
    }

    /**
     * 新增子订单
     */
    public Boolean insertByBo(OrderItemBo bo) {
        OrderItem add = MapstructUtils.convert(bo, OrderItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    public boolean batchSaveOrUpdate(List<OrderItem> orderItemList) {
        if (LoginHelper.getTenantTypeEnum().equals(TenantType.Distributor)) {
            return this.saveOrUpdateBatch(orderItemList);
        }
        return TenantHelper.ignore(() -> this.saveOrUpdateBatch(orderItemList));
    }

    /**
     * 修改子订单
     */
    public Boolean updateByBo(OrderItemBo bo) {
        OrderItem update = MapstructUtils.convert(bo, OrderItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    public Boolean updateNoTenant(OrderItem orderItem) {
        return TenantHelper.ignore(() -> baseMapper.updateById(orderItem)) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrderItem entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除子订单
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据订单ID获取子订单集合
     * @param orderId
     * @return
     */
    public List<OrderItem> getListByOrderId(Long orderId) {
        LambdaQueryWrapper<OrderItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderItem::getOrderId, orderId);
        if (ObjectUtil.equals(LoginHelper.getTenantTypeEnum(), TenantType.Distributor)) {
            return baseMapper.selectList(lqw);
        }
        if (ObjectUtil.equals(LoginHelper.getTenantTypeEnum(), TenantType.Supplier)) {
            lqw.eq(OrderItem::getSupplierTenantId, LoginHelper.getTenantId());
        }
        return baseMapper.selectListNotTenant(lqw);
    }

    public List<OrderItem> getListByOrderIdNotTenant(Long orderId) {
        LambdaQueryWrapper<OrderItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderItem::getOrderId, orderId);
        return baseMapper.selectListNotTenant(lqw);
    }

    /**
     * 根据订单ID和库存管理方获取子订单集合
     * @param orderId
     * @return
     */
    public List<OrderItem> getListByOrderIdAndStockManager(Long orderId, StockManagerEnum stockManagerEnum) {
        LambdaQueryWrapper<OrderItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderItem::getOrderId, orderId);
        lqw.eq(OrderItem::getStockManager, stockManagerEnum);
        if (ObjectUtil.equals(LoginHelper.getTenantTypeEnum(), TenantType.Distributor)) {
            return baseMapper.selectList(lqw);
        }
        if (ObjectUtil.equals(LoginHelper.getTenantTypeEnum(), TenantType.Supplier)) {
            lqw.eq(OrderItem::getSupplierTenantId, LoginHelper.getTenantId());
        }
        return baseMapper.selectListNotTenant(lqw);
    }

    /**
     * 获取已发货、已支付的子订单
     * @param orderItemNoList
     * @return
     */
    public List<OrderItem> getOrderItemListByShipped(List<String> orderItemNoList) {
        if (ObjectUtil.equals(LoginHelper.getTenantTypeEnum(), TenantType.Distributor)) {
            return baseMapper.getOrderItemListByShipped(orderItemNoList);
        }
        return TenantHelper.ignore(() -> baseMapper.getOrderItemListByShipped(orderItemNoList));
    }

    /**
     * 查询子订单所有履约状态
     * @param orderId
     * @return
     */
    public Set<LogisticsProgress> queryFulfillmentTypesByOrderId(Long orderId) {
        LambdaQueryWrapper<OrderItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderItem::getOrderId, orderId)
            .eq(OrderItem::getOrderState, OrderStateType.Paid)
            .eq(OrderItem::getOrderId, orderId)
        ;
        List<OrderItem> orderItems = new ArrayList<>();
        if (ObjectUtil.equals(LoginHelper.getTenantTypeEnum(), TenantType.Distributor)) {
            orderItems = baseMapper.selectList(lqw);
        } else {
            orderItems = TenantHelper.ignore(() -> baseMapper.selectList(lqw));
        }
        if (CollUtil.isNotEmpty(orderItems)) {
            return orderItems.stream().map(OrderItem::getFulfillmentProgress).collect(Collectors.toSet());
        }
        return new HashSet<>();
    }

    @InMethodLog("根据子订单编号获取子订单信息")
    public OrderItem getByOrderItemNo(String orderItemNo) {
        LambdaQueryWrapper<OrderItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderItem::getOrderItemNo, orderItemNo);
        if (ObjectUtil.equals(LoginHelper.getTenantTypeEnum(), TenantType.Distributor)) {
            return baseMapper.selectOne(lqw);
        }
        return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
    }

    @InMethodLog("根据子订单编号获取子订单信息（指定供货商）")
    public OrderItem getByOrderItemNo(String orderItemNo, String supplierTenantId) {
        LambdaQueryWrapper<OrderItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderItem::getOrderItemNo, orderItemNo);
        lqw.eq(OrderItem::getSupplierTenantId, supplierTenantId);
        return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
    }

    public boolean existsOrderItemNo(String orderItemNo) {
        log.info("进入【查询临时子订单号是否重复】 orderItemNo = {}", orderItemNo);
        LambdaQueryWrapper<OrderItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderItem::getOrderItemNo, orderItemNo);
        return TenantHelper.ignore(() -> baseMapper.exists(lqw));
    }

    /**
     * 根据订单主键，将指定状态的子订单，改为目标状态
     * @param orderId
     * @param originState 指定状态
     * @param targetState 目标状态
     * @return
     */
    public Boolean changeOrderStateByOrderId(Long orderId, OrderStateType originState, OrderStateType targetState) {
        log.info("进入【根据订单主键，将指定状态的子订单，改为目标状态】 orderId = {}, originState = {}, targetState = {}", orderId, originState, targetState);
        LambdaUpdateWrapper<OrderItem> luw = Wrappers.lambdaUpdate();
        luw.set(OrderItem::getOrderState, targetState)
            .eq(OrderItem::getOrderId, orderId)
            .eq(originState != null, OrderItem::getOrderState, originState);
        return baseMapper.update(null, luw) > 0;
    }

    @InMethodLog(value = "根据订单主键，将指定状态的子订单，改为目标状态")
    public Boolean changeOrderStateByOrderId(List<Long> orderIdList, OrderStateType targetState) {
        LambdaUpdateWrapper<OrderItem> luw = Wrappers.lambdaUpdate();
        luw.set(OrderItem::getOrderState, targetState)
           .in(OrderItem::getOrderId, orderIdList);
        return baseMapper.update(null, luw) > 0;
    }

    /**
     * 获取已经支付的子订单数据集合
     * @param ordersId
     * @return
     */
    public List<OrderItem> getPaidOrderItems(Long ordersId) {
        LambdaQueryWrapper<OrderItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderItem::getOrderState, OrderStateType.Paid);
        if (ObjectUtil.equals(LoginHelper.getTenantTypeEnum(), TenantType.Distributor)) {
            return baseMapper.selectList(lqw);
        }
        return TenantHelper.ignore(() -> baseMapper.selectList(lqw));
    }

    /**
     * 更新子订单发货单状态
     * @param orderItemNo
     * @param shippingOrderState
     * @return
     */
    public Boolean updateShippingOrderState(String orderItemNo, ShippingOrderStateEnum shippingOrderState) {
        LambdaUpdateWrapper<OrderItem> luw = Wrappers.lambdaUpdate();
        luw.set(OrderItem::getShippingOrderState, shippingOrderState)
            .eq(OrderItem::getOrderItemNo, orderItemNo);
        return TenantHelper.ignore(() -> baseMapper.update(null, luw)) > 0;
    }

    /**
     * 查询子订单所有订单状态
     * @param orderId
     * @return
     */
    public Set<OrderStateType> queryOrderStatusTypes(Long orderId) {
        LambdaQueryWrapper<OrderItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderItem::getOrderId, orderId);
        List<OrderItem> orderItems = new ArrayList<>();
        if (ObjectUtil.equals(LoginHelper.getTenantTypeEnum(), TenantType.Distributor)) {
            orderItems = baseMapper.selectList(lqw);
        } else {
            orderItems = TenantHelper.ignore(() -> baseMapper.selectList(lqw));
        }
        if (CollUtil.isNotEmpty(orderItems)) {
            return orderItems.stream().map(OrderItem::getOrderState).collect(Collectors.toSet());
        }
        return new HashSet<>();
    }


    /**
     * 查询主订单下所有未发货的非第三方仓库子订单
     * @param orderNo
     * @return
     */
    public List<OrderItem> getAllUnDispatchedItems(String orderNo) {
        if (ObjectUtil.equals(LoginHelper.getTenantTypeEnum(), TenantType.Distributor)) {
            return baseMapper.getAllUnDispatchedItems(orderNo, OrderStateType.Paid.name(), LogisticsProgress.UnDispatched.name());
        }
        return TenantHelper.ignore(() -> baseMapper.getAllUnDispatchedItems(orderNo, OrderStateType.Paid.name(), LogisticsProgress.UnDispatched.name()));
    }


    @InMethodLog(value = "查询发货时间在指定区间内的所有已发货的非第三方仓库子订单")
    public List<OrderItem> getAllTimeRangeDispatchedItems(OrderStateType orderStateType, LogisticsProgress logisticsProgress, Date minDate, Date maxDate) {
        LambdaQueryWrapper<OrderItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderItem::getFulfillmentProgress, logisticsProgress)
        .eq(OrderItem::getOrderState, orderStateType)
        .isNotNull(OrderItem::getDispatchedTime)
        .le(OrderItem::getDispatchedTime, minDate)
        .gt( OrderItem::getDispatchedTime, maxDate);
        if (LoginHelper.getTenantTypeEnum() != null && LoginHelper.getTenantTypeEnum().equals(TenantType.Distributor)) {
            return baseMapper.selectList(lqw);
        }
        return TenantHelper.ignore(() -> baseMapper.selectList(lqw));
    }

    @InMethodLog(value = "查询发货时间小于指定时间的所有已发货的非第三方仓库子订单")
    public List<OrderItem> getAllUnDispatchedItems(OrderStateType orderStateType, LogisticsProgress logisticsProgress, Date maxDate) {
        LambdaQueryWrapper<OrderItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderItem::getFulfillmentProgress, logisticsProgress)
            .eq(OrderItem::getOrderState, orderStateType)
            .isNotNull(OrderItem::getDispatchedTime)
            .lt(OrderItem::getDispatchedTime, maxDate);
        if (LoginHelper.getTenantTypeEnum() != null && LoginHelper.getTenantTypeEnum().equals(TenantType.Distributor)) {
            return baseMapper.selectList(lqw);
        }
        return TenantHelper.ignore(() -> baseMapper.selectList(lqw));
    }

    @InMethodLog(value = "售出数量")
    public Integer countSoldNumber(String orderState, String supProductCode, String sku) {
        return baseMapper.countSoldNumber(orderState, supProductCode, sku);
    }

    @InMethodLog(value = "统计平台应付金额（不包括批发）")
    public BigDecimal statsPlatformPayableTotalAmount(Date startDate, Date endDate) {
        return TenantHelper.ignore(() -> baseMapper.sumPlatformPayableTotalAmount(startDate, endDate));
    }

    @InMethodLog("根据活动编号查询有效的出货数量")
    public Integer queryValidQuantityByActivity(String activityCode) {
        return baseMapper.queryValidQuantityByActivity(activityCode);
    }

    /**
     * 根据productSkuCode和订单状态查询订单信息
     *
     * @param productSkuCodeList 产品sku码列表
     * @param orderState 订单状态
     * @return 查询的订单信息列表
     */
    @InMethodLog("根据productSkuCode和订单状态查询订单信息")
    public List<OrderItem> queryOrderItemByProductSkuCodeAndOrderState(List<String> productSkuCodeList, String orderState) {
        LambdaQueryWrapper<OrderItem> lqw = Wrappers.lambdaQuery();
        lqw.in(OrderItem::getProductSkuCode, productSkuCodeList)
         .eq(OrderItem::getOrderState, orderState);
        return TenantHelper.ignore(() ->baseMapper.selectList(lqw));
    }

    public void updateOrSetNUll(List<OrderItem> orderItemUpdateList, Integer exceptionCode) {
        // 先把价格都更新进去,然后二次更新的时候用updateWrapper更新null
        if(CollUtil.isNotEmpty(orderItemUpdateList)){
            updateBatchByIdNoTenant(orderItemUpdateList);
        }
        List<Long> ids = orderItemUpdateList.stream().map(OrderItem::getId).collect(Collectors.toList());
        if(OrderExceptionEnum.final_delivery_fee_exception.getValue().equals(exceptionCode)
            ||OrderExceptionEnum.out_of_stock_exception.getValue().equals(exceptionCode)
            ||OrderExceptionEnum.measurement_anomaly.getValue().equals(exceptionCode)){
            LambdaUpdateWrapper<OrderItem> orderItemLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            orderItemLambdaUpdateWrapper
                .set(OrderItem::getSupplierIncomeEarned, null)
                .set(OrderItem::getOriginalPayableUnitPrice, null)
                .set(OrderItem::getOriginalPayableTotalAmount, null)
                .set(OrderItem::getOriginalActualUnitPrice, null)
                .set(OrderItem::getOriginalActualTotalAmount, null)
                .set(OrderItem::getOriginalRefundExecutableAmount, null)
                .set(OrderItem::getPlatformPayableUnitPrice, null)
                .set(OrderItem::getPlatformPayableTotalAmount, null)
                .set(OrderItem::getPlatformActualUnitPrice, null)
                .set(OrderItem::getPlatformActualTotalAmount, null)
                .set(OrderItem::getPlatformRefundExecutableAmount, null)
                .in(OrderItem::getId,ids);
            update(orderItemLambdaUpdateWrapper);
        }

    }


    public void updateBatchOrSetNUll(List<OrderItem> orderItemNewList, HashMap<String, Integer> codesMap) {
        ArrayList<String> orderNos = new ArrayList<>();
        ArrayList<OrderItem> updateItems = new ArrayList<>();
        ArrayList<Long> ids = new ArrayList<>();
        Boolean isNeedSetNUll = false;
        //codesMap遍历,如果Integer==5,将key的值存入orderNos
        for (Map.Entry<String, Integer> entry : codesMap.entrySet()) {
            // 考虑一点,库存异常实际也是没有尾程派送费的 后续会补充 tag lty todo 自提的库存异常是不用额外操作的 自提可能会有库存
            if (OrderExceptionEnum.final_delivery_fee_exception.getValue().equals(entry.getValue())
                ||OrderExceptionEnum.out_of_stock_exception.getValue().equals(entry.getValue())
                ||OrderExceptionEnum.measurement_anomaly.getValue().equals(entry.getValue())) {
                orderNos.add(entry.getKey());
                isNeedSetNUll = true;
            }
        }

        // orderItemNewList 遍历,如果orderNos包含orderItemNewList的orderNo,将orderItemNewList的元素存入deliveryExceptionItems,否则存入updateItems
        for (OrderItem item : orderItemNewList) {
            if (orderNos.contains(item.getOrderNo())) {
                ids.add(item.getId());
            } else {
                updateItems.add(item);
            }
        }

        // 正常更新
        if(CollUtil.isNotEmpty(orderItemNewList)){
            updateBatchByIdNoTenant(orderItemNewList);
        }
        // 对null填充项目的进行二次更新
        if(isNeedSetNUll){
            LambdaUpdateWrapper<OrderItem> orderItemLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            orderItemLambdaUpdateWrapper
                .set(OrderItem::getSupplierIncomeEarned, null)
                .set(OrderItem::getOriginalPayableUnitPrice, null)
                .set(OrderItem::getOriginalPayableTotalAmount, null)
                .set(OrderItem::getOriginalActualUnitPrice, null)
                .set(OrderItem::getOriginalActualTotalAmount, null)
                .set(OrderItem::getOriginalRefundExecutableAmount, null)
                .set(OrderItem::getPlatformPayableUnitPrice, null)
                .set(OrderItem::getPlatformPayableTotalAmount, null)
                .set(OrderItem::getPlatformActualUnitPrice, null)
                .set(OrderItem::getPlatformActualTotalAmount, null)
                .set(OrderItem::getPlatformRefundExecutableAmount, null)
                .in(OrderItem::getId,ids);
            update(orderItemLambdaUpdateWrapper);

        }

    }


    /**
     * 功能描述：按订单项目编号更新装运订单状态
     *
     * @param orderNos           订单编号
     * @param shippingOrderState 装运订单状态
     * @return {@link Boolean }
     * <AUTHOR>
     * @date 2024/11/05
     */
    public Boolean updateShippingOrderStateByOrderNos(List<String> orderNos, ShippingOrderStateEnum shippingOrderState) {
        LambdaUpdateWrapper<OrderItem> luw = Wrappers.lambdaUpdate();
        luw.set(OrderItem::getShippingOrderState, shippingOrderState)
           .in(OrderItem::getOrderNo, orderNos);
        return TenantHelper.ignore(() -> baseMapper.update(null, luw)) > 0;
    }

    /**
     * 功能描述：获取列表
     *
     * @param orderNos 订单编号
     * <AUTHOR>
     * @date 2025/03/17
     */
    public List<OrderItem> getList(List<String> orderNos) {
        LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OrderItem::getOrderNo, orderNos);
        return  TenantHelper.ignore(()->list(wrapper));
    }

    /**
     * 功能描述：获取订单号和订单项目号地图,会过滤仅保留PickUp
     *
     * @param orderNos 订单编号
     * @return {@link Map }<{@link String }, {@link OrderItem }>
     * <AUTHOR>
     * @date 2025/03/26
     */
    public Map<String, OrderItem> getOrderNoAndOrderItemNoMap(List<String> orderNos) {
        LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OrderItem::getOrderNo, orderNos);

        List<OrderItem> list = list(wrapper);

        return list.stream().collect(Collectors.toMap(OrderItem::getOrderNo, Function.identity()));
    }

    public List<OrderItem> getByOrderNos(List<String> distinctOrderNos) {
        LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OrderItem::getOrderNo, distinctOrderNos);
        return TenantHelper.ignore(()->list(wrapper));
    }
}
