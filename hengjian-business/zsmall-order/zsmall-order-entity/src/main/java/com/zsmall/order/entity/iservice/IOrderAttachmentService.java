package com.zsmall.order.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.common.enums.order.OrderAttachmentTypeEnum;
import com.zsmall.order.entity.domain.OrderAttachment;
import com.zsmall.order.entity.domain.bo.OrderAttachmentBo;
import com.zsmall.order.entity.domain.vo.OrderAttachmentVo;
import com.zsmall.order.entity.mapper.OrderAttachmentMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 主订单附件Service接口
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@RequiredArgsConstructor
@Service
public class IOrderAttachmentService extends ServiceImpl<OrderAttachmentMapper, OrderAttachment> {

    private final OrderAttachmentMapper baseMapper;

    /**
     * 查询主订单附件
     */
    public OrderAttachmentVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询主订单附件列表
     */
    public TableDataInfo<OrderAttachmentVo> queryPageList(OrderAttachmentBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OrderAttachment> lqw = buildQueryWrapper(bo);
        Page<OrderAttachmentVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询主订单附件列表
     */
    public List<OrderAttachmentVo> queryList(OrderAttachmentBo bo) {
        LambdaQueryWrapper<OrderAttachment> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OrderAttachment> buildQueryWrapper(OrderAttachmentBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrderAttachment> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getOssId() != null, OrderAttachment::getOssId, bo.getOssId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), OrderAttachment::getOrderNo, bo.getOrderNo());
        lqw.like(StringUtils.isNotBlank(bo.getAttachmentName()), OrderAttachment::getAttachmentName, bo.getAttachmentName());
        lqw.like(StringUtils.isNotBlank(bo.getAttachmentOriginalName()), OrderAttachment::getAttachmentOriginalName, bo.getAttachmentOriginalName());
        lqw.eq(StringUtils.isNotBlank(bo.getAttachmentSuffix()), OrderAttachment::getAttachmentSuffix, bo.getAttachmentSuffix());
        lqw.eq(StringUtils.isNotBlank(bo.getAttachmentSavePath()), OrderAttachment::getAttachmentSavePath, bo.getAttachmentSavePath());
        lqw.eq(StringUtils.isNotBlank(bo.getAttachmentShowUrl()), OrderAttachment::getAttachmentShowUrl, bo.getAttachmentShowUrl());
        lqw.eq(bo.getAttachmentSort() != null, OrderAttachment::getAttachmentSort, bo.getAttachmentSort());
        lqw.eq(StringUtils.isNotBlank(bo.getAttachmentType()), OrderAttachment::getAttachmentType, bo.getAttachmentType());
        return lqw;
    }

    /**
     * 新增主订单附件
     */
    public Boolean insertByBo(OrderAttachmentBo bo) {
        OrderAttachment add = MapstructUtils.convert(bo, OrderAttachment.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改主订单附件
     */
    public Boolean updateByBo(OrderAttachmentBo bo) {
        OrderAttachment update = MapstructUtils.convert(bo, OrderAttachment.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrderAttachment entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除主订单附件
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据orderNo获取主订单附件
     * @param orderNo
     * @return
     */
    public OrderAttachment getByOrderNo(String orderNo) {
        LambdaQueryWrapper<OrderAttachment> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderAttachment::getOrderNo, orderNo);
        return baseMapper.selectOne(lqw);
    }

    /**
     * 功能描述：按订单号和附件类型查询,只返回一个最新的附件
     *
     * @param orderNo        订单号
     * @param attachmentType 附件类型
     * @return {@link OrderAttachment }
     * <AUTHOR>
     * @date 2024/11/21
     */
    public OrderAttachment queryByOrderNoAndAttachmentType(String orderNo, OrderAttachmentTypeEnum attachmentType) {
        LambdaQueryWrapper<OrderAttachment> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderAttachment::getOrderNo, orderNo);
        lqw.eq(OrderAttachment::getAttachmentType, attachmentType);
        lqw.orderByDesc(OrderAttachment::getCreateTime);
        lqw.last("limit 1");
        return baseMapper.selectOne(lqw);
    }
    public List<OrderAttachment> queryListByOrderNoAndAttachmentType(String orderNo, OrderAttachmentTypeEnum attachmentType) {
        LambdaQueryWrapper<OrderAttachment> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderAttachment::getOrderNo, orderNo);
        lqw.eq(OrderAttachment::getAttachmentType, attachmentType);
        return baseMapper.selectList(lqw);
    }

    public Boolean deleteByOrderNoAndAttachmentType(String orderNo, OrderAttachmentTypeEnum attachmentType) {
        LambdaQueryWrapper<OrderAttachment> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderAttachment::getOrderNo, orderNo);
        lqw.eq(OrderAttachment::getAttachmentType, attachmentType);
        return baseMapper.delete(lqw) > 0;
    }

    public List<OrderAttachment> getByOrderNos(List<String> orderNumbers) {
        LambdaQueryWrapper<OrderAttachment> lqw = Wrappers.lambdaQuery();
        lqw.in(OrderAttachment::getOrderNo,orderNumbers);
        lqw.eq(OrderAttachment::getDelFlag,0);
        List<OrderAttachment> attachments = baseMapper.selectList(lqw);
        return attachments;
    }



    public void deleteByOrderNosAndAttachmentType(List<String> orderNos, OrderAttachmentTypeEnum label) {
        LambdaUpdateWrapper<OrderAttachment> lqw = Wrappers.lambdaUpdate();
        lqw.in(OrderAttachment::getOrderNo,orderNos);
        lqw.eq(OrderAttachment::getAttachmentType,label);
        lqw.set(OrderAttachment::getDelFlag,2);
        baseMapper.update(null,lqw);
    }
    public void deleteByOrderNosAndOssId(List<String> orderNos, Long ossId) {
//        lqw.eq(OrderAttachment::getOssId,ossId); 找不到 要用json检索
        LambdaUpdateWrapper<OrderAttachment> lqw = Wrappers.lambdaUpdate();
        lqw.in(OrderAttachment::getOrderNo,orderNos);
        lqw.eq(OrderAttachment::getOssId,ossId);
        lqw.set(OrderAttachment::getDelFlag,2);
        baseMapper.update(null,lqw);
    }
}
