package com.zsmall.order.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.common.enums.order.OrderExtraInfoTypeEnum;
import com.zsmall.order.entity.domain.OrderExtraInfo;
import com.zsmall.order.entity.domain.bo.OrderExtraInfoBo;
import com.zsmall.order.entity.domain.vo.OrderExtraInfoVo;
import com.zsmall.order.entity.mapper.OrderExtraInfoMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 主订单额外信息Service接口
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@RequiredArgsConstructor
@Service
public class IOrderExtraInfoService extends ServiceImpl<OrderExtraInfoMapper, OrderExtraInfo> {

    private final OrderExtraInfoMapper baseMapper;

    /**
     * 查询主订单额外信息
     */
    public OrderExtraInfoVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询主订单额外信息列表
     */
    public TableDataInfo<OrderExtraInfoVo> queryPageList(OrderExtraInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OrderExtraInfo> lqw = buildQueryWrapper(bo);
        Page<OrderExtraInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询主订单额外信息列表
     */
    public List<OrderExtraInfoVo> queryList(OrderExtraInfoBo bo) {
        LambdaQueryWrapper<OrderExtraInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OrderExtraInfo> buildQueryWrapper(OrderExtraInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrderExtraInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), OrderExtraInfo::getOrderNo, bo.getOrderNo());
        lqw.like(StringUtils.isNotBlank(bo.getEntityName()), OrderExtraInfo::getEntityName, bo.getEntityName());
        lqw.eq(StringUtils.isNotBlank(bo.getInfoType()), OrderExtraInfo::getInfoType, bo.getInfoType());
        return lqw;
    }

    @InMethodLog("根据订单编号和额外信息类型查询主订单额外信息")
    public OrderExtraInfo queryByOrderNoAndInfoType(String orderNo, OrderExtraInfoTypeEnum infoType) {
        LambdaQueryWrapper<OrderExtraInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderExtraInfo::getOrderNo, orderNo);
        lqw.eq(OrderExtraInfo::getInfoType, infoType);
        return baseMapper.selectOne(lqw);
    }
}
