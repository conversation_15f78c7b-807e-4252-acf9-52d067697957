package com.zsmall.order.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.order.entity.domain.OrderItemPrice;
import com.zsmall.order.entity.mapper.OrderItemPriceMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/9/29 15:41
 */
@RequiredArgsConstructor
@Service
public class IOrderItemPriceTranService extends ServiceImpl<OrderItemPriceMapper, OrderItemPrice> {
}
