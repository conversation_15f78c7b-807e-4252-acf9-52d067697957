package com.zsmall.order.entity.domain.dto;

import com.zsmall.common.annotaion.ExcelFieldAnnotation;
import com.zsmall.common.domain.dto.ExcelBaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 订单导入DTO
 *
 * <AUTHOR>
 * @date 2023/6/8
 */
@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
public class OrderImportDTO extends ExcelBaseDTO {

    @ExcelFieldAnnotation(required = true)
    private String sequence;
    @ExcelFieldAnnotation
    private String channelType;
    @ExcelFieldAnnotation
    private String channelAlias;

    @ExcelFieldAnnotation
    private String channelOrderNo;
    @ExcelFieldAnnotation(required = true)
    private String productSkuCode;
    @ExcelFieldAnnotation(required = true)
    private Integer quantity;
    @ExcelFieldAnnotation
    private String activityCode;
    @ExcelFieldAnnotation(required = true)
    private String recipientName;
    @ExcelFieldAnnotation(required = true)
    private String address1;
    @ExcelFieldAnnotation
    private String address2;
    @ExcelFieldAnnotation
    private String address3;
    @ExcelFieldAnnotation
    private String companyName;
    @ExcelFieldAnnotation(required = true)
    private String city;
    @ExcelFieldAnnotation(required = true)
    private String stateCode;
    @ExcelFieldAnnotation(required = true)
    private String zipCode;
    @ExcelFieldAnnotation(required = true)
    private String phoneNumber;
    @ExcelFieldAnnotation(required = true)
    private String countryCode;
    @ExcelFieldAnnotation(required = true)
    private String logisticsType;
    @ExcelFieldAnnotation
    private String warehouseSystemCode;
    @ExcelFieldAnnotation
    private String logisticsCarrier;
    @ExcelFieldAnnotation
    private String logisticsService;
    @ExcelFieldAnnotation
    private String logisticsTrackingNo;
    // dev_1.0.1 减去
//    @ExcelFieldAnnotation
//    private String thirdBilling;
//    @ExcelFieldAnnotation
//    private String carrierAccount;
//    @ExcelFieldAnnotation
//    private String carrierAccountZipCode;

}
