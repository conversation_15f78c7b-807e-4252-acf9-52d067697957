package com.zsmall.order.factory;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 售后规则工厂
 *
 * <AUTHOR>
 * @date 2023/2/7
 */
@Component
public class RefundRuleFactory {

    public RefundRuleService getService(String refundRuleType) {
        return map.get(refundRuleType);
    }

    private static Map<String, RefundRuleService> map = new HashMap<>();

    public static void register(String refundRuleType, RefundRuleService thirdStockService) throws Exception {
        if (refundRuleType == null || thirdStockService == null) {
            throw new Exception("售后规则工厂 - 未找到对应实例注册");
        }
        map.put(refundRuleType, thirdStockService);
    }

}
