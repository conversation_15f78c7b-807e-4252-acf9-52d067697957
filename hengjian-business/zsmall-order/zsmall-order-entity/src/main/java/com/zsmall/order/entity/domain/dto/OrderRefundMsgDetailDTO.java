package com.zsmall.order.entity.domain.dto;

import lombok.Data;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/4/15 14:34
 */
@Data
public class OrderRefundMsgDetailDTO {

    // 商品信息
    private String channelSku;              // 3.11 渠道SKU
    private String erpSku;                  // 3.12 ERP SKU
    private String spuId;                   // 3.13 SPU ID
    private String category;                // 3.14 品类=商品分类
    private Integer receivedQuantity;       // 3.16 收到货数量
    private Integer returnQuantity;         // 3.15 退货数量
    private String productName;             // 3.17 商品名称（原编号可能错误）
    private String shippingTracking;        // 3.8 发货tracking
    // 退回物流
    private String returnCarrier;           // 3.25 寄回承运商
    private String refundReason;            // 3.20 退款原因
    private String refundDescription;       // 3.21 退款描述
    private String refundNumber;            // 3.18 退款编号
    private String orderNo;             // 3.5 订单号

}
