package com.zsmall.order.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zsmall.order.entity.domain.OrderItemTrackingImportRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【order_item_tracking_import_record】的数据库操作Mapper
* @createDate 2025-03-28 14:00:33
* @Entity
*/
public interface OrderItemTrackingImportRecordMapper extends BaseMapper<OrderItemTrackingImportRecord> {
    @InterceptorIgnore(tenantLine = "true")
    boolean existsOrderItemTrackingImportNo(@Param("importRecordNo") String importRecordNo);

    List<OrderItemTrackingImportRecord> queryFailedByIds(@Param("ids")List<Long> ids);
}




