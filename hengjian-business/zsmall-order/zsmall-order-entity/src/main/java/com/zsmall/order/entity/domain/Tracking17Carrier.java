package com.zsmall.order.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.domain.SortEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 17Track承运商
 * @TableName tracking17_carrier
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tracking17_carrier")
public class Tracking17Carrier extends SortEntity {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 物流公司key
     */
    private Integer carrierKey;

    /**
     * 国家
     */
    private Integer carrierCountry;

    /**
     * 国家镜像
     */
    private String carrierCountryIso;

    /**
     * 物流公司官网
     */
    private String carrierUrl;

    /**
     * 物流公司名称
     */
    private String carrierName;

    /**
     * 物流公司名称（简中）
     */
    private String carrierNameZhCn;

    /**
     * 物流公司名称（繁中）
     */
    private String carrierNameZhHk;

    /**
     * 分组
     */
    private String carrierGroup;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

}
