package com.zsmall.order.entity.domain.openapi;

import cn.hutool.core.date.DateTime;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OpenApiOrdersDeliveryDTO {
    /**
     * 订单号
     */
    private  String orderNo;

    /**
     * 发货状态 picked:已配货,shipped:已发货
     */
    private String shippingState;

    public List<OpenApiOrderOutShipment> orderShipments;

    @Data
    public static class OpenApiOrderOutShipment{
        /**
         * Tracking号
         */
        private String trackingNo;

        /**
         * 发货方式
         */
        private String carrier;

        /**
         * 发货数量
         */
        private Integer shippedQuantity;

        /**
         * 发货时间
         */
        private DateTime shipmentDate;

        /**
         * 发货商品编码
         */
        private String productSkuCode;

    }



}
