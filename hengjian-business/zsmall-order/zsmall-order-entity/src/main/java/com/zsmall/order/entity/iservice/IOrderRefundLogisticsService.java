package com.zsmall.order.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.order.entity.domain.OrderRefundLogistics;
import com.zsmall.order.entity.domain.bo.OrderRefundLogisticsBo;
import com.zsmall.order.entity.domain.vo.OrderRefundLogisticsVo;
import com.zsmall.order.entity.mapper.OrderRefundLogisticsMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 售后退货物流Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-08
 */
@RequiredArgsConstructor
@Service
public class IOrderRefundLogisticsService extends ServiceImpl<OrderRefundLogisticsMapper, OrderRefundLogistics> {

    private final OrderRefundLogisticsMapper baseMapper;

    /**
     * 查询售后退货物流
     */
    public OrderRefundLogisticsVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询售后退货物流列表
     */
    public TableDataInfo<OrderRefundLogisticsVo> queryPageList(OrderRefundLogisticsBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OrderRefundLogistics> lqw = buildQueryWrapper(bo);
        Page<OrderRefundLogisticsVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询售后退货物流列表
     */
    public List<OrderRefundLogisticsVo> queryList(OrderRefundLogisticsBo bo) {
        LambdaQueryWrapper<OrderRefundLogistics> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OrderRefundLogistics> buildQueryWrapper(OrderRefundLogisticsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrderRefundLogistics> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getOrderRefundId() != null, OrderRefundLogistics::getOrderRefundId, bo.getOrderRefundId());
        lqw.eq(bo.getOrderRefundItemId() != null, OrderRefundLogistics::getOrderRefundItemId, bo.getOrderRefundItemId());
        lqw.eq(StringUtils.isNotBlank(bo.getLogisticsReturnType()), OrderRefundLogistics::getLogisticsReturnType, bo.getLogisticsReturnType());
        lqw.eq(StringUtils.isNotBlank(bo.getLogisticsCarrier()), OrderRefundLogistics::getLogisticsCarrier, bo.getLogisticsCarrier());
        lqw.eq(StringUtils.isNotBlank(bo.getLogisticsTrackingNo()), OrderRefundLogistics::getLogisticsTrackingNo, bo.getLogisticsTrackingNo());
        lqw.eq(StringUtils.isNotBlank(bo.getLogisticsPickupAddress()), OrderRefundLogistics::getLogisticsPickupAddress, bo.getLogisticsPickupAddress());
        return lqw;
    }

    /**
     * 新增售后退货物流
     */
    public Boolean insertByBo(OrderRefundLogisticsBo bo) {
        OrderRefundLogistics add = MapstructUtils.convert(bo, OrderRefundLogistics.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改售后退货物流
     */
    public Boolean updateByBo(OrderRefundLogisticsBo bo) {
        OrderRefundLogistics update = MapstructUtils.convert(bo, OrderRefundLogistics.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrderRefundLogistics entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除售后退货物流
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据售后子单ID获取退后物流信息
     * @param orderRefundItemId
     * @return
     */
    public OrderRefundLogistics getByOrderRefundItemId(Long orderRefundItemId) {
        LambdaQueryWrapper<OrderRefundLogistics> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderRefundLogistics::getOrderRefundItemId, orderRefundItemId);
        return baseMapper.selectOne(lqw);
    }
}
