package com.zsmall.order.entity.domain;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.orderShippingRecord.ShippingStateEnum;
import com.zsmall.common.enums.warehouse.WarehouseTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 子订单出货单对象 order_item_shipping_record
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("order_item_shipping_record")
public class OrderItemShippingRecord extends NoDeptTenantEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 供货商租户编号
     */
    private String supplierTenantId;

    /**
     * 主订单编号
     */
    private String orderNo;

    /**
     * 子订单编号
     */
    private String orderItemNo;

    /**
     * 渠道类型
     */
    private ChannelTypeEnum channelType;

    /**
     * 出货仓库类型
     */
    private WarehouseTypeEnum warehouseType;

    /**
     * 出货仓库编号
     */
    private String warehouseCode;

    /**
     * 出货仓库唯一系统编号
     */
    private String warehouseSystemCode;

    /**
     * 出货单编号
     */
    private String shippingNo;

    /**
     * 出货单状态
     */
    private ShippingStateEnum shippingState;

    /**
     * 出货单异常状态码（错误信息原文）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String shippingErrorCode;

    /**
     * 出货单异常信息
     */
    @TableField(typeHandler = JacksonTypeHandler.class, updateStrategy = FieldStrategy.IGNORED)
    private JSONObject shippingErrorMessage;

    /**
     * 销售发货单JSON字符串，用于二次创建
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONObject inCreateJson;

    /**
     * 出货单是否由系统托管轮询（0-否，1-是）
     */
    private Boolean systemManaged;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
