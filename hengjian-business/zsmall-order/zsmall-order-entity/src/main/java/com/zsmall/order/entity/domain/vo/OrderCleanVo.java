package com.zsmall.order.entity.domain.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Data;

import java.util.Date;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/23 17:27
 */
@Data
public class OrderCleanVo {
    @JsonProperty("bill_code")
    @JSONField(name = "bill_code")
    private String billCode;

    @JsonProperty("bill_date")
    @J<PERSON><PERSON>ield(name = "bill_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private Date billDate;



}
