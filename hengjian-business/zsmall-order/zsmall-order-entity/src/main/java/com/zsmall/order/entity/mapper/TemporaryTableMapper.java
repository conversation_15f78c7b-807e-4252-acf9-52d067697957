package com.zsmall.order.entity.mapper;

/**
 * @BelongsProject: hengjian-distribution
 * @BelongsPackage: com.zsmall.order.entity.mapper
 * @Author: Len
 * @CreateTime: 2024-08-16  17:10
 * @Description: TODO
 * @Version: 1.0
 */


import com.zsmall.order.entity.domain.TemporaryTable;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 临时Mapper接口
 *
 * <AUTHOR> Li
 * @date 2024-08-16
 */

@Mapper
public interface TemporaryTableMapper  {

    /**
     * 创建临时表
     */
    void createTemporaryTable(@Param("tableName") String tableName);

    /**
     * 保存数据到临时表里面
     */

    void batchInsertTemporaryTable(@Param("list") List<TemporaryTable> dOs, @Param("tableName") String tableName);

    /**
     * 删除临时表
     */
    void dropTemporaryTable(@Param("tableName") String tableName);
    /**
     * 清空临时表
     */
    void cleanTemporaryTable(@Param("tableName") String tableName);

    /**
     * 拼接sql插入
     * @param sql
     */
    @Insert("${sql}")
    void insertBySql(@Param("sql") String sql);

}
