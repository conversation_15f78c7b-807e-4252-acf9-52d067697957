package com.zsmall.order.entity.domain.event;

import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.dto.OrderItemDTO;
import lombok.Getter;
import lombok.Setter;

/**
 * 生成子订单事件
 *
 * <AUTHOR>
 * @date 2023/6/27
 */
@Getter
@Setter
public class GenerateOrderItemEvent {

    /**
     * 分销商编号
     */
    private String dTenantId;

    private Orders order;
    private String productSkuCode;
    private Integer totalQuantity;

    /**
     * 渠道类型
     */
    private ChannelTypeEnum channelTypeEnum;

    /**
     * 物流类型
     */
    private LogisticsTypeEnum logisticsType;

    /**
     * 国家，计算运费使用，除了US之外的国家，不需要计算运费
     */
    private String country;

    /**
     * 物流邮编，计算运费使用（若是美国xxxxx-xxxx格式的邮编，只取前面五位传入）
     */
    private String logisticsZipCode;

    /**
     * 第三方物流
     */
    private Boolean logisticsThirdBilling;

    /**
     * 指定仓库
     */
    private String warehouseSystemCode;

    /**
     * 活动编号
     */
    private String activityCode;

    /**
     * 回参
     */
    private OrderItemDTO outDTO;

}
