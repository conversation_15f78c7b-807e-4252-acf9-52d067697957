package com.zsmall.order.entity.iservice;

import cn.hutool.core.codec.Base62;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.hengjian.common.core.constant.AbstractCodeTypeBase;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.service.RedisCodeGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalTime;

/**
 * 仓库系统编号生成器实现类
 *
 * <AUTHOR>
 * @date 2023/5/24
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderCodeGenerator extends RedisCodeGenerator {
    final private IOrderItemTrackingImportRecordService iOrderItemTrackingImportService;
    final private IOrderImportRecordService iOrderImportRecordService;
    final private IOrderImportTempService iOrderImportTempService;
    final private IOrdersService iOrdersService;
    final private IOrderItemService iOrderItemService;
    final private IOrderRefundService iOrderRefundService;
    final private IOrderRefundItemService iOrderRefundItemService;
    final private IOrderItemShippingRecordService iOrderItemShippingRecordService;
    final private IWholesaleIntentionOrderItemService iWholesaleIntentionOrderItemService;
    final private IWholesaleIntentionOrderService iWholesaleIntentionOrderService;

    /**
     * 编号生成器 // todo 后续需要 更换成雪花算法 1s 10条就容易出现单号重复
     *
     * @param type 主类型
     * @return
     */
    @Override
    public String codeGenerate(AbstractCodeTypeBase type) throws RStatusCodeException {
        log.info("编号生成器 type = {}", type);
        var code = "";
        var value = type.getValue();
        var repeat = true;
        while (repeat) {
            if (BusinessCodeEnum.OrderImportRecordNo.equals(type)) {
                code = value + super.getDateTimeNumber2Year() + RandomUtil.randomNumbers(3);
                repeat = iOrderImportRecordService.existImportRecordNo(code);
            } else if (BusinessCodeEnum.TempOrderNo.equals(type)) {
                Snowflake snowflake = IdUtil.getSnowflake(1, 1);
                long id = snowflake.nextId();
                String base62Id = Base62.encode(String.valueOf(id));
                // 生成唯一ID并转换为字符串（长整型）
                code = value + base62Id;
                repeat = iOrderImportTempService.existsTempOrderNo(code);
            } else if (BusinessCodeEnum.OrderNo.equals(type)) {
                LocalTime nowTime = LocalTime.now();
                long digitIncrease = super.getSecondOneDigitIncrease(nowTime, value);
                code = value + super.getDateTimeNumber2Year() + digitIncrease + RandomUtil.randomNumbers(2);
                repeat = iOrdersService.existsOrderNo(code);
                if(!repeat) {
                    repeat = super.checkMinuteDuplicate(nowTime, value, code);
                }
            } else if (BusinessCodeEnum.OrderItemNo.equals(type)) {
                LocalTime nowTime = LocalTime.now();
                long digitIncrease = super.getSecondOneDigitIncrease(nowTime, value);
                code = value + super.getDateTimeNumber2Year() + digitIncrease + RandomUtil.randomNumbers(2);
                repeat = iOrderItemService.existsOrderItemNo(code);
                if(!repeat) {
                    repeat = super.checkSecondDuplicate(nowTime, value, code);
                }
            } else if (BusinessCodeEnum.OrderRefundNo.equals(type)) {
                code = value + RandomUtil.randomNumbers(10);
                repeat = iOrderRefundService.existsOrderRefundNo(code);
            } else if (BusinessCodeEnum.OrderRefundItemNo.equals(type)) {
                code = value + RandomUtil.randomNumbers(10);
                repeat = iOrderRefundItemService.existsOrderRefundItemNo(code);
            } else if (BusinessCodeEnum.ShippingRecordNo.equals(type)) {
                code = value + RandomUtil.randomNumbers(12);
                repeat = iOrderItemShippingRecordService.existsShippingNo(code);
            } else if (BusinessCodeEnum.WholesaleIntentionItem.equals(type)) {
                code = value + RandomUtil.randomNumbers(12);
                repeat = iWholesaleIntentionOrderItemService.existsWholesaleIntentionOrderItemNo(code);
            }  else if (BusinessCodeEnum.WholesaleIntention.equals(type)) {
                code = value + RandomUtil.randomNumbers(12);
                repeat = iWholesaleIntentionOrderService.existsWholesaleIntentionOrderNo(code);
            } else if (BusinessCodeEnum.OrderItemTrackingRecordNo.equals(type)) {
                code = value + RandomUtil.randomNumbers(12);
                repeat = iOrderItemTrackingImportService.existsOrderItemTrackingImportNo(code);
            }else {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.BUSINESS_CODE_GENERATE_ERROR);
            }
        }
        if (StrUtil.isNotBlank(code)) {
            return code;
        } else {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.BUSINESS_CODE_GENERATE_ERROR);
        }
    }

    /**
     * 编号生成器
     *
     * @param type    主类型
     * @param subType 子类型
     * @return
     */
    @Override
    public String codeGenerate(AbstractCodeTypeBase type, String subType) throws RStatusCodeException {
        return codeGenerate(type);
    }
}
