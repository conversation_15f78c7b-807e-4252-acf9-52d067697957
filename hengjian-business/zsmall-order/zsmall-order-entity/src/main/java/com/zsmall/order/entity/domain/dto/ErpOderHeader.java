package com.zsmall.order.entity.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.zsmall.order.entity.domain.OrderItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2023/12/12 15:52
 */
@Setter
@Getter
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.USE_DEFAULTS)
public class ErpOderHeader implements Serializable {
    //渠道标识
    private String account_id;
    //订单号
    private String ordernum;
    //
    private String display_ordernum;

    private String display_seller;

    private String shipping_name;

    private String addressline1;

    private String addressline2;

    private String addressline3;

    private String district;

    private String city;

    private String state_or_region;

    private String postal_code;

    private String country_code;

    private String phone;

    private String from_phone;//梦百合

    private String display_order_comment;

    private String ship_service_level;

    private String shipping_method_code;

    private String channel_shipment_method_code;

    private String fulfillment_policy;

    private String notification_email_list;

    private String carrier_group;

    private String charge_account_no;

    private String packing_url;

    private String remark;

    private String po_number;

    private String shipping_price;

    private String cod_data;

    private Integer orderType;

    private Integer order_flag;

    private String fulfillment_channel;

    private String is_prime;

    private Integer created_by;

    private String shipping_status;

    private String carrier_addition;

    private String address_type;

    private String amount; // 订单金额 － 必填
    private String currency_code; // 订单币种 － 必填
    private String status; // 订单状态 － 必填
    private String buy_email; // 顾客邮箱 － 必填
    private String buy_name; // 顾客名称
    private String buy_id; // 客户渠道ID
    private String display_order_date; // 发货单显示订单时间
    private String received_date; // 预计收货日期
    private String country; // 收货国家 － 必填
    private String payment_date; // 订单付款时间 － 必填
    private String shipNode;//walmartDSV的仓库代码
    private String storeId;//walmartDSV的独有
    private String updated;
    private String carrier;
    private String is_business_order;
    private String sales_channel;
    private String amount_discount;

    //uspo
    private String latest_ship_date;     // 发货窗口期
    private String earliest_ship_date;   // 发货窗口期
    private String customer_refer_no;    //（供应商selling_party_id）
    private Integer purchase_order_state;//（订单状态 1.Closed  2.Acknowledged   3.New）
    private String ship_to_party;        //（供应商仓库的PartyID）
    private String created;              // 订单时间

    //AmazonVendor
    private String deal_no;        //对应REF--REF02, PD
    private String fob_shipment_payment_method;    //对应FOB--FOB01, 发运付款方式
    private String fob_transport_term;    //对应FOB--FOB05, 运输方式
    private String fob_description;    //对应FOB--FOB07, 备注信息
    private String is_back_order;    //对应CSH--CSH01, 是否允许back order
    private String package_description;    //对应PKG--PKG05, 包装体积
    private String address_code;    //对应N1--N104, 地址码
    private String warehouse_code;
    private String subinventory_code;//发货仓库
    //补发单会有
    private String org_warehouse_id;//发货仓库id
    //ack专用字段
    //系统仓库的邮编
    private String warehouse_zip_code;
    //渠道仓库代码
    private String supplier_id;
    private String ship_to_name;
    private String ship_to_address1;
    private String ship_to_city;
    private String ship_to_state;
    private String ship_to_zip;
    private String ship_to_country;

    private List<ErpOrderItemDTO> items;
}
