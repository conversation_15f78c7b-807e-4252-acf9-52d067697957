package com.zsmall.order.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.order.entity.domain.OrderRefundRule;
import com.zsmall.order.entity.domain.bo.OrderRefundRuleBo;
import com.zsmall.order.entity.domain.vo.OrderRefundRuleVo;
import com.zsmall.order.entity.mapper.OrderRefundRuleMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 售后规则Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-06-08
 */
@RequiredArgsConstructor
@Service
public class IOrderRefundRuleService {

    private final OrderRefundRuleMapper baseMapper;
    private final BusinessParameterService businessParameterService;

    /**
     * 查询售后规则
     */
    public OrderRefundRuleVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询售后规则列表
     */
    public TableDataInfo<OrderRefundRuleVo> queryPageList(OrderRefundRuleBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OrderRefundRule> lqw = buildQueryWrapper(bo);
        Page<OrderRefundRuleVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询售后规则列表
     */
    public List<OrderRefundRuleVo> queryList(OrderRefundRuleBo bo) {
        LambdaQueryWrapper<OrderRefundRule> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OrderRefundRule> buildQueryWrapper(OrderRefundRuleBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrderRefundRule> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getRefundRuleNo()), OrderRefundRule::getRefundRuleNo, bo.getRefundRuleNo());
        lqw.eq(StringUtils.isNotBlank(bo.getApplicableFulfillment()), OrderRefundRule::getApplicableFulfillment, bo.getApplicableFulfillment());
        lqw.eq(StringUtils.isNotBlank(bo.getRefundReasonZhCn()), OrderRefundRule::getRefundReasonZhCn, bo.getRefundReasonZhCn());
        lqw.eq(StringUtils.isNotBlank(bo.getRefundReasonEnUs()), OrderRefundRule::getRefundReasonEnUs, bo.getRefundReasonEnUs());
        lqw.eq(bo.getWhetherReviewMd() != null, OrderRefundRule::getWhetherReviewMd, bo.getWhetherReviewMd());
        lqw.eq(bo.getWhetherSupportModifyAmount() != null, OrderRefundRule::getWhetherSupportModifyAmount, bo.getWhetherSupportModifyAmount());
        lqw.eq(bo.getWhetherProvidePictures() != null, OrderRefundRule::getWhetherProvidePictures, bo.getWhetherProvidePictures());
        lqw.eq(bo.getWhetherRestoreInventory() != null, OrderRefundRule::getWhetherRestoreInventory, bo.getWhetherRestoreInventory());
        lqw.eq(bo.getSort() != null, OrderRefundRule::getSort, bo.getSort());
        return lqw;
    }

    /**
     * 新增售后规则
     */
    public Boolean insertByBo(OrderRefundRuleBo bo) {
        OrderRefundRule add = MapstructUtils.convert(bo, OrderRefundRule.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改售后规则
     */
    public Boolean updateByBo(OrderRefundRuleBo bo) {
        OrderRefundRule update = MapstructUtils.convert(bo, OrderRefundRule.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrderRefundRule entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除售后规则
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据规则编号查询
     * @param refundRuleNo
     * @return
     */
    public OrderRefundRule queryByRefundRuleNo(String refundRuleNo, String applicableFulfillment) {
        LambdaQueryWrapper<OrderRefundRule> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderRefundRule::getRefundRuleNo, refundRuleNo)
            .eq(OrderRefundRule::getApplicableFulfillment, applicableFulfillment);
        return baseMapper.selectOne(lqw);
    }


    @InMethodLog(value = "查询适用履约状态")
    public List<String> queryApplicableFulfillment() {
        return baseMapper.queryApplicableFulfillment();
    }


    @InMethodLog(value = "根据适用履约状态查询")
    public List<OrderRefundRule> queryByApplicableFulfillment(String applicableFulfillment) {
        LambdaQueryWrapper<OrderRefundRule> lqw = Wrappers.lambdaQuery();
         lqw.eq(OrderRefundRule::getApplicableFulfillment, applicableFulfillment)
            .orderByDesc(OrderRefundRule::getSort);
        return baseMapper.selectList(lqw);
    }

}
