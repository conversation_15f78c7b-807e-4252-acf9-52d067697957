package com.zsmall.order.entity.domain.event;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/7/6 19:04
 */
@Getter
@Setter
public class CountSoldNumberEvent {

    private String orderState;
    private String productCode;
    private String sku;
    private Integer sold;

    public CountSoldNumberEvent(String orderState, String productCode, String sku) {
        this.orderState = orderState;
        this.productCode = productCode;
        this.sku = sku;
    }
}
