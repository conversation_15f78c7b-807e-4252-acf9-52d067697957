package com.zsmall.order.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zsmall.order.entity.domain.WholesaleIntentionOrder;
import com.zsmall.order.entity.domain.dto.WIOrderPageDTO;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【wholesale_intention_order(国外现货批发意向订单)】的数据库操作Mapper
* @createDate 2023-07-24 10:46:40
* @Entity com.zsmall.order.entity.domain.WholesaleIntentionOrder
*/
public interface WholesaleIntentionOrderMapper extends BaseMapper<WholesaleIntentionOrder> {

    Page<WholesaleIntentionOrder> queryPage(@Param("queryDTO") WIOrderPageDTO wiOrderPageDTO, Page<WholesaleIntentionOrder> page);

    @InterceptorIgnore(tenantLine = "true")
    boolean existsWholesaleIntentionOrderNo(@Param("wholesaleIntentionOrderNo") String wholesaleIntentionOrderNo);

}




