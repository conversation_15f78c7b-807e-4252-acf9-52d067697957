package com.zsmall.order.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.order.entity.domain.AirwallexRequestInfo;
import com.zsmall.order.entity.domain.AirwallexRequestLog;
import com.zsmall.order.entity.domain.OrderAddressInfo;
import com.zsmall.order.entity.mapper.AirwallexRequestInfoMapper;
import com.zsmall.order.entity.mapper.OrderAddressInfoMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024年3月7日  18:54
 * @description:
 */
@RequiredArgsConstructor
@Service
public class IAirwallexRequestInfoService extends ServiceImpl<AirwallexRequestInfoMapper, AirwallexRequestInfo> {

    private final AirwallexRequestInfoMapper baseMapper;


    public AirwallexRequestInfo getByRequestId(String requestId){
        return baseMapper.getByRequestId(requestId);
    }

    public void addAirwallexRequestLog(AirwallexRequestLog airwallexRequestLog){
        baseMapper.addAirwallexRequestLog(airwallexRequestLog);
    }

    public AirwallexRequestInfo getByCommitId(String commitId){
        return baseMapper.getByCommitId(commitId);
    }
}
