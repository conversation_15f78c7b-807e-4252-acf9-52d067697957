package com.zsmall.order.entity.domain.dto;

import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.OrderItemPrice;
import com.zsmall.order.entity.domain.OrderItemProductSku;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 子订单生成DTO
 *
 * <AUTHOR>
 * @date 2023/6/10
 */
@Getter
@AllArgsConstructor
public class OrderItemDTO {

    /**
     * 子订单
     */
    private OrderItem orderItem;

    /**
     * 子订单价格
     */
    private OrderItemPrice orderItemPrice;

    /**
     * 子订单商品信息
     */
    private OrderItemProductSku orderItemProductSku;

    /**
     * 国际化信息
     */
    private LocaleMessage localeMessage;

}
