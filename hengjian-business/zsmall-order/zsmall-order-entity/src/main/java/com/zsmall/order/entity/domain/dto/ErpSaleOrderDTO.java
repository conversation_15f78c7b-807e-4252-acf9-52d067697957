package com.zsmall.order.entity.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.zsmall.common.enums.BooleanEnum;
import com.zsmall.common.enums.order.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2023/12/12 16:52
 */
@Getter
@Setter
@Accessors(chain = true)
@Access(AccessType.FIELD)
@JsonInclude(JsonInclude.Include.USE_DEFAULTS)
public class ErpSaleOrderDTO {

    private ErpSaleOrderDetailDTO saleOrderDetails;
    private List<ErpSaleOrderAddressDTO> saleOrderAddressList;
    private List<ErpSaleOrderItemDTO> saleOrderItemsList;
    /**
     * 版本号
     */
    private Integer createdBy = 0;
    @Column(name = "ver_no")
    private Integer verNo = 9;
    /**
     * 组织ID
     */
    @Column(name = "org_id")
    private Integer orgId;
    /**
     * 仓库ID
     */
    @Column(name = "org_warehouse_id")
    private Integer orgWarehouseId;
    /**
     * 仓库code
     */
    @Column(name = "org_warehouse_code")
    private String orgWarehouseCode;

    /**
     * 渠道仓库code
     */

    @Column(name = "channel_warehouse_code")
    private String channelWarehouseCode;
    /**
     * 所属父ID号默认0
     */

    @Column(name = "parent_id")
    private Integer parentId;
    /**
     * 项目ID
     */
    @Column(name = "line_id")
    private Integer lineId;
    /**
     * 渠道名称
     */

    @Column(name = "channel")
    private String channel;
    /**
     * 渠道ID
     */

    @Column(name = "channel_id")
    private Integer channelId;
    /**
     * 渠道标识
     */

    @Column(name = "channel_flag")
    private String channelFlag;
    /**
     * 渠道订单号
     */
    @Column(name = "order_no")
    private String orderNo;
    /**
     * 采购单号
     */

    @Column(name = "po_number")
    private String poNumber;
    /**
     * 渠道订单状态  0 pending 1 unshipped 2 shipped 3 canceled 4 refund
     */

    @Column(name = "channel_status")
    private B2cChannelStatusEnum channelStatus;
    /**
     * 订单状态  0未支付 1未发货 2已审核 3已分配 4已配货 5已发货 6已取消 7已退款
     */
    @Column(name = "order_status")
    private OrderStatusEnum orderStatus;
    /**
     * 订单发货状态 0:未发货;1:未发货;2:配货中 3:已配货 ; 4:已发货 ; 5:已完成;6:已取消 7:已退款
     */

    /**
     * 订单类型  0正常销售 1展示订单 2分销订单 3刷评订单
     */

    @Column(name = "order_type")
    private B2cOrderTypeEnum orderType;
    /**
     * 异常编码  0无异常的订单 100系统异常 1000商品无法关联 2000订单地址异常 3000订单更新异常 4000订单配货异常 5000订单取消异常 6000Tracking异常 7000订单成本异常 4500订单发运方式异常 4600订单附件上传异常
     */

    @Column(name = "error_code")
    private B2cOrderErrorEnum errorCode;



    @Column(name = "detail_error_code")
    private String detailErrorCode;


    /**
     * 订单发货类型 0 MFN 1 AFN
     */

    @Column(name = "fulfillment_channel")
    private FulfillmentChannelEnum fulfillmentChannel;
    /**
     * 产品总数量
     */

    @Column(name = "order_quantity")
    private Integer orderQuantity;
    /**
     * 已发货产品数量
     */

    @Column(name = "shiped_quantity")
    private Integer shipedQuantity;
    /**
     * 承运商发货方式
     */
    @Column(name = "shipping_method_code")
    private String shippingMethodCode;
    /**
     * 承运商账号分组
     */
    @Column(name = "carrier_account_group_id")
    private Integer carrierAccountGroupId;
    /**
     * 云仓回传发货方式
     */

    @Column(name = "warehouse_return_method_code")
    private String warehouseReturnMethodCode;
    /**
     * 发货等级
     */

    @Column(name = "ship_service_level")
    private String shipServiceLevel;
    /**
     * 发货成本
     */

    @Column(name = "shipping_cost")
    private BigDecimal shippingCost;
    /**
     * 收到的运费
     */

    @Column(name = "shipping_price")
    private BigDecimal shippingPrice;
    /**
     * 预估物流费费
     */

    @Column(name = "shipping_fee")
    private BigDecimal shippingfee;

    /**
     * 总重量
     */

    @Column(name = "weight")
    private BigDecimal weight;
    /**
     * 收件人
     */

    @Column(name = "shipping_name")
    private String shippingName;
    /**
     * 币种
     */
    @Column(name = "currency_code")
    private String currencyCode;
    /**
     * 金额
     */

    @Column(name = "amount")
    private BigDecimal amount;
    /**
     * 美金金额
     */

    @Column(name = "us_amount")
    private BigDecimal usAmount;
    /**
     * 是否是红人订单 0否 1是
     */

    @Column(name = "is_celebrity")
    private BooleanEnum isCelebrity;
    /**
     * 是否是样品订单 0否 1是
     */

    @Column(name = "is_sample")
    private BooleanEnum isSample;
    /**
     * 是否是补发订单 0否 1是
     */

    @Column(name = "is_reshipment")
    private BooleanEnum isReshipment;
    /**
     * 0 是否是附加订单 0否 1是
     */

    @Column(name = "is_addition")
    private BooleanEnum isAddition;
    /**
     * 是否是拆解订单 0否 1是
     */

    @Column(name = "is_dismantlement")
    private BooleanEnum isDismantlement;
    /**
     * 是否激活,0没有激活;1已经激活
     */

    @Column(name = "is_active")
    private BooleanEnum isActive;
    /**
     * 是否为prime的订单,0否;1是
     */

    @Column(name = "is_prime")
    private BooleanEnum isPrime;
    /**
     * 是否商业订单,0否;1是
     */

    @Column(name = "is_business_order")
    private BooleanEnum isBusinessOrder;

    /**
     * 是否需要人工审核 0否，1是
     */

    @Column(name = "is_artificial")
    private BooleanEnum isArtificial;
    /**
     * 是否投保 0代表不投保;1代表投保
     */

    @Column(name = "is_insurance")
    private BooleanEnum isInsurance;
    /**
     *是否是onsite订单 0否 1是
     */

    @Column(name = "is_onsite")
    private BooleanEnum isOnsite;
    /**
     * 0 正常发货;1 walmart dsv平台s2s的订单,需要等包裹标签才能发货
     */

    @Column(name = "shipment_type")
    private Integer shipmentType;
    /**
     *结算状态
     */

    @Column(name = "settlement_status")
    private BooleanEnum settlementStatus;
    /**
     * 记录推送云仓状态, 0未推送
     */

    @Column(name = "push_fbb_flag")
    private Integer pushFbbFlag;
    /**
     * 发货时间
     */

    @Column(name = "shipment_date")
    private LocalDateTime shipmentDate;
    /**
     * 最晚发货时间
     */

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'+00:00'",timezone = "UTC")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @Column(name = "latest_ship_date")
    private LocalDateTime latestShipDate;
    /**
     * 是否允许回写父订单 0 字段不起作用 1回写 2不回写
     */

    @Column(name = "is_allow_tracking")
    private AllowTrackingEnum isAllowTracking;
    /**
     * 备注
     */

    @Column(name = "remark")
    private String remark;
    /**
     * 订单流程类型 待定义
     */

    @Column(name = "process_type")
    private Integer processType;
    /**
     * 客户ID
     */

    @Column(name = "customer_id")
    private Integer customerId;
    /**
     * 销售员ID
     */
    @Column(name = "salesperson_id")
    private Integer salespersonId;
    /**
     * 账期ID
     */

    @Column(name = "payment_term_id")
    private Integer paymentTermId;
    /**
     *收发票人
     */

    @Column(name = "bill_to")
    private Integer billTo;
    /**
     * 收货人
     */

    @Column(name = "ship_to")
    private Integer shipTo;
    /**
     * 0表示b2c 1表示b2b
     */

    @Column(name = "order_class")
    private OrderClassEnum orderClass;

    /**
     * 仓库订单号，（b2b用）
     *
     */

    @Column(name = "warehouse_ordernum")
    private String warehouseOrdernum;
    /**
     * b2b的订单类型 0:未识别 1:普通单 2:清仓单 3:样品单
     */

    @Column(name = "manual_order_type")
    private ManualOrderTypeEnum manualOrderType;

    /**
     * b2b 佣金比率
     */

    @Column(name = "commission_rate")
    private BigDecimal commissionRate;
    /**
     * 付款方式
     */
    @Column(name = "payment_method")
    private String paymentMethod;
    /**
     * 付款状态
     */

    @Column(name = "payment_status")
    private Integer paymentStatus;

    /**
     * 审核时间
     */

    @Column(name = "check_date")
    private LocalDateTime checkDate;

    /**
     * 供应商selling_party_id
     */

    @Column(name = "customer_refer_no")
    private String customerReferNo;

    /**
     * 供应商仓库的PartyID
     */

    @Column(name = "ship_to_party")
    private String shipToParty;

    /**
     * 1.Closed  2.Acknowledged   3.New
     */

    @Column(name = "purchase_order_state")
    private Integer purchaseOrderState;

    /**
     * 分配时间
     */

    @Column(name = "distribute_date")
    private LocalDateTime distributeDate;
    /**
     * 渠道创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'+00:00'",timezone = "UTC")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @Column(name = "channel_created")
    private LocalDateTime channelCreated;
    /**
     * 渠道创建时间(pst)
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'+00:00'",timezone = "UTC")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @Column(name = "pst_channel_created")
    private LocalDateTime pstChannelCreated;
    /**
     * 渠道更新时间
     */

    @Column(name = "channel_updated")
    private LocalDateTime channelUpdated;

    /**
     * 禁用时间戳
     */

    @Column(name = "disabled_at")
    @Accessors(chain = false)
    private Integer disabledAt;
    /**
     * 禁用者id
     */

    @Column(name = "disabled_by")
    @Accessors(chain = false)
    private Integer disabledBy;
    /**
     * 禁用者名称
     */

    @Column(name = "disabled_name")
    @Accessors(chain = false)
    private String disabledName;










    @Transient
    private B2cOrderSignEnum b2cOrderSign;

    @Transient
    private String countryCode;

    @Transient
    private Integer logisticStatus;

    @Transient
    private LocalDateTime warehouseExpectedShipDate;


    @Transient
    private List<Long> labelIds;

    @Transient
    private Boolean accountPay;

    @Transient
    private Integer zone;



    public void setChannelCreated(LocalDateTime channelCreated){
        this.channelCreated = channelCreated;
        try {
            // 转PST时区 获取pst时间对应的日期
            ZonedDateTime zoneDateTime = channelCreated.atZone(ZoneId.systemDefault());
            LocalDateTime pstTime = zoneDateTime.withZoneSameInstant(ZoneId.of("America/Los_Angeles")).toLocalDateTime();
            this.pstChannelCreated = pstTime;
        }catch (Exception e){
            e.printStackTrace();
        }
    }
    public List<ErpSaleOrderItemDTO> pushSaleOrderItemsList(List<ErpSaleOrderItemDTO> saleOrderItemsList){
        this.saleOrderItemsList = saleOrderItemsList;
        return this.saleOrderItemsList;
    }
}
