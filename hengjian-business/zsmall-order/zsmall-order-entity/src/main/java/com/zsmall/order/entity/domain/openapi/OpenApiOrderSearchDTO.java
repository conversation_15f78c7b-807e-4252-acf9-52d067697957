package com.zsmall.order.entity.domain.openapi;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OpenApiOrderSearchDTO {
    /**
     * 订单号集合
     */
    private List<String> orderExtendIdList;
    /**
     * 渠道订单号集合
     */
    private List<String> channelOrderNoList;

    /**
     * 供货商租户id
     */
    private String supplierTenantId;
    /**
     * 分销商租户ID
     */
    private String distributorTenantId;
    /**
     *  订单开始时间
     */
    private Date createTimeStart;
    /**
     *  订单结束时间
     */
    private Date createTimeEnd;
    /**
     * 页码
     */
    private Integer current;
    /**
     * 每页条数
     */
    private Integer size;

    /**
     * 数据库偏移量，用于分页查询
     */
    private Integer offset;
}
