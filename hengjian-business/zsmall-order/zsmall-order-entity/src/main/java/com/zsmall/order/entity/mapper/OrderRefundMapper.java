package com.zsmall.order.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.order.entity.domain.OrderRefund;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.bo.RefundApplyBo;
import com.zsmall.order.entity.domain.vo.OrderRefundVo;
import com.zsmall.order.entity.domain.vo.RefundApplyVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 售后申请主单Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-08
 */
public interface OrderRefundMapper extends BaseMapperPlus<OrderRefund, OrderRefundVo> {

    Page<RefundApplyVo> queryPageList(@Param("dto") RefundApplyBo dto, Page<OrderRefund> page);

    List<RefundApplyVo> queryPageList(@Param("dto") RefundApplyBo dto);

    Integer countByInProgress(@Param("orderId") Long orderId);

    @InterceptorIgnore(tenantLine = "true")
    boolean existsOrderRefundNo(@Param("orderRefundNo") String orderRefundNo);

    OrderRefundVo sumPlatformRefundAmountForTenant();

    @InterceptorIgnore(tenantLine = "true")
    List<OrderRefund> queryByOrderItemAndState(@Param("orderItemId") Long orderItemId, @Param("refundState") String refundState);

    BigDecimal sumByRefundPrice(@Param("orderId") Long orderId, @Param("isSupplier") Boolean isSupplier);


    /**
     * 统计平台退款金额
     * @param startDate
     * @param endDate
     * @return
     */
    BigDecimal sumPlatformRefundAmount(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    List<OrderRefund> queryOrderRefundByRefunded(@Param("orderId") Long orderId, @Param("excludeRefundId") Long excludeRefundId, @Param("refundState") String refundState);

    /**
     * 功能描述：按退款编号查询订单
     *
     * @param orderRefundNo 订单退款编号
     * @return {@link Orders }
     * <AUTHOR>
     * @date 2025/03/27
     */
    Orders queryOrderByRefundNo(@Param("orderRefundNo")String orderRefundNo);
}
