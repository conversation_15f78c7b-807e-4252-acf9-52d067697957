package com.zsmall.order.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.hengjian.common.tenant.mapper.TenantMapperPlus;
import com.hengjian.system.domain.SysTenant;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.vo.OrderItemVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 子订单Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
public interface OrderItemMapper extends TenantMapperPlus<OrderItem, OrderItemVo> {

    List<OrderItem> getOrderItemListByShipped(@Param("orderItemNoList") List<String> orderItemNoList);

    List<OrderItem> getAllUnDispatchedItems(@Param("orderNo") String orderNo,@Param("orderState") String orderState,@Param("fulfillmentProcess") String fulfillmentProcess);

    Integer countSoldNumber(@Param("orderState") String orderState, @Param("productCode") String productCode, @Param("sku") String sku);

    /**
     * 汇总平台应付总金额
     * @param startDate
     * @param endDate
     * @return
     */
    BigDecimal sumPlatformPayableTotalAmount(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @InterceptorIgnore(tenantLine = "true")
    Integer queryValidQuantityByActivity(@Param("activityCode") String activityCode);

    SysTenant getSysTenantByThirdChannelFlag(@Param("channelFlag") String channelFlag, @Param("tenantId") String tenantId);

}
