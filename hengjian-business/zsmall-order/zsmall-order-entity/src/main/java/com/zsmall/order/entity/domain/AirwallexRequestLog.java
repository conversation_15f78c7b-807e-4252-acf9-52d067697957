package com.zsmall.order.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Access;
import javax.persistence.AccessType;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-07
 */
@Getter
@Setter
@Accessors(chain = true)
@Access(AccessType.FIELD)
@JsonInclude(JsonInclude.Include.USE_DEFAULTS)
@AllArgsConstructor
@NoArgsConstructor
@TableName("airwallex_request_log")
public class AirwallexRequestLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 空中云汇请求接口 requestId
     */
    private String requestId;

    /**
     * 接口返回信息
     */
    private String returnInfo;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
