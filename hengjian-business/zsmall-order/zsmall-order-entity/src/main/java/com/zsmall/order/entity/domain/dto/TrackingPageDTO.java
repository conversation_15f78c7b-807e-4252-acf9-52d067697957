package com.zsmall.order.entity.domain.dto;

import lombok.Data;

/**
 * 物流跟踪单查询DTO
 *
 * <AUTHOR>
 */
@Data
public class TrackingPageDTO {

    /**
     * 筛选项
     */
    // 订单时间
    private String startDate;
    private String endDate;
    // 渠道单号
    private String channelOrderId;

    private String channelType;
    // 发货方式
    private String logisticsType;
    // 物流商
    private String logisticsCarrier;
    // 物流进度
    private String logisticsProgress;


    private String orderNo;
    private String trackingNo;
    private String productSkuCode;
    private String logisticsTrackingNo;
    private String Sku;
    private String tenantId;
    private String tenantType;


}
