package com.zsmall.order.entity.domain;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.zsmall.common.domain.SortEntity;
import com.zsmall.common.enums.order.OrderExtraInfoTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 主订单额外信息对象 order_extra_info
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("order_extra_info")
public class OrderExtraInfo extends SortEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 主订单编号
     */
    private String orderNo;

    /**
     * 信息正文
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONObject content;

    /**
     * 信息实体名
     */
    private String entityName;

    /**
     * 信息类型
     */
    private OrderExtraInfoTypeEnum infoType;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
