package com.zsmall.order.entity.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.zsmall.common.enums.order.AddressFlagEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2023/12/12 16:25
 */
@Getter
@Setter
@Accessors(chain = true)
@Access(AccessType.FIELD)
@JsonInclude(JsonInclude.Include.USE_DEFAULTS)
public class ErpSaleOrderAddressDTO {
    /**
     * 订单ID
     */
    @Column(name = "head_id")
    private Integer headId;
    /**
     * 地址1
     */
    @Column(name = "addressline1")
    private String addressline1;
    /**
     * 地址2
     */
    @Column(name = "addressline2")
    private String addressline2;
    /**
     * 地址3
     */
    @Column(name = "addressline3")
    private String addressline3;
    /**
     * 地址类型
     */
    @Column(name = "address_type")
    private String addressType;
    /**
     * 国家
     */
    @Column(name = "county")
    private String county;
    /**
     * 州或省份
     */
    @Column(name = "state")
    private String state;


    /**
     * 城市
     */
    @Column(name = "city")
    private String city;
    /**
     * 收货行政区
     */
    @Column(name = "district")
    private String district;
    /**
     * 邮编
     */
    @Column(name = "postal_code")
    private String postalCode;
    /**
     * 国家编码
     */
    @Column(name = "country_code")
    private String countryCode;
    /**
     * 电话
     */
    @Column(name = "phone")
    private String phone;
    /**
     * 平台传过来的客户名
     */
    @Column(name = "customer_outer_id")
    private String buyId;
    /**
     * 手机
     */
    @Column(name = "mobile_phone")
    private String mobilePhone;
    /**
     * 地址标志 0 收件人地址
     */
    @Column(name = "address_flag")
    private AddressFlagEnum addressFlag;


}
