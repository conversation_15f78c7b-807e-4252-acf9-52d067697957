package com.zsmall.order.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.common.enums.order.OrderExceptionEnum;
import com.zsmall.order.entity.domain.OrderExceptionHandleRecord;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.mapper.OrderExceptionHandleRecordMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


/**
 * 订单异常处理记录Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2024-11-26
 */
@RequiredArgsConstructor
@Service
public class IOrderExceptionHandleRecordService extends ServiceImpl<OrderExceptionHandleRecordMapper, OrderExceptionHandleRecord> {

    private final OrderExceptionHandleRecordMapper baseMapper;

    public OrderExceptionHandleRecord getByOrderId(Long orderId) {
        LambdaQueryWrapper<OrderExceptionHandleRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderExceptionHandleRecord::getOrderId,orderId);
        return baseMapper.selectOne(lqw);
    }
}
