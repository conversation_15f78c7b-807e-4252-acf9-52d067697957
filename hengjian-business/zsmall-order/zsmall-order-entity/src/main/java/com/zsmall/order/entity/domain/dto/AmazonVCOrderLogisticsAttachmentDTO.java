package com.zsmall.order.entity.domain.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年10月29日  16:00
 * @description: amazonVC订单获取面单信息类
 */
@Data
@Accessors(chain = true)
public class AmazonVCOrderLogisticsAttachmentDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String orderNo;

    private String channel;

    private String containerType;

    private String warehouseCode;

    private List<AmazonVCOrderLogisticsAttachmentItemDTO> items;
}
