package com.zsmall.order.factory;

import com.hengjian.common.core.exception.RStatusCodeException;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.OrderRefund;
import com.zsmall.order.entity.domain.OrderRefundItem;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.bo.SubmitRefundApplyBo;
import org.springframework.beans.factory.InitializingBean;

/**
 * 售后规则处理
 * <AUTHOR>
 * @date 2023/2/7
 */
public interface RefundRuleService extends InitializingBean {

  /**
   * 售后规则“是否”类条件处理
   * @param whetherValue
   * @param orderItem
   * @param orderRefund
   * @param orderRefundItem
   */
  void refundRuleWhetherHandle(Integer whetherValue, SubmitRefundApplyBo requestBody,
                               OrderItem orderItem, OrderRefund orderRefund, OrderRefundItem orderRefundItem) throws RStatusCodeException;

  /**
   * 售后规则“是否”类条件处理（主订单适用）
   * @param whetherValue
   * @param order
   * @param orderRefund
   * @param orderRefundItem
   */
  void refundRuleWhetherHandle(Integer whetherValue, SubmitRefundApplyBo requestBody,
                               Orders order, OrderItem orderItem, OrderRefund orderRefund, OrderRefundItem orderRefundItem) throws RStatusCodeException;
}
