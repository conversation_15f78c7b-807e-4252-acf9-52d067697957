//package com.zsmall.order.entity.domain.dto;
//
//import com.baomidou.mybatisplus.annotation.FieldStrategy;
//import com.baomidou.mybatisplus.annotation.IdType;
//import com.baomidou.mybatisplus.annotation.TableField;
//import com.baomidou.mybatisplus.annotation.TableId;
//import lombok.Data;
//
//import java.io.Serializable;
//import java.util.Date;
//import java.util.List;
//
///**
// * lty notes
// *
// * <AUTHOR> Theo
// * @create 2023/12/13 9:54
// */
//@Data
//
//public class MarListingInfoDTO implements Serializable {
//    /**
//     * 主键
//     */
//    @TableId(type = IdType.AUTO)
//    private Long id;
//
//    /**
//     * 组织ID
//     */
//    private Long organizationId;
//
//    /**
//     * ASIN
//     */
//    private String asin;
//
//    /**
//     * 标题
//     */
//    @TableField(updateStrategy= FieldStrategy.IGNORED)
//    private String asinTitle;
//
//    /**
//     * 优惠券
//     */
//    @TableField(updateStrategy= FieldStrategy.IGNORED)
//    private String coupon;
//
//    /**
//     * 原价(折扣前价格)
//     */
//    @TableField(updateStrategy= FieldStrategy.IGNORED)
//    private String listPrice;
//
//    /**
//     * 折扣(率)
//     */
//    @TableField(updateStrategy= FieldStrategy.IGNORED)
//    private String discount;
//
//    /**
//     * 折扣后价格
//     */
//    @TableField(updateStrategy= FieldStrategy.IGNORED)
//    private String discountPrice;
//
//    /**
//     * deal价格(deal)
//     */
//    @TableField(updateStrategy= FieldStrategy.IGNORED)
//    private String dealPrice;
//
//    /**
//     * 折扣的金额(百分比)(deal)
//     */
//    @TableField(updateStrategy= FieldStrategy.IGNORED)
//    private String youSave;
//
//    /**
//     * 购物车价格
//     */
//    @TableField(updateStrategy= FieldStrategy.IGNORED)
//    private String cartPrice;
//
//    /**
//     * 库存状态
//     */
//    @TableField(updateStrategy= FieldStrategy.IGNORED)
//    private String salesStatus;
//
//    /**
//     * Listing上线时间
//     */
//    @TableField(updateStrategy= FieldStrategy.IGNORED)
//    private String firstDate;
//
//    /**
//     * 转换后Listing上线时间
//     */
//    private Date convertFirstDate;
//
//    /**
//     * 确认发货到送达时间
//     */
//    @TableField(updateStrategy= FieldStrategy.IGNORED)
//    private String deliveryTime;
//
//    /**
//     * 送达时间30024
//     */
//    private String deliveryTimeGa;
//
//    /**
//     * 销售状态,发货时间30024
//     */
//    private String salesStatusGa;
//
//    /**
//     * 送达时间07302
//     */
//    private String deliveryTimeNj;
//
//    /**
//     * 销售状态,发货时间07302
//     */
//    private String salesStatusNj;
//
//    /**
//     * 总星级
//     */
//    private String starRating;
//
//    /**
//     * 大类名称
//     */
//    private String mainCategory;
//
//    /**
//     * 大类排名
//     */
//    private String mainCategoryRank;
//
//    /**
//     * 小类名称
//     */
//    private String subCategory;
//
//    /**
//     * 小类排名
//     */
//    private String subCategoryRank;
//
//    /**
//     * 北京时间
//     */
//    private Date bjTime;
//
//    /**
//     * utc时间
//     */
//    private Date sutcTime;
//
//    /**
//     * 美国时间
//     */
//    private Date usTime;
//
//    /**
//     * 创建者id
//     */
//    private Integer createdBy;
//
//    /**
//     * 创建者名称
//     */
//    private String createdName;
//
//    /**
//     * 创建时间戳
//     */
//    private Date createdAt;
//
//    /**
//     * 更新者ID
//     */
//    private Integer updatedBy;
//
//    /**
//     * 更新者名称
//     */
//    private String updatedName;
//
//    /**
//     * 更新时间戳
//     */
//    private Date updatedAt;
//
//    /**
//     * 删除者ID
//     */
//    private Integer disabledBy;
//
//    /**
//     * 删除者名称
//     */
//    private String disabledName;
//
//    /**
//     * 删除时间戳
//     */
//    private Date disabledAt;
//
//    @TableField(exist = false)
//    private static final long serialVersionUID = 1L;
//
//
//    /**
//     * 图片信息
//     */
//    @TableField(exist = false)
//    private List<String> images;
//
//    /*描述信息*/
//    @TableField(exist = false)
//    private List<String> describe5;
//
////    /*相似商品信息*/
////    @TableField(exist = false)
////    private MarListingInfoSimilar consider;
////
////    /*同步地区发货及到货时间*/
////    @TableField(exist = false)
////    private List<MarListingDeliveryInfo> receiptDeliveryDetails;
//
//
//    /**
//     * 搜索条件
//     */
//    @TableField(exist = false)
//    private String searchTime;
//
//    /**
//     * 搜索条件 价格监控..
//     */
//    @TableField(exist = false)
//    private String monitortype;
//
//
//    /**
//     * 搜索条件 原价..
//     */
//    @TableField(exist = false)
//    private String project;
//}
