package com.zsmall.order.entity.domain.dto;

import com.zsmall.common.domain.bo.TrackingNoBo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 响应信息-退款步骤
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class RefundStageDTO {

    /**
     * 时间
     */
    private String time;
    /**
     * 角色
     */
    private String role;
    /**
     * 图片展示地址集合
     */
    private List<String> imageShowUrls;
    /**
     * 文本
     */
    private String text_en_US;
    /**
     * 文本
     */
    private String text_zh_CN;
    /**
     * 排序号
     */
    private Integer sort;
    /**
     * 退货物流跟踪单号
     */
    private String trackingNo;
    /**
     * 退货物流承运商
     */
    private String carrier;
    /**
     * 退货取件地址
     */
    private String pickupAddress;

    /**
     * 物流跟踪单号集合
     */
    private List<TrackingNoBo> trackingInfoList;

}
