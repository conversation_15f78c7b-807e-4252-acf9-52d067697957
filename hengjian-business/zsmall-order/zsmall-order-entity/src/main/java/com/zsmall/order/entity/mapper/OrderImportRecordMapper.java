package com.zsmall.order.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.order.entity.domain.OrderImportRecord;
import com.zsmall.order.entity.domain.vo.orderImport.OrderImportRecordVo;
import org.apache.ibatis.annotations.Param;

/**
 * 订单导入记录Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-08
 */
public interface OrderImportRecordMapper extends BaseMapperPlus<OrderImportRecord, OrderImportRecordVo> {

    /**
     * 分页查询
     * @param page
     * @param queryValue
     * @return
     */
    Page<OrderImportRecordVo> queryPage(Page<OrderImportRecordVo> page, @Param("queryValue") String queryValue);

    /** 查询导入记录编号是否已存在 */
    @InterceptorIgnore(tenantLine = "true")
    boolean existImportRecordNo(@Param("importRecordNo") String importRecordNo);

}
