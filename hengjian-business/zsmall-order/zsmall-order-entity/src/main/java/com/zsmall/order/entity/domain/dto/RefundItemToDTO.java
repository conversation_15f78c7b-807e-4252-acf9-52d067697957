package com.zsmall.order.entity.domain.dto;

import com.zsmall.order.entity.domain.OrderItemProductSku;
import com.zsmall.order.entity.domain.OrderRefundItem;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.vo.RefundDetailVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 响应信息-退款单信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class RefundItemToDTO extends RefundDetailVo {


    //售后数量
    private Integer num;
    private String channelType;
    private String orderType;

    private Orders orders;
    private List<OrderRefundItem> orderRefundItems;
    private OrderItemProductSku orderItemProductSku;


}
