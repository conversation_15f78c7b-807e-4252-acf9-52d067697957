package com.zsmall.order.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.order.entity.domain.OrderItemTrackingImportRecord;
import com.zsmall.order.entity.mapper.OrderItemTrackingImportRecordMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/3/31 16:43
 */
@RequiredArgsConstructor
@Service
public class IOrderItemTrackingImportRecordService extends ServiceImpl<OrderItemTrackingImportRecordMapper, OrderItemTrackingImportRecord> {
    public boolean existsOrderItemTrackingImportNo(String code) {
        return baseMapper.existsOrderItemTrackingImportNo(code);
    }

    public List<OrderItemTrackingImportRecord> queryByIds(List<Long> ids) {
        LambdaQueryWrapper<OrderItemTrackingImportRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OrderItemTrackingImportRecord::getId, ids);
        wrapper.eq(OrderItemTrackingImportRecord::getDelFlag, 0);
        return baseMapper.selectList(wrapper);
    }

    public List<OrderItemTrackingImportRecord> queryFailedByIds(List<Long> ids) {
        return baseMapper.queryFailedByIds(ids);
    }
}
