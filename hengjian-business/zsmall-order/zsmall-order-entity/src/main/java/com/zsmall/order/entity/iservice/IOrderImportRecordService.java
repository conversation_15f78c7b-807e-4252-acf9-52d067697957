package com.zsmall.order.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.common.enums.orderImportRecord.ImportStateEnum;
import com.zsmall.order.entity.domain.OrderImportRecord;
import com.zsmall.order.entity.domain.vo.orderImport.OrderImportRecordVo;
import com.zsmall.order.entity.mapper.OrderImportRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 订单导入记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-08
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class IOrderImportRecordService extends ServiceImpl<OrderImportRecordMapper, OrderImportRecord> {

    private final OrderImportRecordMapper baseMapper;

    public boolean existImportRecordNo(String importRecordNo) {
        return baseMapper.existImportRecordNo(importRecordNo);
    }

    /**
     * 查询订单导入记录列表
     */
    public TableDataInfo<OrderImportRecordVo> queryPageList(PageQuery pageQuery, String queryValue) {
        Page<OrderImportRecordVo> result = baseMapper.queryPage(pageQuery.build(), queryValue);
        return TableDataInfo.build(result);
    }

    /**
     * 根据记录编号查询
     *
     * @return
     */
    public OrderImportRecord queryByRecordNo(String recordNo) {
        log.info("进入【根据记录编号查询】 recordNo = {}", recordNo);
        return lambdaQuery().eq(OrderImportRecord::getImportRecordNo, recordNo).one();
    }

    /**
     * 根据记录编号查询
     *
     * @return
     */
    public OrderImportRecord queryByRecordNoAndState(String recordNo, ImportStateEnum importState) {
        log.info("进入【根据记录编号查询】 recordNo = {}, importState = {}", recordNo, importState);
        return lambdaQuery().eq(OrderImportRecord::getImportRecordNo, recordNo)
            .eq(OrderImportRecord::getImportState, importState).one();
    }


    /**
     * 功能描述：新交易更新 0 未下单 1正在下单 2 已下单 3.下单失败
     *
     * @param recordNo 记录编号
     * @param val      瓦尔
     * <AUTHOR>
     * @date 2024/09/02
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateForNewTransact(String recordNo, int val) {
        LambdaUpdateWrapper<OrderImportRecord> successWrapper = new LambdaUpdateWrapper<OrderImportRecord>()
            .eq(OrderImportRecord::getImportRecordNo, recordNo)
            .set(OrderImportRecord::getOrderTempState, val);
        update(successWrapper);
    }
}
