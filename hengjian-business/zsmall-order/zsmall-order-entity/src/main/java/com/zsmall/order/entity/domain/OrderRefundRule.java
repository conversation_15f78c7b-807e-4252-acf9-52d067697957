package com.zsmall.order.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.annotaion.RefundRuleAnalysisAnnotation;
import com.zsmall.common.domain.SortEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 售后规则对象 order_refund_rule
 *
 * <AUTHOR> Li
 * @date 2023-06-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("order_refund_rule")
public class OrderRefundRule extends SortEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 退款规则编号
     */
    private String refundRuleNo;

    /**
     * 适用履约状态（适用于未发货、已发货等状态）
     */
    private String applicableFulfillment;

    /**
     * 售后原因明细（中文）
     */
    private String refundReasonZhCn;

    /**
     * 售后原因明细（英文）
     */
    private String refundReasonEnUs;

    /**
     * 是否需要员工审核（10-不需要，20-需要，30-超阈值时需要）
     */
    @RefundRuleAnalysisAnnotation(ruleType = "WhetherReviewMd", order = 2)
    private Integer whetherReviewMd;

    /**
     * 是否支持修改退款金额（10-不支持，20-支持）
     */
    @RefundRuleAnalysisAnnotation(ruleType = "WhetherSupportModifyAmount", order = 1)
    private Integer whetherSupportModifyAmount;

    /**
     * 是否需要提供图片举证（10-不需要，20-需要）
     */
    @RefundRuleAnalysisAnnotation(ruleType = "WhetherProvidePictures", order = 3)
    private Integer whetherProvidePictures;

    /**
     * 是否还原库存（10-不还原，20-整单还原，30-可执行金额为零时还原）
     */
    @RefundRuleAnalysisAnnotation(ruleType = "WhetherRestoreInventory", order = 4)
    private Integer whetherRestoreInventory;

    /**
     * 排序（业务中按照倒序查询）
     */
    private Integer sort;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
