package com.zsmall.order.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.order.entity.domain.WholesaleIntentionOrderItem;
import com.zsmall.order.entity.mapper.WholesaleIntentionOrderItemMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【wholesale_intention_order_item(国外现货批发意向子订单)】的数据库操作Service实现
 * @createDate 2023-07-24 10:46:40
 */
@Service
public class IWholesaleIntentionOrderItemService extends ServiceImpl<WholesaleIntentionOrderItemMapper, WholesaleIntentionOrderItem> {


    /**
     * 根据主订单id查询子订单集合
     *
     * @param wiOrderId
     * @return
     */
    @InMethodLog(value = "根据主订单id查询子订单集合")
    public List<WholesaleIntentionOrderItem> queryWIOrderId(Long wiOrderId) {
        return TenantHelper.ignore(() -> lambdaQuery().eq(WholesaleIntentionOrderItem::getWholesaleIntentionOrderId, wiOrderId).list(),
            TenantType.Manager, TenantType.Supplier);
    }

    public boolean existsWholesaleIntentionOrderItemNo(String wholesaleIntentionOrderNo) {
        return baseMapper.existsWholesaleIntentionOrderItemNo(wholesaleIntentionOrderNo);
    }

    @InMethodLog(value = "根据productSkuCodeList集合获取正在进行中的批发意向单")
    public List<WholesaleIntentionOrderItem> existsProcessingOrderItem(List<String> productSkuCodeList) {
        return baseMapper.existsProcessingOrderItem(productSkuCodeList);
    }
}




