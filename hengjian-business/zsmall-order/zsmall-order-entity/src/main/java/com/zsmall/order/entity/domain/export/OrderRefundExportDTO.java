
package com.zsmall.order.entity.domain.export;

import com.alibaba.excel.annotation.ExcelProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderRefundExportDTO implements Serializable {

    @ExcelProperty(value = "退货单号")
    private String orderRefundNo;

    @ExcelProperty(value = "订单编号")
    private String orderNo;

    @ExcelProperty(value = "创建日期")
    private String refundApplyTime;

    @ExcelProperty(value = "分销商")
    private String tenantId;

    @ExcelProperty(value = "店铺标识")
    private String thirdChannelFlag;

    @ExcelProperty(value = "客户名称")
    private String name;

    @ExcelProperty(value = "SKU ID")
    private String itemNo;

    @ExcelProperty(value = "供应商")
    private String supplierName;
    @ExcelProperty(value = "币种")
    private String currencyCode;
    @ExcelProperty(value = "退款总金额")
    private String total;

    @ExcelProperty(value = "支付状态")
    private String refundAmountState;

    @ExcelProperty(value = "履约状态")
    private String fulfillmentProgress;

    @ExcelProperty(value = "销售渠道")
    private String channelType;





}
