package com.zsmall.order.entity.domain.vo;

import com.zsmall.common.domain.bo.TrackingNoBo;
import com.zsmall.order.entity.domain.dto.RefundStageDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 响应信息-退款单信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class RefundItemDetailVo {

//    private String orderNo;
    private String sku;
    private String orderItemNo;
//    private String orderRefundItemNo;
//    private Integer num;
    private String imageShowUrl;
//    private String description;
//    private BigDecimal actualRefundPrice;
//    private BigDecimal price;
//    private List<TrackingNoBo> trackingInfoList;
    private Boolean needSupplement;
    private String logisticsType;

    /**
     * 承运商
     */
    private String carrier;
    /**
     * 跟踪单
     */
    private String trackingNo;

    /**
     * 仓库编码
     */
//    private String warehouseCode;
    /**
     * 订单类型
     */
    private String productName;
    /**
     * itemNo
     */
    private String itemNo;
    /**
     * 退款子单编号
     */
    private String orderRefundItemNo;
    /**
     * 退款状态
     */
    private String refundItemStatus;
    /**
     * 物流进度
     */
    private String fulfillment;

    /**
     * 退款步骤
     */
    private List<RefundStageDTO> stages;

    /**
     * 物流跟踪单号集合
     */
    private List<TrackingNoBo> trackingInfoList;
}
