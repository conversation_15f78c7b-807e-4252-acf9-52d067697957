package com.zsmall.order.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.hengjian.system.domain.SysTenant;
import com.zsmall.order.entity.domain.OrderItemPrice;
import com.zsmall.order.entity.domain.OrderItemTrackingRecord;
import com.zsmall.order.entity.domain.OrderLogisticsInfo;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.bo.order.OrderAttachmentExportDto;
import com.zsmall.order.entity.domain.bo.order.OrderExportListDTO;
import com.zsmall.order.entity.domain.bo.order.OrdersPageBo;
import com.zsmall.order.entity.domain.mq.EsOrdersDTO;
import com.zsmall.order.entity.domain.openapi.OpenApiOrderSearchDTO;
import com.zsmall.order.entity.domain.vo.OrderListVo;
import com.zsmall.order.entity.domain.vo.order.OrdersVo;
import com.zsmall.order.entity.domain.vo.statistics.ChannelSalesStatisticsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 主订单Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@Mapper
public interface OrdersMapper extends BaseMapperPlus<Orders, OrdersVo> {

    Page<Orders> queryPageList(@Param("dto") OrdersPageBo dto, Page<Orders> page);

    @InterceptorIgnore(tenantLine = "true")
    boolean existsOrderNo(@Param("orderNo") String orderNo);

    Orders getByOrderNoAndSupplier(@Param("orderNo") String orderNo, @Param("supplierTenantId") String supplierTenantId);

    Orders getShippingOrder(@Param("orderNo") String orderNo, @Param("orderState") String orderState, @Param("shippingOrderState") String shippingOrderState);

    @InterceptorIgnore(tenantLine = "true")
    List<ChannelSalesStatisticsVo> queryChannelSalesStatisticsForMag(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    List<ChannelSalesStatisticsVo> queryChannelSalesStatisticsForDis(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @InterceptorIgnore(tenantLine = "true")
    List<ChannelSalesStatisticsVo> queryChannelSalesStatisticsForSup(@Param("supplierId") String supplierId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    BigDecimal statsPlatformOrderPaymentAmount4Wholesale(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    SysTenant getSysTenantByThirdChannelFlag(String thirdChannelFlag);

    List<Orders> selectOrderDetailByOrderNos(@Param("orderNoSet") Collection<String> orderNoSet, @Param("tenantId") String tenantId);

    List<OrderExportListDTO> getOrderExportList(@Param("dto") OrdersPageBo orderExportQueryDTO, @Param("page") Integer page, @Param("pageSize") Integer pageSize);

    Integer getOrderExportCount(@Param("dto") OrdersPageBo orderExportQueryDTO);



    /**
     * 根据渠道订单号修改tracking回传标识
     *
     * @param channelOrderNo
     */
    void updateOrderTrackingFlag(@Param("channelOrderNo") String channelOrderNo, @Param("trackingFlag") Integer trackingFlag, @Param("tenantId") String tenantId);


    /**
     * 根据渠道订单号和channelId修改 trackingFlag
     *
     * @param channelOrderNo
     * @param channelId
     * @param trackingFlag
     */
    void updateOrderTrackingFlagByChannelOrderNoAndChannelId(@Param("channelOrderNo")String channelOrderNo,@Param("channelId") Long channelId, @Param("tenantId")Integer trackingFlag);

    /**
     * 根据渠道订单号集合修改tracking回传标识
     *
     * @param channelOrderNoList
     * @param trackingFlag
     */
    void updateOrderTrackingFlagByChannelOrderNo(@Param("channelOrderNoList") List<String> channelOrderNoList, @Param("trackingFlag") Integer trackingFlag);


    List<OrderExportListDTO> selectOrderAddressInfo(@Param("tableName") String tableName);
    List<OrderExportListDTO> selectOrderLogisticsInfo(@Param("tableName") String tableName);
    List<OrderExportListDTO> selectOrderAddressAndLogisticsInfo(@Param("tableName") String tableName);
    List<OrderExportListDTO> selectOrderTrackingRecord(@Param("tableName") String tableName);
    List<OrderAttachmentExportDto> selectOrderAttach(@Param("tableName") String tableName);
    List<OrderExportListDTO> selectOrderSkuName(@Param("tableName") String tableName);
    List<com.zsmall.order.entity.domain.OrderItem> selectOrderItem(@Param("tableName") String tableName);
    List<OrderItemPrice> selectOrderItemPrice(@Param("tableName") String tableName);
    List<OrderExportListDTO> selectOrderWarehouseCode(@Param("tableName")String temporaryTable);
    List<OrderItemTrackingRecord> selectOrderItemTrackingRecord(@Param("tableName")String temporaryTable);
    List<OrderLogisticsInfo> selectOrderLogisticsInfoByEsInsert(@Param("tableName")String temporaryTable);
    /**
     * 根据渠道sku和exceptionCode获取订单信息
     *
     * @param channelSkuList
     * @param exceptionCode
     * @return
     */
    List<Orders> getByChannelSkuAndExceptionCode(@Param("channelSkuList")List<String> channelSkuList,@Param("exceptionCode") Integer exceptionCode ,@Param("orderState")String orderState,@Param("orderSource") Integer orderSource);

    Integer getByChannelSkuAndExceptionCodeNum(@Param("channelSkuList")List<String> channelSkuList,@Param("exceptionCode") Integer exceptionCode ,@Param("orderState")String orderState,@Param("orderSource") Integer orderSource);

    /**
     * 根据渠道sku和exceptionCode获取订单信息,分页
     *
     * @param pageSize
     * @param offset
     * @param channelSkuList
     * @param exceptionCode
     * @param orderState
     * @param orderSource
     * @return
     */
    List<Orders> getByChannelSkuAndExceptionCodePage(@Param("pageSize") int pageSize, @Param("offset") int offset,@Param("channelSkuList")List<String> channelSkuList,@Param("exceptionCode") Integer exceptionCode ,@Param("orderState")String orderState,@Param("orderSource") Integer orderSource);

    /**
     * 根据渠道sku获取订单信息
     *
     * @param pageSize
     * @param offset
     * @param orderNoList
     * @param exceptionCode
     * @param orderState
     * @param orderSource
     * @return
     */
    List<Orders> getByOrderNoListAndExceptionCodePage(@Param("pageSize") int pageSize, @Param("offset") int offset,@Param("orderNoList")List<String> orderNoList,@Param("exceptionCode") Integer exceptionCode ,@Param("orderState")String orderState,@Param("orderSource") Integer orderSource);
    /**
     * @description:  最新订单列表查询
     * @author: len
    *  @date: 2024/8/14 11:26
     * @param: dto
     * @param: page
     * @return: com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.zsmall.order.entity.domain.vo.OrderListVo>
     **/
    List<OrderListVo> queryOrderListVoPageList(@Param("dto") OrdersPageBo dto, Integer page,Integer limit);
    /**
     * @description: 订单列表查询
     * @author: Len
     * @date: 2024/9/13 15:57
     * @param: sku
     * @param: productName
     * @param: itemNo
     * @return: java.util.List<java.lang.Long>
     **/
    List<Long> getOrderIdBySkuQuery(@Param("sku") String sku,@Param("productName") String productName);

    List<Long> getOrderIdByActivityQuery(@Param("tenantType") String tenantType,@Param("activityCode") String activityCode);

    List<Long> getOrderIdByTrackingNoQuery(@Param("trackingNo") String trackingNo);

    Integer countByOrderListVo(@Param("dto") OrdersPageBo dto);

    /**
     * 根据渠道订单号、订单状态、展示订单查询订单数量
     * @param channelOrderNo
     * @param orderState
     * @param isShow
     * @return
     */
    Integer countChannelOrderNo(@Param("channelOrderNo") String channelOrderNo, @Param("orderState") String orderState, @Param("isShow") Boolean isShow);

    /**
     * 订单附件导出查询数量
     * @param dto
     * @return
     */
    Integer countOrderAttachment(@Param("dto") OrdersPageBo dto);

    /**
     * 订单附件导出查询
     * @param startRow 开始行
     * @param finalPageSize 结束行
     * @param dto
     * @return
     */
    List<OrderAttachmentExportDto> selectOrderAttachment(@Param("startRow") int startRow,@Param("finalPageSize") int finalPageSize,@Param("dto") OrdersPageBo dto);

    /**
     * OpenAPI 查询订单详情
     * @param apiOrderSearchDTO
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<String> selectPageOrderWithOpenApi(@Param("dto") OpenApiOrderSearchDTO apiOrderSearchDTO);

    /**
     * OpenAPI查询订单数量
     * @param apiOrderSearchDTO
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    int selectOrderCountWithOpenApi(@Param("dto") OpenApiOrderSearchDTO apiOrderSearchDTO);

    @InterceptorIgnore(tenantLine = "true")
    List<Orders> selectOrderDetailWithOpenApi(@Param("dto") OpenApiOrderSearchDTO apiOrderSearchDTO);
    @InterceptorIgnore(tenantLine = "true")
    List<Orders> getOrderNoByOrderExtendId(@Param("orderExtendId") String orderExtendId, @Param("tenantId") String tenantId);

    /**
     *
     * @return
     */
    int countOrder();

    Set<String> getOrderNos(int page, int pageSize);

    /**
     *
     * @param page 页码
     * @param pageSize 每页条数
     * @param orderNos 订单号集合
     * @return 订单列表
     */
    List<EsOrdersDTO> getOrders(@Param("page") int page, @Param("pageSize") int pageSize, @Param("orderNos") Set<String> orderNos);

    /**
     * 更新订单的物流异常码
     * @param orderId
     */
    void updateShipmentExceptionCodeNullById(@Param("orderId") Long orderId);
}
