package com.zsmall.order.entity.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.zsmall.common.enums.order.B2cIsShipEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2023/12/12 16:42
 */
@Getter
@Setter
@Accessors(chain = true)
@Access(AccessType.FIELD)
@JsonInclude(JsonInclude.Include.USE_DEFAULTS)
public class ErpSaleOrderItemDTO implements Serializable {
    private static final long serialVersionUID = 693468696296687126L;

    /**
     * 项目ID
     */


    @Column(name = "line_id")
    private Integer lineId;
    /**
     * 组织ID
     */


    @Column(name = "org_id")
    private Integer orgId;
    /**
     * 渠道类型
     */


    @Column(name = "channel")
    private String channel;
    /**
     * 渠道ID
     */


    @Column(name = "channel_id")
    private Integer channelId;
    /**
     * 渠道标识
     */


    @Column(name = "channel_flag")
    private String channelFlag;
    /**
     * 单据行
     */
    @Column(name = "channel_order_item_id")
    private String channelOrderItemId;
    /**
     * 订单ID
     */


    @Column(name = "head_id")
    private Integer headId;
    /**
     * 渠道订单号
     */
    @Column(name = "order_no")
    private String orderNo;
    /**
     * 渠道sku
     */

    @Column(name = "seller_sku")
    private String sellerSku;
    /**
     * 系统sku
     */

    @Column(name = "erp_sku")
    private String erpSku;
    /**
     * 是否需要发货,0自动;1手动
     */

    @Column(name = "is_ship")
    private B2cIsShipEnum isShip;
    /**
     * 识别码
     */


    @Column(name = "asin")
    private String asin;
    /**
     * 识别码
     */


    @Column(name = "parent_asin")
    private String parentAsin;
    /**
     * 商品名称
     */


    @Column(name = "title")
    private String title;
    /**
     * 数量
     */

    @Column(name = "quantity")
    private Integer quantity;
    /**
     * 确认数量
     */


    @Column(name = "confirmed_qty")
    private Integer confirmedQty;
    /**
     * 发运数量
     */


    @Column(name = "shiped_quantity")
    private Integer shipedQuantity;
    /**
     * 仓库ID
     */


    @Column(name = "org_warehouse_id")
    private Integer orgWarehouseId;
    /**
     * 行单价
     */

    private BigDecimal price;

    @Column(name = "item_amount")
    private BigDecimal itemAmount;
    /**
     * 行销售金额
     */


    @Column(name = "channel_total_sales_amount")
    private BigDecimal channelTotalSalesAmount;

    /**
     * 确认金额
     */


    @Column(name = "confirmed_total_amount")
    private BigDecimal confirmedTotalAmount;
    /**
     * 美金金额
     */


    @Column(name = "us_amount")
    private BigDecimal usAmount;
    /**
     * 币种
     */


    @Column(name = "currency_code")
    private String currencyCode;
    /**
     * 礼品信息
     */


    @Column(name = "gift_message_text")
    private String giftMessageText;
    /**
     * 礼品币种
     */


    @Column(name = "gift_wrap_currency_code")
    private String giftWrapCurrencyCode;
    /**
     * 礼品金额
     */


    @Column(name = "gift_wrap_price")
    private BigDecimal giftWrapPrice;
    /**
     * 礼品税费币种
     */


    @Column(name = "gift_wrap_tax_currency_code")
    private String giftWrapTaxCurrencyCode;
    /**
     * 礼品税费金额
     */


    @Column(name = "gift_wrap_tax_price")
    private BigDecimal giftWrapTaxPrice;
    /**
     * 促销标识
     */


    @Column(name = "promotion_ids")
    private String promotionIds;
    /**
     * 促销折扣币种
     */


    @Column(name = "promotion_discount_currency_code")
    private String promotionDiscountCurrencyCode;
    /**
     * 折扣金额
     */


    @Column(name = "promotion_discount_price")
    private BigDecimal promotionDiscountPrice;
    /**
     * 税费金额
     */


    @Column(name = "tax_price")
    private BigDecimal taxPrice;
    /**
     * 税币种
     */


    @Column(name = "tax_currency_code")
    private String taxCurrencyCode;
    /**
     * 运费
     */
    @Column(name = "shipping_status")
    private String shippingStatus;

    @Column(name = "shipping_price")
    private BigDecimal shippingPrice;
    /**
     * 运费币种
     */


    @Column(name = "shipping_currency_code")
    private String shippingCurrencyCode;
    /**
     * 运费成本
     */


    @Column(name = "shipping_cost")
    private BigDecimal shippingCost;
    /**
     * 运费成本币种
     */


    @Column(name = "shipping_cost_currency_code")
    private String shippingCostCurrencyCode;
    /**
     * 运费税费金额
     */


    @Column(name = "shipping_tax_price")
    private BigDecimal shippingTaxPrice;
    /**
     * 运费税费币种
     */


    @Column(name = "shipping_tax_currency_code")
    private String shippingTaxCurrencyCode;
    /**
     * 运费折扣币种
     */


    @Column(name = "shipping_discount_currency_code")
    private String shippingDiscountCurrencyCode;
    /**
     * 运费折扣金额
     */


    @Column(name = "shipping_discount_price")
    private BigDecimal shippingDiscountPrice;
    /**
     * 处理费
     */


    @Column(name = "process_price")
    private BigDecimal processPrice;
    /**
     * 云仓回传发货方式
     */


    @Column(name = "warehouse_return_method_code")
    private String warehouseReturnMethodCode;
    /**
     * 追踪号
     */
    @Column(name = "track_no")
    private String trackNo;
    /**
     * 备注
     */


    @Column(name = "remark")
    private String remark;
    /**
     * 显示的备注
     */


    @Column(name = "display_comment")
    private String displayComment;
    /**
     * 产品类型 简单产品、打包产品
     */


    @Column(name = "sku_type")
    private Integer skuType;
    /**
     * item成本  fob成本
     */


    @Column(name = "item_cost")
    private BigDecimal itemCost;
    /**
     * ddp成本
     */


    @Column(name = "ddp_cost")
    private BigDecimal ddpCost;
    /**
     * 成本币种
     */


    @Column(name = "cost_currency")
    private String costCurrency;
    /**
     * 渠道创建时间
     */


    @Column(name = "channel_created")
    private LocalDateTime channelCreated;
    /**
     * 渠道更新时间
     */


    @Column(name = "channel_updated")
    private LocalDateTime channelUpdated;
    /**
     * 禁用时间戳
     */


    @Column(name = "disabled_at")
    @Accessors(chain = false)
    private Integer disabledAt;
    /**
     * 禁用者id
     */


    @Column(name = "disabled_by")
    @Accessors(chain = false)
    private Integer disabledBy;
    /**
     * 禁用者名称
     */


    @Column(name = "disabled_name")
    @Accessors(chain = false)
    private String disabledName;

    /**
     * 更新标记
     */


    @Column(name = "data_update_flag")
    private Integer dataUpdateFlag;

    /**
     * 原始金额记录
     */


    @Column(name = "ori_item_amount")
    private BigDecimal oriItemAmount;

    /**
     * 原始运费记录
     */


    @Column(name = "ori_shipping_price")
    private BigDecimal oriShippingPrice;

    /**
     * 是否允许延期交货
     */


    @Column(name = "is_back_order_allowed")
    private Integer isBackOrderAllowed;

    /**
     * 单位
     */


    @Column(name = "unit_of_measure")
    private String unitOfMeasure;

    /**
     * 单位尺寸
     */


    @Column(name = "unit_size")
    private String unitSize;

    /**
     * 预估物流费
     */


    @Column(name = "estimate_ship_cost")
    private BigDecimal estimateShipCost;



    @Column(name = "discount_price")
    private BigDecimal discountPrice;


    /**
     * 回传平台的carriercode
     */
    @Transient
    private String trackingMethodCode;

    @Transient
    private String picUrl;
}
