package com.zsmall.order.entity.iservice;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.order.OrderRefundStateType;
import com.zsmall.order.entity.domain.OrderRefundItem;
import com.zsmall.order.entity.domain.bo.OrderRefundItemBo;
import com.zsmall.order.entity.domain.vo.OrderRefundItemVo;
import com.zsmall.order.entity.mapper.OrderRefundItemMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 售后申请子单Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-08
 */
@RequiredArgsConstructor
@Service
public class IOrderRefundItemService extends ServiceImpl<OrderRefundItemMapper, OrderRefundItem> {

    private final OrderRefundItemMapper baseMapper;


    /**
     * 查询售后申请子单
     */
    public OrderRefundItemVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询售后申请子单列表
     */
    public TableDataInfo<OrderRefundItemVo> queryPageList(OrderRefundItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OrderRefundItem> lqw = buildQueryWrapper(bo);
        Page<OrderRefundItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询售后申请子单列表
     */
    public List<OrderRefundItemVo> queryList(OrderRefundItemBo bo) {
        LambdaQueryWrapper<OrderRefundItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OrderRefundItem> buildQueryWrapper(OrderRefundItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrderRefundItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getOrderId() != null, OrderRefundItem::getOrderId, bo.getOrderId());
        lqw.eq(bo.getOrderItemId() != null, OrderRefundItem::getOrderItemId, bo.getOrderItemId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), OrderRefundItem::getOrderNo, bo.getOrderNo());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderItemNo()), OrderRefundItem::getOrderItemNo, bo.getOrderItemNo());
        lqw.eq(bo.getOrderRefundId() != null, OrderRefundItem::getOrderRefundId, bo.getOrderRefundId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderRefundNo()), OrderRefundItem::getOrderRefundNo, bo.getOrderRefundNo());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderRefundItemNo()), OrderRefundItem::getOrderRefundItemNo, bo.getOrderRefundItemNo());
        lqw.eq(bo.getRefundQuantity() != null, OrderRefundItem::getRefundQuantity, bo.getRefundQuantity());
        lqw.eq(StringUtils.isNotBlank(bo.getProductSkuCode()), OrderRefundItem::getProductSkuCode, bo.getProductSkuCode());
        lqw.eq(StringUtils.isNotBlank(bo.getActivityType()), OrderRefundItem::getActivityType, bo.getActivityType());
        lqw.eq(StringUtils.isNotBlank(bo.getActivityCode()), OrderRefundItem::getActivityCode, bo.getActivityCode());
        lqw.eq(bo.getOriginalPayableTotalAmount() != null, OrderRefundItem::getOriginalPayableTotalAmount, bo.getOriginalPayableTotalAmount());
        lqw.eq(bo.getOriginalPrepaidTotalAmount() != null, OrderRefundItem::getOriginalPrepaidTotalAmount, bo.getOriginalPrepaidTotalAmount());
        lqw.eq(bo.getOriginalActualTotalAmount() != null, OrderRefundItem::getOriginalActualTotalAmount, bo.getOriginalActualTotalAmount());
        lqw.eq(bo.getPlatformPayableTotalAmount() != null, OrderRefundItem::getPlatformPayableTotalAmount, bo.getPlatformPayableTotalAmount());
        lqw.eq(bo.getPlatformPrepaidTotalAmount() != null, OrderRefundItem::getPlatformPrepaidTotalAmount, bo.getPlatformPrepaidTotalAmount());
        lqw.eq(bo.getPlatformActualTotalAmount() != null, OrderRefundItem::getPlatformActualTotalAmount, bo.getPlatformActualTotalAmount());
        return lqw;
    }

    /**
     * 新增售后申请子单
     */
    public Boolean insertByBo(OrderRefundItemBo bo) {
        OrderRefundItem add = MapstructUtils.convert(bo, OrderRefundItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改售后申请子单
     */
    public Boolean updateByBo(OrderRefundItemBo bo) {
        OrderRefundItem update = MapstructUtils.convert(bo, OrderRefundItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrderRefundItem entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除售后申请子单
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    public boolean existsOrderRefundItemNo(String orderRefundItemNo) {
        return baseMapper.existsOrderRefundItemNo(orderRefundItemNo);
    }

    /**
     * 根据子订单ID获取售后申请子单集合
     * @param orderItemId
     * @return
     */
    public List<OrderRefundItem> queryListByOrderItemId(Long orderItemId) {
        LambdaQueryWrapper<OrderRefundItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderRefundItem::getOrderItemId, orderItemId)
        .orderByDesc(OrderRefundItem::getCreateTime);
        return baseMapper.selectList(lqw);
    }



    /**
     * 根据售后单 编号获取售后申请子单集合
     * @param orderRefundNo
     * @return
     */
    public List<OrderRefundItem> queryByOrderRefundNo(String orderRefundNo) {
        LambdaQueryWrapper<OrderRefundItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderRefundItem::getOrderRefundNo, orderRefundNo)
            .orderByDesc(OrderRefundItem::getCreateTime);
        return TenantHelper.ignore(() ->baseMapper.selectList(lqw));
    }

    /**
     * 根据子订单编号判断是否存在进行中的退款子单
     * @param orderItemNo
     * @return
     */
    public Integer countByInProgress(String orderItemNo) {
        if (ObjectUtil.equals(LoginHelper.getTenantTypeEnum(), TenantType.Distributor)) {
            return baseMapper.countByInProgress(orderItemNo);
        }
        return TenantHelper.ignore(() -> baseMapper.countByInProgress(orderItemNo));
    }

    /**
     *据主订单id获取成功退款的商品总数
     * @param orderId
     * @return
     */
    public Integer getOrderTotalRefundNum(Long orderId) {
        if (ObjectUtil.equals(TenantType.Distributor, LoginHelper.getTenantTypeEnum())) {
            return baseMapper.getOrderTotalRefundNum(orderId);
        }else {
            return TenantHelper.ignore(() ->baseMapper.getOrderTotalRefundNum(orderId));
        }
    }

    /**
     *根据子订单id获取成功退款的商品总数
     * @param orderId
     * @return
     */
    public Integer getOrderItemTotalRefundNum(Long orderId) {
        if (ObjectUtil.equals(TenantType.Distributor, LoginHelper.getTenantTypeEnum())) {
            return baseMapper.getOrderItemTotalRefundNum(orderId);
        }else {
            return TenantHelper.ignore(() ->baseMapper.getOrderItemTotalRefundNum(orderId));
        }
    }


    /**
     * 根据退款子单编号获取退款子单信息
     * @param refundItemNo
     * @return
     */
    public OrderRefundItem getByOrderRefundItemNo(String refundItemNo) {
        LambdaQueryWrapper<OrderRefundItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderRefundItem::getOrderRefundItemNo, refundItemNo);
        return TenantHelper.ignore(() ->baseMapper.selectOne(lqw));
    }

    public OrderRefundItem getByOrderRefundItemNoAndState(String refundItemNo, OrderRefundStateType stateType) {
        if (ObjectUtil.equals(TenantType.Distributor, LoginHelper.getTenantTypeEnum())) {
            return baseMapper.getByOrderRefundItemNoAndState(refundItemNo, stateType.name());
        }else {
            return TenantHelper.ignore(() ->baseMapper.getByOrderRefundItemNoAndState(refundItemNo, stateType.name()));
        }
    }

    /**
     * 根据退款单id获取退款子单信息
     * @param orderRefundId
     * @return
     */
    public List<OrderRefundItem> getByOrderRefundId(Long orderRefundId) {
        LambdaQueryWrapper<OrderRefundItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderRefundItem::getOrderRefundId, orderRefundId);
        return TenantHelper.ignore(() ->baseMapper.selectList(lqw));
    }

    public List<OrderRefundItem> getByOrderId(Long orderId) {
        LambdaQueryWrapper<OrderRefundItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderRefundItem::getOrderId, orderId)
            .orderByDesc(OrderRefundItem::getCreateTime);
        return TenantHelper.ignore(() ->baseMapper.selectList(lqw));
    }

}
