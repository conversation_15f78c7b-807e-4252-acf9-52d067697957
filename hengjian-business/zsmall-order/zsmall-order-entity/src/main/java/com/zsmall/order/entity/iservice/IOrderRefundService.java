package com.zsmall.order.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.order.OrderRefundStateType;
import com.zsmall.order.entity.domain.OrderRefund;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.bo.OrderRefundBo;
import com.zsmall.order.entity.domain.bo.RefundApplyBo;
import com.zsmall.order.entity.domain.vo.OrderRefundVo;
import com.zsmall.order.entity.domain.vo.RefundApplyVo;
import com.zsmall.order.entity.mapper.OrderRefundMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 售后申请主单Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-08
 */
@RequiredArgsConstructor
@Service
public class IOrderRefundService extends ServiceImpl<OrderRefundMapper, OrderRefund> {

    private final OrderRefundMapper baseMapper;



    /**
     * 查询售后申请主单
     */
    public OrderRefundVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    @InMethodLog("根据ID查询售后申请主单（无视租户）")
    public OrderRefund queryByIdNotTenant(Long id) {
        return TenantHelper.ignore(() -> baseMapper.selectById(id));
    }


    /**
     * 查询售后申请主单列表
     */
    public IPage<RefundApplyVo> queryPageList(RefundApplyBo bo, Page<OrderRefund> page) {
        if (ObjectUtil.equals(TenantType.Distributor, LoginHelper.getTenantTypeEnum())) {
            return baseMapper.queryPageList(bo, page);
        }
        return TenantHelper.ignore(() -> baseMapper.queryPageList(bo, page));
    }

    /**
     * 查询售后申请主单列表
     */
    public List<RefundApplyVo> queryList(RefundApplyBo bo) {
        if (ObjectUtil.equals(TenantType.Distributor.name(), bo.getTenantType())) {
            return baseMapper.queryPageList(bo);
        }
        return TenantHelper.ignore(() -> baseMapper.queryPageList(bo));
    }

    private LambdaQueryWrapper<OrderRefund> buildQueryWrapper(OrderRefundBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrderRefund> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getSupplierTenantId()), OrderRefund::getSupplierTenantId, bo.getSupplierTenantId());
        lqw.eq(bo.getOrderId() != null, OrderRefund::getOrderId, bo.getOrderId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), OrderRefund::getOrderNo, bo.getOrderNo());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderRefundNo()), OrderRefund::getOrderRefundNo, bo.getOrderRefundNo());
        lqw.eq(bo.getOriginalRefundAmount() != null, OrderRefund::getOriginalRefundAmount, bo.getOriginalRefundAmount());
        lqw.eq(bo.getPlatformRefundAmount() != null, OrderRefund::getPlatformRefundAmount, bo.getPlatformRefundAmount());
        lqw.eq(bo.getRefundApplyTime() != null, OrderRefund::getRefundApplyTime, bo.getRefundApplyTime());
        lqw.eq(StringUtils.isNotBlank(bo.getRefundType()), OrderRefund::getRefundType, bo.getRefundType());
        lqw.eq(bo.getRefundQuantity() != null, OrderRefund::getRefundQuantity, bo.getRefundQuantity());
        lqw.eq(StringUtils.isNotBlank(bo.getRefundRuleNo()), OrderRefund::getRefundRuleNo, bo.getRefundRuleNo());
        lqw.eq(StringUtils.isNotBlank(bo.getRefundRuleReason()), OrderRefund::getRefundRuleReason, bo.getRefundRuleReason());
        lqw.eq(StringUtils.isNotBlank(bo.getRefundDescription()), OrderRefund::getRefundDescription, bo.getRefundDescription());
        lqw.eq(StringUtils.isNotBlank(bo.getRefundAmountState()), OrderRefund::getRefundAmountState, bo.getRefundAmountState());
        lqw.eq(StringUtils.isNotBlank(bo.getRefundState()), OrderRefund::getRefundState, bo.getRefundState());
        lqw.eq(bo.getRefundCompletionTime() != null, OrderRefund::getRefundCompletionTime, bo.getRefundCompletionTime());
        lqw.eq(StringUtils.isNotBlank(bo.getRefundDisputeState()), OrderRefund::getRefundDisputeState, bo.getRefundDisputeState());
        lqw.eq(StringUtils.isNotBlank(bo.getMessagingAppId()), OrderRefund::getMessagingAppId, bo.getMessagingAppId());
        lqw.eq(StringUtils.isNotBlank(bo.getMessagingAppType()), OrderRefund::getMessagingAppType, bo.getMessagingAppType());
        lqw.eq(StringUtils.isNotBlank(bo.getManagerUserId()), OrderRefund::getManagerUserId, bo.getManagerUserId());
        lqw.eq(bo.getManagerReviewTime() != null, OrderRefund::getManagerReviewTime, bo.getManagerReviewTime());
        lqw.eq(StringUtils.isNotBlank(bo.getManagerReviewOpinion()), OrderRefund::getManagerReviewOpinion, bo.getManagerReviewOpinion());
        lqw.eq(StringUtils.isNotBlank(bo.getSupplierUserId()), OrderRefund::getSupplierUserId, bo.getSupplierUserId());
        lqw.eq(bo.getSupplierReviewTime() != null, OrderRefund::getSupplierReviewTime, bo.getSupplierReviewTime());
        lqw.eq(StringUtils.isNotBlank(bo.getSupplierReviewOpinion()), OrderRefund::getSupplierReviewOpinion, bo.getSupplierReviewOpinion());
        return lqw;
    }

    /**
     * 新增售后申请主单
     */
    public Boolean insertByBo(OrderRefundBo bo) {
        OrderRefund add = MapstructUtils.convert(bo, OrderRefund.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 保存或修改售后单操作（无租户）
     * @param orderRefund
     * @return
     */
    public Boolean saveByNoTenant(OrderRefund orderRefund) {
        return TenantHelper.ignore(() -> this.saveOrUpdate(orderRefund));
    }

    /**
     * 修改售后申请主单
     */
    public Boolean updateByBo(OrderRefundBo bo) {
        OrderRefund update = MapstructUtils.convert(bo, OrderRefund.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrderRefund entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除售后申请主单
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    public boolean existsOrderRefundNo(String orderRefundNo) {
        return baseMapper.existsOrderRefundNo(orderRefundNo);
    }

    /**
     * 获取正在审核和售后的售后主单集合
     *
     * @param ids
     * @return
     */
    public List<OrderRefund> queryListByIds(List<Long> ids) {
        LambdaQueryWrapper<OrderRefund> lqw = Wrappers.lambdaQuery();
        lqw.in(OrderRefund::getId, ids)
            .in(OrderRefund::getRefundState, "Verifying", "Refunding")
            .orderByDesc(OrderRefund::getCreateTime);
        if (ObjectUtil.equals(LoginHelper.getTenantTypeEnum(), TenantType.Distributor)) {
            return baseMapper.selectList(lqw);
        } else {
            return TenantHelper.ignore(() -> baseMapper.selectList(lqw));
        }
    }

    /**
     * 通过售后单编号获取售后单信息
     * @return
     */
    public OrderRefund queryByRefundNo(String refundNo) {
        LambdaQueryWrapper<OrderRefund> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderRefund::getOrderRefundNo, refundNo);
        if (ObjectUtil.equals(LoginHelper.getTenantTypeEnum(), TenantType.Distributor)) {
            return baseMapper.selectOne(lqw);
        } else {
            return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
        }
    }

    /**
     * 根据orderId获取供应商退款总数
     * @param orderId
     * @param isSupplier
     * @return
     */
    public BigDecimal sumByRefundPrice(Long orderId, Boolean isSupplier) {
        return TenantHelper.ignore(() ->baseMapper.sumByRefundPrice(orderId, isSupplier));
    }


    /**
     * 根据订单ID判断是否存在进行中的退款单
     * @param orderId
     * @return
     */
    public Integer countByInProgress(Long orderId) {
        if (ObjectUtil.equals(LoginHelper.getTenantTypeEnum(), TenantType.Distributor)) {
            return baseMapper.countByInProgress(orderId);
        }
        return TenantHelper.ignore(() -> baseMapper.countByInProgress(orderId));
    }

    @InMethodLog("根据子订单和退款单状态查询退款单")
    public List<OrderRefund> queryByOrderItemAndState(Long orderItemId, OrderRefundStateType refundState) {
        return baseMapper.queryByOrderItemAndState(orderItemId, refundState.name());
    }

    @InMethodLog("根据主订单和退款单状态查询退款单")
    public List<OrderRefund> queryByOrderAndState(Long orderId, OrderRefundStateType refundState) {
        LambdaQueryWrapper<OrderRefund> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderRefund::getOrderId, orderId);
        lqw.eq(OrderRefund::getRefundState, refundState);
        return TenantHelper.ignore(() -> baseMapper.selectList(lqw), TenantType.Manager, TenantType.Supplier);
    }

    /**
     * 统计平台退款金额
     * @param startDate
     * @param endDate
     * @return
     */
    @InMethodLog("统计平台退款金额")
    public BigDecimal sumPlatformRefundAmount(Date startDate, Date endDate) {
        return TenantHelper.ignore(() -> baseMapper.sumPlatformRefundAmount(startDate, endDate));
    }

    @InMethodLog("统计平台退款金额")
    public List<OrderRefund> queryOrderRefundByRefunded(Long orderId, Long excludeRefundId, String refundState) {
        return TenantHelper.ignore(() -> baseMapper.queryOrderRefundByRefunded(orderId, excludeRefundId, refundState));
    }

    /**
     * 功能描述：按订单号查询
     *
     * @param orderNos 订单编号
     * @return {@link List }<{@link String }>
     * <AUTHOR>
     * @date 2025/03/14
     */
    public List<String> queryByOrderNo(List<String> orderNos) {
        LambdaQueryWrapper<OrderRefund> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrderRefund::getOrderNo,orderNos);
        List<OrderRefund> orderRefunds = baseMapper.selectList(queryWrapper);
        if(CollUtil.isEmpty(orderRefunds)){
            throw new RuntimeException("数据异常");
        }
        List<String> orderRefundNos = orderRefunds.stream().map(OrderRefund::getOrderRefundNo).collect(Collectors.toList());
        return orderRefundNos;
    }

    /**
     * 功能描述：按退款编号查询订单
     *
     * @param orderRefundNo 订单退款编号
     * @return {@link Orders }
     * <AUTHOR>
     * @date 2025/03/27
     */
    public Orders queryOrderByRefundNo(String orderRefundNo) {

        return baseMapper.queryOrderByRefundNo(orderRefundNo);
    }

    public List<OrderRefund> listByOrderNoNoTenant(List<String> orderNos) {
        LambdaQueryWrapper<OrderRefund> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrderRefund::getOrderNo,orderNos);
        List<OrderRefund> orderRefunds = TenantHelper.ignore(()->baseMapper.selectList(queryWrapper));
        return orderRefunds;
    }
}
