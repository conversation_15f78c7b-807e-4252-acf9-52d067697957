package com.zsmall.order.entity.iservice;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.orderImportRecord.OrderTempState;
import com.zsmall.order.entity.domain.OrderImportTemp;
import com.zsmall.order.entity.mapper.OrderImportTempMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 订单导入临时表Service数据库层处理
 *
 * <AUTHOR>
 * @date 2023-06-09
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class IOrderImportTempService extends ServiceImpl<OrderImportTempMapper, OrderImportTemp> {

    private final OrderImportTempMapper baseMapper;

    /**
     * 查询渠道订单号是否重复（租户维度查重）
     * @param channelOrderNo
     * @param
     * @return
     */
    public boolean existsChannelOrderNo(String channelOrderNo) {
        log.info("进入【查询渠道订单号是否重复（租户维度查重）】 channelOrderNo = {}", channelOrderNo);
        LambdaQueryWrapper<OrderImportTemp> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderImportTemp::getStoreOrderId, channelOrderNo);
        lqw.in(OrderImportTemp::getTempState, OrderTempState.Temporary, OrderTempState.Official);
        return baseMapper.exists(lqw);
    }

    /**
     * 查询渠道订单号和临时订单状态是否重复（租户维度查重）
     * @param channelOrderNo
     * @param orderTempState
     * @return
     */
    public boolean existsChannelOrderNoAndTempState(String channelOrderNo,OrderTempState orderTempState) {
        log.info("进入【查询渠道订单号是否重复（租户维度查重）】 channelOrderNo = {}", channelOrderNo);
        LambdaQueryWrapper<OrderImportTemp> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderImportTemp::getStoreOrderId, channelOrderNo);
        lqw.eq(OrderImportTemp::getTempState,orderTempState);
        return baseMapper.exists(lqw);
    }

    /**
     * 查询渠道订单号是否重复（租户维度查重，排除某条记录）
     * @param channelOrderNo
     * @param selfId
     * @return
     */
    public boolean existsChannelOrderNoExcludeSelf(String channelOrderNo, Long selfId) {
        log.info("进入【查询渠道订单号是否重复（租户维度查重）】 channelOrderNo = {}", channelOrderNo);
        LambdaQueryWrapper<OrderImportTemp> lqw = Wrappers.lambdaQuery();
        lqw.ne(OrderImportTemp::getId, selfId);
        lqw.eq(OrderImportTemp::getStoreOrderId, channelOrderNo);
        lqw.in(OrderImportTemp::getTempState, OrderTempState.Temporary, OrderTempState.Official);
        return baseMapper.exists(lqw);
    }

    /**
     * 查询临时订单号是否重复
     * @param tempOrderNo
     * @return
     */
    public boolean existsTempOrderNo(String tempOrderNo) {
        log.info("进入【查询临时订单号是否重复】 tempOrderNo = {}", tempOrderNo);
        LambdaQueryWrapper<OrderImportTemp> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderImportTemp::getTempOrderNo, tempOrderNo);
        return TenantHelper.ignore(() -> baseMapper.exists(lqw));
    }

    /**
     * 根据临时单号查询
     * @param tempOrderNo
     * @return
     */
    public OrderImportTemp queryByTempOrderNoAndTempState(String tempOrderNo, OrderTempState tempState) {
        log.info("进入【查询临时订单号是否重复】 tempOrderNo = {}", tempOrderNo);
        LambdaQueryWrapper<OrderImportTemp> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderImportTemp::getTempOrderNo, tempOrderNo);
        lqw.eq(OrderImportTemp::getTempState, tempState);
        return getOne(lqw);
    }

    /**
     * 根据第三方店铺单号查询
     * @param storeOrderId
     * @return
     */
    public OrderImportTemp queryByStoreOrderIdAndTempState(Long recordId, String storeOrderId, OrderTempState tempState) {
        log.info("进入【根据第三方店铺单号查询】 storeOrderId = {}", storeOrderId);
        LambdaQueryWrapper<OrderImportTemp> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderImportTemp::getRecordId, recordId);
        lqw.eq(OrderImportTemp::getStoreOrderId, storeOrderId);
        lqw.eq(OrderImportTemp::getTempState, tempState);
        return getOne(lqw);
    }

    /**
     * 根据临时订单号集合查询了临时订单列表
     * @param tempOrderNoList
     * @return
     */
    public List<OrderImportTemp> queryPendingByTempOrderNoList(List<String> tempOrderNoList) {
        log.info("进入【根据临时订单号集合查询了临时订单列表】方法，tempOrderNoList = {} ", JSONUtil.toJsonStr(tempOrderNoList));
        LambdaQueryWrapper<OrderImportTemp> lqw = new LambdaQueryWrapper<>();
        lqw.in(OrderImportTemp::getTempOrderNo, tempOrderNoList);
        lqw.eq(OrderImportTemp::getTempState, OrderTempState.Temporary);
        return this.list(lqw);
    }

    /**
     * 根据记录id和临时单状态查询
     * @param recordId
     * @param tempState
     * @return
     */
    public List<OrderImportTemp> queryByRecordIdAndTempState(Long recordId, OrderTempState tempState) {
        log.info("进入【根据记录id和临时单状态查询】方法，recordId = {}, tempState = {}", recordId, tempState);
        LambdaQueryWrapper<OrderImportTemp> lqw = new LambdaQueryWrapper<>();
        lqw.in(OrderImportTemp::getRecordId, recordId);
        lqw.eq(OrderImportTemp::getTempState, tempState);
        return this.list(lqw);
    }

    public void updateSetNull(OrderImportTemp tempOrder) {
        LambdaUpdateWrapper<OrderImportTemp> lqw = new LambdaUpdateWrapper<>();
        lqw.eq(OrderImportTemp::getId, tempOrder.getId());
        lqw.set(OrderImportTemp::getShippingFee, null);
        lqw.set(OrderImportTemp::getShippingTotalAmount, null);
        lqw.set(OrderImportTemp::getWarehouseSystemCode, null);
        lqw.set(OrderImportTemp::getDropShippingPrice, null);
        lqw.set(OrderImportTemp::getDropShippingTotalAmount, null);
        update(null, lqw);
    }

    /**
     * 功能描述：更新批处理设置为空
     *
     * @param orderExtendId 订单扩展id
     * <AUTHOR>
     * @date 2025/05/22
     */
    public void updateBatchSetNull(String orderExtendId) {
        if (ObjectUtil.isEmpty(orderExtendId)) return;

        LambdaUpdateWrapper<OrderImportTemp> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(OrderImportTemp::getOrderExtendId, orderExtendId)
               .apply(null); // 确保WHERE条件存在

        // 批量设置字段为null
        NULLABLE_FIELDS.forEach(field ->
            wrapper.set(field, null)
        );

        update(null, wrapper);
    }

    private static final List<SFunction<OrderImportTemp, ?>> NULLABLE_FIELDS = Arrays.asList(
        OrderImportTemp::getShippingFee,
        OrderImportTemp::getShippingTotalAmount,
        OrderImportTemp::getWarehouseSystemCode,
        OrderImportTemp::getDropShippingPrice,
        OrderImportTemp::getDropShippingTotalAmount
    );
    public List<OrderImportTemp> queryByOrderExtendId(String orderExtendId) {
        log.info("进入【根据行id查询】方法，orderExtendId = {}", orderExtendId);
        LambdaQueryWrapper<OrderImportTemp> lqw = new LambdaQueryWrapper<>();
        lqw.in(OrderImportTemp::getOrderExtendId, orderExtendId);

        return this.list(lqw);
    }


}
