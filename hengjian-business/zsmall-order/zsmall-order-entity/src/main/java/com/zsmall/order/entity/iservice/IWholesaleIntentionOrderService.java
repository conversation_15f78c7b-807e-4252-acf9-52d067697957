package com.zsmall.order.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.order.entity.domain.WholesaleIntentionOrder;
import com.zsmall.order.entity.domain.dto.WIOrderPageDTO;
import com.zsmall.order.entity.mapper.WholesaleIntentionOrderMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【wholesale_intention_order(国外现货批发意向订单)】的数据库操作Service实现
 * @createDate 2023-07-24 10:46:40
 */
@Service
public class IWholesaleIntentionOrderService extends ServiceImpl<WholesaleIntentionOrderMapper, WholesaleIntentionOrder> {


    public Page<WholesaleIntentionOrder> queryPage(WIOrderPageDTO wiOrderPageDTO, Page<WholesaleIntentionOrder> page) {
        if (TenantType.Distributor.equals(LoginHelper.getTenantTypeEnum())) {
            return baseMapper.queryPage(wiOrderPageDTO, page);
        } else {
            return TenantHelper.ignore((() -> baseMapper.queryPage(wiOrderPageDTO, page)));
        }
    }

    public WholesaleIntentionOrder queryByWIOrderNo(String wiOrderNo) {
        LambdaQueryChainWrapper<WholesaleIntentionOrder> lambdaQueryChainWrapper = lambdaQuery()
            .eq(WholesaleIntentionOrder::getWholesaleIntentionOrderNo, wiOrderNo);
        if (TenantType.Distributor.equals(LoginHelper.getTenantTypeEnum())) {
            return lambdaQueryChainWrapper.one();
        } else {
            return TenantHelper.ignore(() -> lambdaQueryChainWrapper.one());
        }
    }

    @InMethodLog(value = "根据正式订单编号查询")
    public WholesaleIntentionOrder queryByOrderNo(String orderNo) {
        return TenantHelper.ignore(() -> lambdaQuery().eq(WholesaleIntentionOrder::getOrderNo, orderNo).one(), TenantType.Manager, TenantType.Supplier);
    }

    public boolean existsWholesaleIntentionOrderNo(String wholesaleIntentionOrderNo) {
        return baseMapper.existsWholesaleIntentionOrderNo(wholesaleIntentionOrderNo);
    }

    public WholesaleIntentionOrder queryByTransactionId(Long transactionsId) {
        LambdaQueryWrapper<WholesaleIntentionOrder> lqw = new LambdaQueryWrapper<>();
        lqw.eq(WholesaleIntentionOrder::getDelFlag, 0);
        lqw.exists("select 1 from transactions_wholesale_intention_order " +
            "where transactions_wholesale_intention_order.wholesale_intention_order_id = wholesale_intention_order.id " +
            "and transactions_wholesale_intention_order.transactions_id = " + transactionsId);
        return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
    }
}




