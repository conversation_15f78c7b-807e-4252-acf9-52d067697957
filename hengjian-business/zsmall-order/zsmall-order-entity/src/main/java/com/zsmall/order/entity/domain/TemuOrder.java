package com.zsmall.order.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.domain.dto.TemuOrderItemDTO;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/6/25 16:32
 */
@Data
@TableName("temu_order")
public class TemuOrder {

    private String accountId;
    private String orderNum;

    private BigDecimal amount;
    private String currencyCode;

    private String status;
    private String shippingStatus;
    private String buyName;

    private String fulfillmentChannel;
    private String shippingName;

    private String addressline1;

    private String addressline2;

    private String city;
    private String stateOrRegion;

    private String country;
    private String countryCode;

    private String postalCode;

    private String phone;

    private String created;

    private LocalDateTime createTime;

    private String isBusinessOrder;

    private String platformShipTime;

    private String latestShipDate;

//    private List<TemuOrderItemDTO> items;
    private String tenantId;

    private String logisticsType;

    /**
     * 订单类型 1:有地址的订单 2:没有地址的订单 0:未知
     */
    private Integer orderType;

    /**
     * 异常code 0:无异常 1:商品映射异常 2:订单支付异常 3:库存不足异常
     */
    private Integer exceptionCode;

    /**
     * 渠道 id
     */
    private Long channelId;

    /**
     * 渠道类型
     */
    private ChannelTypeEnum channelType;
}
