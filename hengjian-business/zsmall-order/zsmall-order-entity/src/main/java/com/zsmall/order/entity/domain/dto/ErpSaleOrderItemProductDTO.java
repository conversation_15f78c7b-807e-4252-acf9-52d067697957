package com.zsmall.order.entity.domain.dto;

import com.zsmall.common.enums.BooleanEnum;
import com.zsmall.common.enums.order.B2cIsShipEnum;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.Transient;
import java.math.BigDecimal;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2023/12/13 17:49
 */
public class ErpSaleOrderItemProductDTO {
    /**
     * 组织id
     */
    @Column(name = "org_id")
    private Integer orgId;
    /**
     * 产品线(项目)
     */
    @Column(name = "line_id")
    private Integer lineId;
    /**
     * 订单ID
     */
    @Column(name = "head_id")
    private Integer headId;
    /**
     * 订单号
     */
    @Column(name = "order_no")
    private String orderNo;
    /**
     * 订单行的ID
     */
    @Column(name = "head_item_id")
    private Integer headItemId;
    /**
     * 是否需要发货,0自动;1手动
     */
    @Column(name = "is_ship")
    private B2cIsShipEnum isShip;
    /**
     * seller_SKU
     */
    @Column(name = "seller_sku")
    private String sellerSku;
    /**
     * 产品ID
     */
    @Column(name = "product_id")
    private Integer productId;
    /**
     * erp_sku
     */
    @Column(name = "erp_sku")
    private String erpSku;
    /**
     * 商品名称
     */
    @Column(name = "name_en")
    private String nameEn;
    /**
     * 产品数量
     */
    @Column(name = "quantity")
    private Integer quantity;
    /**
     * 单包数量
     */
    @Column(name = "single_package_quantity")
    private Integer singlePackageQuantity;
    /**
     * 已发数量
     */
    @Column(name = "shiped_quantity")
    private Integer shipedQuantity;
    /**
     * 追踪号
     */
    @Column(name = "track_no")
    private String trackNo;
    /**
     * 行总额
     */
    @Column(name = "item_amount")
    private BigDecimal itemAmount;
    /**
     * 产品投保金额（单价）
     */
    @Column(name = "insurance_amount")
    private BigDecimal insuranceAmount;
    /**
     * 行币种
     */
    @Column(name = "currency_code")
    private String currencyCode;
    /**
     * 产品重量
     */
    @Column(name = "weight")
    private BigDecimal weight;
    /**
     * 产品长度
     */
    @Column(name = "length")
    private BigDecimal length;
    /**
     * 产品宽度
     */
    @Column(name = "width")
    private BigDecimal width;
    /**
     * 产品高度
     */
    @Column(name = "height")
    private BigDecimal height;
    /**
     * 重量单位，默认g
     */
    @Column(name = "weight_unit")
    private String weightUnit;
    /**
     * 长度单位，默认cm
     */
    @Column(name = "length_unit")
    private String lengthUnit;
    /**
     * 产品税
     */
    @Column(name = "tax_price")
    private BigDecimal taxPrice;
    /**
     * 处理费用
     */
    @Column(name = "process_price")
    private BigDecimal processPrice;
    /**
     * 禁用时间戳
     */
    @Column(name = "disabled_at")
    @Accessors(chain = false)
    private Integer disabledAt;
    /**
     * 禁用者id
     */
    @Column(name = "disabled_by")
    @Accessors(chain = false)
    private Integer disabledBy;
    /**
     * 禁用者名称
     */
    @Column(name = "disabled_name")
    @Accessors(chain = false)
    private String disabledName;

    @ManyToOne(
        fetch = FetchType.LAZY
        , optional = true
    )

    private ErpSaleOrderDTO saleOrderEntity;


    private ErpSaleOrderItemDTO saleOrderItemEntity;

    @Transient
    private BooleanEnum isInsurance;

    @Transient
    private Integer orgWarehouseId;
}
