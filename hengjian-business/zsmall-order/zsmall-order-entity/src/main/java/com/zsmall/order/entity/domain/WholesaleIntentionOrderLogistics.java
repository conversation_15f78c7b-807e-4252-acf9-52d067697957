package com.zsmall.order.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zsmall.common.domain.SortEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 国外现货批发意向订单物流信息表
 * @TableName wholesale_intention_order_logistics
 */
@TableName(value ="wholesale_intention_order_logistics")
@Data
@EqualsAndHashCode(callSuper=false)
public class WholesaleIntentionOrderLogistics extends SortEntity {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 国外现货批发意向订单主键
     */
    private Long wholesaleIntentionOrderId;

    /**
     * 发货方式
     */
    private String deliveryType;

    /**
     * 预约提货时间
     */
    private String appointmentTime;

    /**
     * 物流模板编号
     */
    private String logisticsTemplateNo;

    /**
     * 仓库系统编号
     */
    private String warehouseSystemCode;

    /**
     * 仓库地址-国家
     */
    private String warehouseCountry;

    /**
     * 仓库地址-国家Code
     */
    private String warehouseCountryCode;

    /**
     * 仓库地址-州/省
     */
    private String warehouseState;

    /**
     * 仓库地址-州/省ID
     */
    private String warehouseStateCode;

    /**
     * 仓库地址-城市
     */
    private String warehouseCity;

    /**
     * 仓库地址-详细地址1
     */
    private String warehouseAddress1;

    /**
     * 仓库地址-详细地址2
     */
    private String warehouseAddress2;

    /**
     * 仓库地址-邮编
     */
    private String warehouseZipCode;

    /**
     * 仓库联系人
     */
    private String warehouseContact;

    /**
     * 仓库电话
     */
    private String warehousePhone;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
