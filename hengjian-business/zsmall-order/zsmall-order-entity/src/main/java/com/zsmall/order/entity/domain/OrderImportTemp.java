package com.zsmall.order.entity.domain;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.orderImportRecord.OrderTempState;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
 * 订单导入临时表对象 order_import_temp
 *
 * <AUTHOR>
 * @date 2023-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "order_import_temp", autoResultMap = true)
public class OrderImportTemp extends NoDeptTenantEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 订单记录表Id
     */
    private Long recordId;

    /**
     * 正式订单编号
     */
    private String orderNo;

    /**
     * 临时订单编号
     */
    private String tempOrderNo;

    /**
     * 商品名
     */
    private String productName;

    /**
     * 商品SKU唯一编号（ItemNo.）
     */
    private String productSkuCode;
    /**
     * 站点id
     */
    private Long siteId;
    /**
     * 货币代码
     */
    private String currencyCode;

    /**
     * 货币符号
     */
    private String currencySymbol;

    /**
     * 商品图片展示地址
     */
    private String productImageShowUrl;

    /**
     * 商品订购数量
     */
    private Integer productQuantity;

    /**
     * 活动编号
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String activityCode;

    /**
     * 订单商品自提单价
     */
    private BigDecimal pickUpPrice;

    /**
     * 订单商品自提总价
     */
    private BigDecimal pickUpTotalAmount;

    /**
     * 订单商品代发单价
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal dropShippingPrice;

    /**
     * 订单商品代发总价
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal dropShippingTotalAmount;

    /**
     * 运费
     */
    private BigDecimal shippingFee;

    /**
     * 尾程派送费总价
     */
    private BigDecimal shippingTotalAmount;

    /**
     * 国家的缩写
     */
    private String countryCode;

    /**
     * 洲的缩写
     */
    private String stateCode;

    /**
     * 城市
     */
    private String city;

    /**
     * 地址1
     */
    private String address1;

    /**
     * 地址2
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String address2;

    private String address3;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 邮编
     */
    private String zipCode;

    /**
     * 收件人名称
     */
    private String recipientName;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * 物流类型（PickUp-自提，DropShipping-代发商品）
     */
    private LogisticsTypeEnum logisticsType;

    /**
     * 第三方物流商（UPS，FedEx）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String logisticsCarrier;

    private String logisticsCarrierCode;
    /**
     * 物流服务名称
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String logisticsServiceName;

    /**
     * 跟踪单号（JSON数组）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED, typeHandler = JacksonTypeHandler.class)
    private JSONArray logisticsTrackingNo;

    /**
     * 是否选择第三方物流
     */
    private Boolean logisticsThirdBilling;

    /**
     * 第三方发货商账号
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String logisticsAccount;

    /**
     * 第三方发货商账号邮编
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String logisticsAccountZipCode;

    /**
     * 仓库唯一系统编号
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String warehouseSystemCode;

    /**
     * 运输标签存储对象主键
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long shippingLabelOssId;

    /**
     * 运输标签源文件名
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String shippingLabelFileName;

    /**
     * 渠道商店名称
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String storeName;

    /**
     * 渠道商店订单编号
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String storeOrderId;

    /**
     * 临时订单状态：0-待确认，1-已转正式单，2-已取消
     */
    private OrderTempState tempState;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 订单来源 1:接口接入 2:excel导入 3:商城下单 4:openApi
     */
    private Integer orderSource;

    /**
     * 订单扩展id
     */
    private String orderExtendId;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long bolOssId;

    @TableField(typeHandler = JacksonTypeHandler.class,updateStrategy = FieldStrategy.IGNORED)
    private JSONObject cartonLabelOssIds;

    @TableField(typeHandler = JacksonTypeHandler.class,updateStrategy = FieldStrategy.IGNORED)
    private JSONObject palletLabelOssIds;

    @TableField(typeHandler = JacksonTypeHandler.class,updateStrategy = FieldStrategy.IGNORED)
    private JSONObject itemLabelOssIds;


    /**
     * 其他oss-id,文件名称需要从oss表内拿
     */
    @TableField(typeHandler = JacksonTypeHandler.class,updateStrategy = FieldStrategy.IGNORED)
    private JSONObject otherOssIds;
}
