package com.zsmall.order.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.order.entity.domain.OrderRefundAttachment;
import com.zsmall.order.entity.mapper.OrderRefundAttachmentMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 售后申请主单附件Service接口
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@RequiredArgsConstructor
@Service
public class IOrderRefundAttachmentService extends ServiceImpl<OrderRefundAttachmentMapper, OrderRefundAttachment> {



    public List<OrderRefundAttachment> queryListByRefundNo(String orderRefundNo) {
        LambdaQueryWrapper<OrderRefundAttachment> lqw = new LambdaQueryWrapper<>();
        lqw.eq(OrderRefundAttachment::getOrderRefundNo, orderRefundNo);
        return baseMapper.selectList(lqw);
    }

}
