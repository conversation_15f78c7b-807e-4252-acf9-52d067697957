package com.zsmall.order.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.order.entity.domain.Tracking17Carrier;
import com.zsmall.order.entity.mapper.Tracking17CarrierMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 17Track承运商表-数据库层
 *
 * <AUTHOR>
 * @date 2023/6/8
 */

@RequiredArgsConstructor
@Service
@Slf4j
public class ITracking17CarrierService extends ServiceImpl<Tracking17CarrierMapper, Tracking17Carrier> {

    private final Tracking17CarrierMapper tracking17CarrierMapper;

    /**
     * 根据承运商名称查询
     * @param carrierName
     * @return
     */
    public Tracking17Carrier queryByCarrierName(String carrierName) {
        log.info("进入【根据承运商名称查询】 carrierCode = {}", carrierName);
        // 重名只取一个
        List<Tracking17Carrier> list = lambdaQuery().eq(Tracking17Carrier::getCarrierName, carrierName).list();
        return CollUtil.isNotEmpty(list) ? list.get(0) : null;
    }

    /**
     * 根据承运商Code查询
     * @param carrierCode
     * @return
     */
    public Tracking17Carrier queryByCarrierCode(Integer carrierCode) {
        log.info("进入【根据承运商Code查询】 carrierCode = {}", carrierCode);
        return lambdaQuery().eq(Tracking17Carrier::getCarrierKey, carrierCode).one();
    }

    /**
     * 查询列表
     * @return
     */
    public List<Tracking17Carrier> queryList() {
        log.info("进入【查询列表】");
        return lambdaQuery().orderByAsc(Tracking17Carrier::getId).list();
    }

    /**
     * 查询列表
     * @return
     */
    public List<Tracking17Carrier> queryListByGroup(String carrierGroup) {
        log.info("进入【查询列表】");
        return lambdaQuery().eq(StrUtil.isNotBlank(carrierGroup), Tracking17Carrier::getCarrierGroup, carrierGroup).orderByAsc(Tracking17Carrier::getId).list();
    }

    /**
     * 查询列表
     * @return
     */
    public List<Tracking17Carrier> queryList(String queryValue) {
        log.info("进入【查询列表】");
        return lambdaQuery().like(StrUtil.isNotBlank(queryValue), Tracking17Carrier::getCarrierName, queryValue).orderByAsc(Tracking17Carrier::getId).list();
    }


}
