package com.zsmall.order.entity.domain.event;

import com.zsmall.order.entity.domain.OrderItemPrice;
import com.zsmall.order.entity.domain.Orders;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 重新计算订单总金额事件
 *
 * <AUTHOR>
 * @date 2023/6/27
 */
@Getter
@Setter
public class RecalculateOrderAmountEvent {

    private Orders order;

    private List<OrderItemPrice> orderItemPriceList;

}
