package com.zsmall.order.entity.domain.dto;

import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.OrderItemPrice;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * 订单价格计算DTO
 * 用于价格计算方法传参
 *
 * <AUTHOR>
 * @create 2021/8/24 16:33
 */
@Data
@Accessors(chain = true)
public class OrderPriceCalculateDTO {

    /**
     * 子订单
     */
    private OrderItem orderItem;

    private Long siteId;
    /**
     * 子订单价格
     */
    private OrderItemPrice orderItemPrice;

    /**
     * 渠道类型
     */
    private ChannelTypeEnum channelTypeEnum;

    /**
     * 物流类型
     */
    private LogisticsTypeEnum logisticsType;

    /**
     * 国家，计算运费使用，除了US之外的国家，不需要计算运费
     */
    private String country;

    /**
     * 物流邮编，计算运费使用（若是美国xxxxx-xxxx格式的邮编，只取前面五位传入）
     */
    private String logisticsZipCode;

    /**
     * 第三方物流
     */
    private Boolean logisticsThirdBilling;

    /**
     * 物流公司
     */
    private String logisticsCompany;

    /**
     * 物流承运人代码
     */
    private String logisticsCarrierCode;

    /**
     * 物流服务代码
     */
    private String logisticsCode;

    /**
     * 指定仓库
     */
    private String warehouseSystemCode;

    /**
     * 仓库代码
     */
    private String warehouseCode;
    /**
     * 活动编号
     */
    private String activityCode;

    public OrderPriceCalculateDTO() {
    }

    public OrderPriceCalculateDTO(String logisticsZipCode, String activityCode) {
        this.logisticsZipCode = logisticsZipCode;
        this.activityCode = activityCode;
    }

    public OrderPriceCalculateDTO(LogisticsTypeEnum logisticsType, String logisticsZipCode) {
        this.logisticsType = logisticsType;
        this.logisticsZipCode = logisticsZipCode;
    }

    public OrderPriceCalculateDTO(LogisticsTypeEnum logisticsType, String logisticsZipCode, String warehouseSystemCode) {
        this.logisticsType = logisticsType;
        this.logisticsZipCode = logisticsZipCode;
        this.warehouseSystemCode = warehouseSystemCode;
    }

    public OrderPriceCalculateDTO(LogisticsTypeEnum logisticsType, String logisticsZipCode, String warehouseSystemCode, Boolean logisticsThirdBilling) {
        this.logisticsType = logisticsType;
        this.logisticsZipCode = logisticsZipCode;
        this.warehouseSystemCode = warehouseSystemCode;
        this.logisticsThirdBilling = logisticsThirdBilling;
    }

    public OrderPriceCalculateDTO(LogisticsTypeEnum logisticsType, String country, String logisticsZipCode, String warehouseSystemCode, Boolean logisticsThirdBilling) {
        this.country = country;
        this.logisticsType = logisticsType;
        this.logisticsZipCode = logisticsZipCode;
        this.warehouseSystemCode = warehouseSystemCode;
        this.logisticsThirdBilling = logisticsThirdBilling;
    }

    public OrderPriceCalculateDTO(LogisticsTypeEnum logisticsType, String country, String logisticsZipCode, String warehouseSystemCode, Boolean logisticsThirdBilling, String activityCode) {
        this.country = country;
        this.logisticsType = logisticsType;
        this.logisticsZipCode = logisticsZipCode;
        this.warehouseSystemCode = warehouseSystemCode;
        this.logisticsThirdBilling = logisticsThirdBilling;
        this.activityCode = activityCode;
    }

    public static OrderPriceCalculateDTO newDTO(LogisticsTypeEnum logisticsType, String logisticsZipCode) {
        return new OrderPriceCalculateDTO(logisticsType, logisticsZipCode);
    }

    public static OrderPriceCalculateDTO newDTO(LogisticsTypeEnum logisticsType, String logisticsZipCode, String warehouseSystemCode) {
        return new OrderPriceCalculateDTO(logisticsType, logisticsZipCode, warehouseSystemCode);
    }

    public static OrderPriceCalculateDTO newDTO(LogisticsTypeEnum logisticsType, String logisticsZipCode, String warehouseSystemCode, Boolean logisticsThirdBilling) {
        return new OrderPriceCalculateDTO(logisticsType, logisticsZipCode, warehouseSystemCode, logisticsThirdBilling);
    }

    public static OrderPriceCalculateDTO newDTO(LogisticsTypeEnum logisticsType, String country, String logisticsZipCode, String warehouseSystemCode, Boolean logisticsThirdBilling) {
        return new OrderPriceCalculateDTO(logisticsType, country, logisticsZipCode, warehouseSystemCode, logisticsThirdBilling);
    }

    public static OrderPriceCalculateDTO newDTO(LogisticsTypeEnum logisticsType, String country, String logisticsZipCode, String warehouseSystemCode, Boolean logisticsThirdBilling, String activityCode) {
        return new OrderPriceCalculateDTO(logisticsType, country, logisticsZipCode, warehouseSystemCode, logisticsThirdBilling, activityCode);
    }

    public static OrderPriceCalculateDTO newDTO(String logisticsZipCode, String activityCode) {
        return new OrderPriceCalculateDTO(logisticsZipCode, activityCode);
    }

}
