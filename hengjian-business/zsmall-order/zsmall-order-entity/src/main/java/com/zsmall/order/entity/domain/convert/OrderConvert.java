package com.zsmall.order.entity.domain.convert;


import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.OrderItemTrackingRecord;
import com.zsmall.order.entity.domain.vo.OrderItemVo;
import com.zsmall.order.entity.domain.vo.order.OrderPageVo;
import com.zsmall.order.entity.domain.vo.order.OrderPageVoOpenAPI;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;


@Mapper
public interface OrderConvert {
    OrderConvert INSTANCE = Mappers.getMapper(OrderConvert.class);
    Map<Long, OrderItemVo> convertOrderTOOrderItemVo(Map<Long, OrderItem> billAdminExports);
    List<OrderPageVoOpenAPI> convertOrderToOrderPageVoOpenAPI(List<OrderPageVo> orderPageVoList);
    List<OrderPageVoOpenAPI.OrderOpenAPITrackingRecord> convertTrackingRecordToOpenAPITrackingRecord(List<OrderItemTrackingRecord> orderPageVoList);

}
