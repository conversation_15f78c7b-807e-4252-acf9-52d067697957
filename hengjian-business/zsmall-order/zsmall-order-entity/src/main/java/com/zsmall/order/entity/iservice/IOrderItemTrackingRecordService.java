package com.zsmall.order.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.LogisticsProgress;
import com.zsmall.common.enums.order.OrderStateType;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.order.entity.domain.OrderItemTrackingRecord;
import com.zsmall.order.entity.domain.bo.OrderItemTrackingRecordBo;
import com.zsmall.order.entity.domain.dto.TrackingPageDTO;
import com.zsmall.order.entity.domain.vo.OrderItemTrackingRecordVo;
import com.zsmall.order.entity.mapper.OrderItemTrackingRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 子订单物流跟踪单Service接口
 *
 * <AUTHOR>
 * @date 2023-06-06
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IOrderItemTrackingRecordService extends ServiceImpl<OrderItemTrackingRecordMapper, OrderItemTrackingRecord> {

    private final OrderItemTrackingRecordMapper baseMapper;
    private final BusinessParameterService businessParameterService;


    /**
     * 查询子订单物流跟踪单
     */
    public OrderItemTrackingRecordVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询子订单物流跟踪单列表
     */
    public Page<OrderItemTrackingRecordVo> queryPageList(OrderItemTrackingRecordBo bo, PageQuery pageQuery) {

        Page<OrderItemTrackingRecord> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());
        TrackingPageDTO trackingPageDTO = this.generateDTO(bo);
        TenantType tenantType = LoginHelper.getTenantTypeEnum();
        if (TenantType.Supplier.equals(tenantType)) {
            trackingPageDTO.setTenantType(TenantType.Supplier.name());
            trackingPageDTO.setTenantId(LoginHelper.getTenantId());
        }
        log.info("trackingPageDTO = {}", JSONUtil.toJsonStr(trackingPageDTO));
        return TenantHelper.ignore(() -> baseMapper.queryPage(page, trackingPageDTO), TenantType.Manager, TenantType.Supplier);
    }

    /**
     * 查询子订单物流跟踪单列表
     */
    public List<OrderItemTrackingRecordVo> queryList(TrackingPageDTO trackingPageDTO) {
        log.info("trackingPageDTO = {}", JSONUtil.toJsonStr(trackingPageDTO));
        return TenantHelper.ignore(() -> baseMapper.queryPage(trackingPageDTO), TenantType.Manager, TenantType.Supplier);
    }

    private LambdaQueryWrapper<OrderItemTrackingRecord> buildQueryWrapper(OrderItemTrackingRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrderItemTrackingRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), OrderItemTrackingRecord::getOrderNo, bo.getOrderNo());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderItemNo()), OrderItemTrackingRecord::getOrderItemNo, bo.getOrderItemNo());
        lqw.eq(StringUtils.isNotBlank(bo.getSku()), OrderItemTrackingRecord::getSku, bo.getSku());
        lqw.eq(StringUtils.isNotBlank(bo.getProductSkuCode()), OrderItemTrackingRecord::getProductSkuCode, bo.getProductSkuCode());
        lqw.eq(bo.getQuantity() != null, OrderItemTrackingRecord::getQuantity, bo.getQuantity());
        lqw.eq(bo.getDispatchedTime() != null, OrderItemTrackingRecord::getDispatchedTime, bo.getDispatchedTime());
        lqw.eq(bo.getFulfillmentTime() != null, OrderItemTrackingRecord::getFulfillmentTime, bo.getFulfillmentTime());
        lqw.eq(StringUtils.isNotBlank(bo.getLogisticsCarrier()), OrderItemTrackingRecord::getLogisticsCarrier, bo.getLogisticsCarrier());
        lqw.eq(StringUtils.isNotBlank(bo.getLogisticsService()), OrderItemTrackingRecord::getLogisticsService, bo.getLogisticsService());
        lqw.eq(StringUtils.isNotBlank(bo.getLogisticsTrackingNo()), OrderItemTrackingRecord::getLogisticsTrackingNo, bo.getLogisticsTrackingNo());
        lqw.eq(StringUtils.isNotBlank(bo.getLogisticsProgress()), OrderItemTrackingRecord::getLogisticsProgress, bo.getLogisticsProgress());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseCode()), OrderItemTrackingRecord::getWarehouseCode, bo.getWarehouseCode());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseSystemCode()), OrderItemTrackingRecord::getWarehouseSystemCode, bo.getWarehouseSystemCode());
        lqw.eq(bo.getSystemManaged() != null, OrderItemTrackingRecord::getSystemManaged, bo.getSystemManaged());
        lqw.eq(bo.getCallingApi() != null, OrderItemTrackingRecord::getCallingApi, bo.getCallingApi());
        lqw.eq(StringUtils.isNotBlank(bo.getConfirmDate()), OrderItemTrackingRecord::getConfirmDate, bo.getConfirmDate());
        lqw.eq(bo.getLastQueryTime() != null, OrderItemTrackingRecord::getLastQueryTime, bo.getLastQueryTime());
        lqw.eq(StringUtils.isNotBlank(bo.getThirdPartyCode()), OrderItemTrackingRecord::getThirdPartyCode, bo.getThirdPartyCode());
        lqw.eq(StringUtils.isNotBlank(bo.getThirdPartyMessage()), OrderItemTrackingRecord::getThirdPartyMessage, bo.getThirdPartyMessage());
        lqw.eq(StringUtils.isNotBlank(bo.getThirdPartyDateTime()), OrderItemTrackingRecord::getThirdPartyDateTime, bo.getThirdPartyDateTime());
        return lqw;
    }

    /**
     * 新增子订单物流跟踪单
     */
    public Boolean insertByBo(OrderItemTrackingRecordBo bo) {
        OrderItemTrackingRecord add = MapstructUtils.convert(bo, OrderItemTrackingRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改子订单物流跟踪单
     */
    public Boolean updateByBo(OrderItemTrackingRecordBo bo) {
        OrderItemTrackingRecord update = MapstructUtils.convert(bo, OrderItemTrackingRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrderItemTrackingRecord entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除子订单物流跟踪单
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }


    public TrackingPageDTO generateDTO(OrderItemTrackingRecordBo bo) {


        ChannelTypeEnum channelType = bo.getChannelType();
        String logisticsCarrier = bo.getLogisticsCarrier();
        String logisticsProgress = bo.getLogisticsProgress();
        String logisticsType = bo.getLogisticsType();
        String orderNo = bo.getOrderNo();
        String channelOrderId = bo.getChannelOrderId();
        String sku = bo.getSku();
        String productSkuCode = bo.getProductSkuCode();
        String logisticsTrackingNo = bo.getLogisticsTrackingNo();


        TrackingPageDTO trackingPageDTO = new TrackingPageDTO();
        List<DateTime> orderDate = bo.getOrderDate();
        if (CollUtil.isNotEmpty(orderDate)) {
            log.info("orderDate = {}", JSONUtil.toJsonStr(orderDate));
            trackingPageDTO.setStartDate(DateUtil.format(DateUtil.beginOfDay(orderDate.get(0)), "yyyy-MM-dd HH:mm:ss"));
            trackingPageDTO.setEndDate(DateUtil.format(DateUtil.endOfDay(orderDate.get(1)), "yyyy-MM-dd HH:mm:ss"));
        }
        trackingPageDTO.setTenantId(LoginHelper.getTenantId());

        trackingPageDTO.setChannelOrderId(channelOrderId);

        if (channelType != null) {
            trackingPageDTO.setChannelType(channelType.name());
        }
        trackingPageDTO.setLogisticsCarrier(logisticsCarrier);
        trackingPageDTO.setLogisticsProgress(logisticsProgress);
        trackingPageDTO.setLogisticsType(logisticsType);
        trackingPageDTO.setOrderNo(orderNo);
        trackingPageDTO.setChannelOrderId(channelOrderId);
        trackingPageDTO.setSku(sku);
        trackingPageDTO.setProductSkuCode(productSkuCode);
        trackingPageDTO.setLogisticsTrackingNo(logisticsTrackingNo);

        return trackingPageDTO;
    }

    /**
     * 根据orderItemNo获取跟踪单信息集合
     *
     * @param orderItemNo
     * @return
     */
    public List<OrderItemTrackingRecord> getListByOrderItemNo(String orderItemNo) {
        LambdaQueryWrapper<OrderItemTrackingRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderItemTrackingRecord::getOrderItemNo, orderItemNo);
        return baseMapper.selectList(lqw);
    }

    /**
     * 根据orderNo获取跟踪单信息集合
     *
     * @param orderNoList
     * @return
     */
    public List<OrderItemTrackingRecord> getListByOrderNo(List<String> orderNoList) {
        LambdaQueryWrapper<OrderItemTrackingRecord> lqw = Wrappers.lambdaQuery();
        lqw.in(OrderItemTrackingRecord::getOrderNo, orderNoList);
        return baseMapper.selectList(lqw);
    }

    /**
     * 根据trackingNo获取跟踪单信息
     *
     * @param trackingNo
     * @return
     */
    public OrderItemTrackingRecord getByTrackingNo(String trackingNo) {
        LambdaQueryWrapper<OrderItemTrackingRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderItemTrackingRecord::getLogisticsTrackingNo, trackingNo);
        return baseMapper.selectOne(lqw);
    }

    /**
     * 根据orderNo获取跟踪单信息集合
     *
     * @param orderNo
     * @return
     */
    public List<OrderItemTrackingRecord> getListByOrderNo(String orderNo) {
        LambdaQueryWrapper<OrderItemTrackingRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderItemTrackingRecord::getOrderNo, orderNo);
        return baseMapper.selectList(lqw);
    }

    @InMethodLog("已存在的跟踪单解除子订单号关联")
    public Boolean trackingListDisassociate(String orderItemNo) {
        LambdaQueryWrapper<OrderItemTrackingRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderItemTrackingRecord::getOrderItemNo, orderItemNo);
        return baseMapper.delete(lqw) > 0;
    }

    @InMethodLog("已存在的跟踪单解除子订单号关联（多子单）")
    public Boolean trackingListDisassociate(List<String> orderItemNoList) {
        LambdaQueryWrapper<OrderItemTrackingRecord> lqw = Wrappers.lambdaQuery();
        lqw.in(OrderItemTrackingRecord::getOrderItemNo, orderItemNoList);
        return baseMapper.delete(lqw) > 0;
    }

    @InMethodLog("查询在途的跟踪记录")
    public List<OrderItemTrackingRecord> queryInTransitTrackingRecord(String carrier, String trackingNo, OrderStateType paid, LogisticsProgress fulfillment) {
        return baseMapper.queryInTransitTrackingRecord(carrier, trackingNo, paid.name(), fulfillment.name());
    }


    /**
     * 功能描述：按未跟踪订单获取列表
     *
     * @param channelNos 频道编号
     * @return {@link List }<{@link OrderItemTrackingRecord }>
     * <AUTHOR>
     * @date 2025/04/02
     */
    public List<OrderItemTrackingRecord> getListByOrdersHasCompleted(List<String> channelNos) {
        LambdaQueryWrapper<OrderItemTrackingRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OrderItemTrackingRecord::getOrderNo, channelNos);
        wrapper.isNotNull(OrderItemTrackingRecord::getLogisticsTrackingNo);
        wrapper.eq(OrderItemTrackingRecord::getDelFlag, 0);
        return baseMapper.selectList(wrapper);
    }
}
