package com.zsmall.order.entity.domain.event;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 统计订单支付退款总金额 - 开始结束时间范围内
 */
@Data
@NoArgsConstructor
public class StatsPlatformOrderPaymentRefundAmountEvent {
    /**
     * 入参-开始日期
     */
    private Date inStartDate;
    /**
     * 入参-结束时间
     */
    private Date inEndDate;

    /**
     * 出参-订单总金额
     */
    private BigDecimal orderTotalAmount;

    /**
     * 出参-订单退款总金额
     */
    private BigDecimal refundTotalAmount;

    public StatsPlatformOrderPaymentRefundAmountEvent(Date inStartDate, Date inEndDate) {
        this.inStartDate = inStartDate;
        this.inEndDate = inEndDate;
    }
}
