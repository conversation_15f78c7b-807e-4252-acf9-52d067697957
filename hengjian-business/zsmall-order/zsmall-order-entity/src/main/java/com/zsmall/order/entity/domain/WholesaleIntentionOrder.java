package com.zsmall.order.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 国外现货批发意向订单
 * @TableName wholesale_intention_order
 */
@TableName(value ="wholesale_intention_order")
@Data
@EqualsAndHashCode(callSuper=false)
public class WholesaleIntentionOrder extends NoDeptTenantEntity {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联订单编号
     */
    private String orderNo;

    /**
     * 国外现货批发意向订单编号
     */
    private String wholesaleIntentionOrderNo;

    /**
     * 国外现货商品编号
     */
    private String productCode;

    /**
     * 订购总数量
     */
    private Integer totalQuantity;

    /**
     * 预估操作费
     */
    private BigDecimal estimatedOperationFee;

    /**
     * 预估运费
     */
    private BigDecimal estimatedShippingFee;

    /**
     * 预估处理时间（天）
     */
    private Integer estimatedHandleTime;

    /**
     * 预留时间（天）
     */
    private Integer reservedTime;

    /**
     * 商品金额（总）
     */
    private BigDecimal productAmount;

    /**
     * 平台商品金额（总）
     */
    private BigDecimal productAmountPlatform;

    /**
     * 订金比例
     */
    private BigDecimal depositRatio;

    /**
     * 商品尾款金额（总）
     */
    private BigDecimal productBalanceAmount;

    /**
     * 平台商品尾款金额（总）
     */
    private BigDecimal productBalanceAmountPlatform;

    /**
     * 最终操作费（供货商录入）
     */
    private BigDecimal finalOperationFee;

    /**
     * 最终操作费（平台涨价后得出）
     */
    private BigDecimal finalOperationFeePlatform;

    /**
     * 最终运费（供货商录入）
     */
    private BigDecimal finalShippingFee;

    /**
     * 最终运费（平台涨价后得出）
     */
    private BigDecimal finalShippingFeePlatform;

    /**
     * 最终处理时间（供货商录入）
     */
    private Integer finalHandleTime;

    /**
     * 订金总金额（生成订单时已预付，为指定比例的商品总金额）
     */
    private BigDecimal orderDepositAmount;

    /**
     * 平台订金总金额（生成订单时已预付，为指定比例的商品总金额）
     */
    private BigDecimal orderDepositAmountPlatform;

    /**
     * 尾款总金额（构成为：批发代发：80%商品金额+操作费+物流费；批发自提：80%商品金额+操作费；批发一件代发：80%商品金额）
     */
    private BigDecimal orderBalanceAmount;

    /**
     * 平台尾款总金额（构成为：批发代发：80%平台商品金额+平台最终操作费+平台最终物流费；批发自提：80%平台商品金额+平台操作费；批发一件代发：80%平台商品金额）
     */
    private BigDecimal orderBalanceAmountPlatform;

    /**
     * 订单总金额（尾款+订金）
     */
    private BigDecimal orderTotalAmount;

    /**
     * 订单平台总金额（平台尾款+平台订金）
     */
    private BigDecimal orderTotalAmountPlatform;

    /**
     * 应付金额（一般情况下等于order_platform_balance_amount字段）
     */
    private BigDecimal orderPayableAmount;

    /**
     * 意向订单阶段（10-供货商未录入价格，20-供货商已录入价格，30-分销商已确认提货）
     */
    private Integer orderStage;

    /**
     * 意向订单状态（0-已取消，1-进行中，2-已完成）
     */
    private Integer orderStatus;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
