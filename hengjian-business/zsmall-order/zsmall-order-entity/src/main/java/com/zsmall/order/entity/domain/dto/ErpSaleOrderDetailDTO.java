package com.zsmall.order.entity.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2023/12/12 16:38
 */
@Data
@Accessors(chain = true)
public class ErpSaleOrderDetailDTO {
    /**
     * 订单ID
     */
    @Column(name = "head_id")
    private Integer headId;
    /**
     * 发货的快递账号
     */
    @Column(name = "source_third_party_account")
    private String sourceThirdPartyAccount;
    /**
     * 发货的快递账号
     */
    @Column(name = "charge_account_no")
    private String chargeAccountNo;
    /**
     * 测算的成本账号 与chargeAccountNo 只存在一个
     */
    @Column(name = "cost_account_no")
    private String costAccountNo;
    /**
     * 成本测算对应的caiirer（实际发货的carrier）
     */
    @Column(name = "cost_carrier_service_code")
    private String costCarrierServiceCode;
    /**
     * 前买面单标识 0 正常 1前买面单
     */
    @Column(name = "pre_buy_label_flag")
    private Integer preBuyLabelFlag;
    /**
     * 渠道发货方式
     */
    @Column(name = "channel_method_code")
    private String channelMethodCode;

    /**
     * 买家邮箱
     */
    @Column(name = "buy_email")
    private String buyEmail;
    /**
     * 买家姓名
     */
    @Column(name = "buy_name")
    private String buyName;
    /**
     * URL
     */
    @Column(name = "packing_url")
    private String packingUrl;
    /**
     * 发货条款
     */
    @Column(name = "fulfillment_policy")
    private String fulfillmentPolicy;
    /**
     * 显示的订单备注
     */
    @Column(name = "display_order_comment")
    private String displayOrderComment;
    /**
     * 卖家
     */
    @Column(name = "display_seller")
    private String displaySeller;
    /**
     * 卖家订单号
     */
    @Column(name = "display_ordernum")
    private String displayOrdernum;
    /**
     * 显示的订单时间
     */
    @Column(name = "display_order_date")
    private LocalDateTime displayOrderDate;
    /**
     * 包裹到达时间
     */
    @Column(name = "received_date")
    private LocalDateTime receivedDate;
    /**
     * 销售类型
     */
    @Column(name = "sales_channel")
    private String salesChannel;

    /**
     * 来源承运商
     */
    @Column(name = "source_carrier")
    private String sourceCarrier;

    /**
     * 系统承运商
     */
    @Column(name = "carrier")
    private String carrier;

    @Column(name = "carrier_addition")
    private String carrierAddition;
    /**
     * 渠道付款时间
     */
    @Column(name = "payment_date")
    private LocalDateTime paymentDate;
    /**
     * 仓库预计发货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'+00:00'",timezone = "UTC")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @Column(name = "warehouse_expected_ship_date")
    private LocalDateTime warehouseExpectedShipDate;


    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'+00:00'",timezone = "UTC")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @Column(name = "channel_latest_ship_date")
    private LocalDateTime channelLatestShipDate;
    /**
     * 最早发货时间
     */
    @Column(name = "earliest_ship_date")
    private LocalDateTime earliestShipDate;
    /**
     * 总折扣
     */
    @Column(name = "amount_discount")
    private BigDecimal amountDiscount;
    /**
     * 汇率
     */
    @Column(name = "rate")
    private BigDecimal rate;
    /**
     * cod数据
     */
    @Column(name = "cod_data")
    private String codData;
    /**
     * B2B清仓单号
     */
    @Column(name = "reference_no")
    private String referenceNo;
    /**
     * 发票号
     */
    @Column(name = "invoice_no")
    private String invoiceNo;
    /**
     * 是否验证库存
     */
    @Column(name = "check_inventory")
    private Integer checkInventory;
    /**
     * 订单回传平台状态
     */
    @Column(name = "return_channel_flag")
    private Integer returnChannelFlag;

    /**
     * 系统发货等级 0 标准（standard-48 hour） 1 当日发（same day ship out）
     */
    @Column(name = "system_ship_service_level")
    private Integer systemShipServiceLevel;



}
