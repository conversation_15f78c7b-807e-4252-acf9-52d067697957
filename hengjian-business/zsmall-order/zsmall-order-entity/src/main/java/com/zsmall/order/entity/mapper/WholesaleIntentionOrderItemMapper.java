package com.zsmall.order.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zsmall.order.entity.domain.WholesaleIntentionOrderItem;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【wholesale_intention_order_item(国外现货批发意向子订单)】的数据库操作Mapper
* @createDate 2023-07-24 10:46:40
* @Entity com.zsmall.order.entity.domain.WholesaleIntentionOrderItem
*/
public interface WholesaleIntentionOrderItemMapper extends BaseMapper<WholesaleIntentionOrderItem> {

    @InterceptorIgnore(tenantLine = "true")
    boolean existsWholesaleIntentionOrderItemNo(@Param("wholesaleIntentionOrderNo") String wholesaleIntentionOrderNo);

    @InterceptorIgnore(tenantLine = "true")
    List<WholesaleIntentionOrderItem> existsProcessingOrderItem(@Param("productSkuCodeList") List<String> productSkuCodeList);
}




