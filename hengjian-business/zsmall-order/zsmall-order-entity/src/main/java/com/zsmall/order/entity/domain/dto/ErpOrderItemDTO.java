package com.zsmall.order.entity.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2023/12/12 15:48
 */
@Setter
@Getter
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.USE_DEFAULTS)
public class ErpOrderItemDTO implements Serializable {
    private String sku; // Seller SKU － 必填
    private String asin; // Amazon ASIN － AMAZON订单必填
    private String title; // 产品描述 － 必填
    private String quantity; // 产品数量 － 必填
    private String subtotal; // 行总额 － 必填  供货价啊
    private String sales_total_amount; // 行销售总金额
    private String insurance_amount;//产品保额
    private String is_insurance;//是否投保
    private String currency_code; // 行币种 － 必填
    private String display_comment; // 发货单显示备注
    private String shiped_quantity; // 已发产品数量 － 如果有, 必填
    private String track_no; // 物流单号 － 如果有, 必填
    private String process_price; // 处理费
    private String shipping_cost; // 发货成本 － 如果有, 必填
    private String shipping_cost_currency_code; // 发货成本币种 － 如果有, 必填
    private String shipping_price; // 运费－ 如果有, 必填
    private String shipping_currency_code; // 运费币种－ 如果有, 必填
    private String shipping_tax_price; // 运费税
    private String shipping_tax_currency_code; // 运费税币种
    private String shipping_discount_price; // 运费折扣
    private String shipping_discount_currency_code; // 运费折扣币种
    private String tax_price; // 税费
    private String tax_currency_code; // 税费币种
    private String promotion_ids; // 折扣信息
    private String promotion_discount_price; // 折扣价格
    private String promotion_discount_currency_code; // 折扣币种
    private String gift_message_text; // 礼品包装留言
    private String gift_wrap_price; // 礼品包装价格
    private String gift_wrap_currency_code; // 礼品包装币种
    private String gift_wrap_tax_price; // 礼品包装税
    private String gift_wrap_tax_currency_code; // 礼品包装税比重
    private String channel_order_item_id;
    private String remark; // 行备注
    private String created; // 行创建时间－ 必填
    private String updated; // 行更新时间－ 必填
    private String price;    //行单价
    private String order_line_status;
    private String storeId;
    private String carrier_addition;//签收
    private String org_warehouse_id;//仓库id

    //ack专用字段
    private String accept_quantity;
    private String receipt_date;

    private String seller_sku; // added by saisai.liang @20230403 for 发货订单导入增加sellersku字段

    //uspo
    private String unit_of_measure;     //单位  样例：Eaches
    private String unit_size;           //单位尺寸  样例：1
    private Integer is_back_order_allowed;  //是否允许延期交货

}
