package com.zsmall.order.entity.mapper;

import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.order.entity.domain.AirwallexRequestInfo;
import com.zsmall.order.entity.domain.AirwallexRequestLog;
import com.zsmall.order.entity.domain.OrderAddressInfo;
import com.zsmall.order.entity.domain.vo.AirwallexRequestInfoVo;
import com.zsmall.order.entity.domain.vo.OrderAddressInfoVo;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2024年3月7日  18:45
 * @description:
 */
public interface AirwallexRequestInfoMapper extends BaseMapperPlus<AirwallexRequestInfo, AirwallexRequestInfoVo> {

    /**
     * 根据requestId获取AirwallexRequestInfo
     *
     * @param requestId
     * @return
     */
    AirwallexRequestInfo getByRequestId(@Param("requestId")String requestId);

    /**
     * 根据commitId获取AirwallexRequestInfo
     *
     * @param commitId
     * @return
     */
    AirwallexRequestInfo getByCommitId(@Param("commitId")String commitId);

    /**
     * 新增空中云汇日志信息
     *
     * @param airwallexRequestLog
     */
    void addAirwallexRequestLog(@Param("airwallexRequestLog") AirwallexRequestLog airwallexRequestLog);
}
