package com.zsmall.order.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.order.entity.domain.OrderItemProductSku;
import com.zsmall.order.entity.domain.vo.OrderItemProductSkuVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 子订单商品SKU信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
public interface OrderItemProductSkuMapper extends BaseMapperPlus<OrderItemProductSku, OrderItemProductSkuVo> {

    @InterceptorIgnore(tenantLine = "true")
    OrderItemProductSku queryByWrapperNoTenant(@Param(Constants.WRAPPER) Wrapper<OrderItemProductSku> queryWrapper);

    /**
     * 根据订单明细id修改删除标识
     *
     * @param orderItemIdList
     * @param delFlag
     */
    void updateDelFlagByOrderItemIdList(@Param("orderItemIdList") List<Long> orderItemIdList, @Param("delFlag") Integer delFlag);
}
