package com.zsmall.order.entity.domain;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.common.enums.orderImportRecord.ImportStateEnum;
import com.zsmall.common.enums.orderImportRecord.ImportTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 订单导入记录对象 order_import_record
 *
 * <AUTHOR>
 * @date 2023-06-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "order_import_record", autoResultMap = true)
public class OrderImportRecord extends NoDeptTenantEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 存储对象主键
     */
    private Long ossId;

    /**
     * 导入记录编号
     */
    private String importRecordNo;
    /**
     * 订单处理状态 0 未下单 1正在下单 2 已下单  3 下单失败
     */
    private Integer orderTempState;
    /**
     * 导入文件名
     */
    private String importFileName;

    /**
     * 导入的订单数量
     */
    private Integer importOrders;

    /**
     * 导入信息
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONObject importMessage;

    /**
     * 导入类型：Excel-表格，ShippingCart-购物车
     */
    private ImportTypeEnum importType;

    /**
     * 导入状态：Failed，Cancel，Pending，Importing，Success
     */
    private ImportStateEnum importState;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
