package com.zsmall.order.biz.service;

import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.order.entity.domain.bo.OrderAttachmentBo;
import com.zsmall.order.entity.domain.vo.OrderAttachmentVo;

import java.util.Collection;
import java.util.List;

/**
 * 主订单附件Service接口
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
public interface OrderAttachmentService {

    /**
     * 查询主订单附件
     */
    OrderAttachmentVo queryById(Long id);

    /**
     * 查询主订单附件列表
     */
    TableDataInfo<OrderAttachmentVo> queryPageList(OrderAttachmentBo bo, PageQuery pageQuery);

    /**
     * 查询主订单附件列表
     */
    List<OrderAttachmentVo> queryList(OrderAttachmentBo bo);

    /**
     * 新增主订单附件
     */
    Boolean insertByBo(OrderAttachmentBo bo);

    /**
     * 修改主订单附件
     */
    Boolean updateByBo(OrderAttachmentBo bo);

    /**
     * 校验并批量删除主订单附件信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
