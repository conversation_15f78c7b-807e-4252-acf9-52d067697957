package com.zsmall.order.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.common.enums.order.OrderExceptionEnum;
import com.zsmall.order.biz.service.OrderItemPriceService;
import com.zsmall.order.entity.domain.OrderItemPrice;
import com.zsmall.order.entity.domain.bo.OrderItemPriceBo;
import com.zsmall.order.entity.domain.vo.OrderItemPriceVo;
import com.zsmall.order.entity.iservice.IOrderItemPriceService;
import com.zsmall.order.entity.mapper.OrderItemPriceMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 子订单价格Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@RequiredArgsConstructor
@Service
public class OrderItemPriceServiceImpl implements OrderItemPriceService {

    private final OrderItemPriceMapper baseMapper;
    private final IOrderItemPriceService iOrderItemPriceService;
    /**
     * 查询子订单价格
     */
    @Override
    public OrderItemPriceVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询子订单价格列表
     */
    @Override
    public TableDataInfo<OrderItemPriceVo> queryPageList(OrderItemPriceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OrderItemPrice> lqw = buildQueryWrapper(bo);
        Page<OrderItemPriceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询子订单价格列表
     */
    @Override
    public List<OrderItemPriceVo> queryList(OrderItemPriceBo bo) {
        LambdaQueryWrapper<OrderItemPrice> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OrderItemPrice> buildQueryWrapper(OrderItemPriceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrderItemPrice> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getOrderItemId() != null, OrderItemPrice::getOrderItemId, bo.getOrderItemId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderItemNo()), OrderItemPrice::getOrderItemNo, bo.getOrderItemNo());
        lqw.eq(StringUtils.isNotBlank(bo.getProductSkuCode()), OrderItemPrice::getProductSkuCode, bo.getProductSkuCode());
        lqw.eq(StringUtils.isNotBlank(bo.getLogisticsType()), OrderItemPrice::getLogisticsType, bo.getLogisticsType());
        lqw.eq(bo.getTotalQuantity() != null, OrderItemPrice::getTotalQuantity, bo.getTotalQuantity());
        lqw.eq(bo.getOriginalUnitPrice() != null, OrderItemPrice::getOriginalUnitPrice, bo.getOriginalUnitPrice());
        lqw.eq(bo.getOriginalOperationFee() != null, OrderItemPrice::getOriginalOperationFee, bo.getOriginalOperationFee());
        lqw.eq(bo.getOriginalFinalDeliveryFee() != null, OrderItemPrice::getOriginalFinalDeliveryFee, bo.getOriginalFinalDeliveryFee());
        lqw.eq(bo.getOriginalPickUpPrice() != null, OrderItemPrice::getOriginalPickUpPrice, bo.getOriginalPickUpPrice());
        lqw.eq(bo.getOriginalDropShippingPrice() != null, OrderItemPrice::getOriginalDropShippingPrice, bo.getOriginalDropShippingPrice());
        lqw.eq(bo.getOriginalDepositUnitPrice() != null, OrderItemPrice::getOriginalDepositUnitPrice, bo.getOriginalDepositUnitPrice());
        lqw.eq(bo.getOriginalBalanceUnitPrice() != null, OrderItemPrice::getOriginalBalanceUnitPrice, bo.getOriginalBalanceUnitPrice());
        lqw.eq(bo.getPlatformUnitPrice() != null, OrderItemPrice::getPlatformUnitPrice, bo.getPlatformUnitPrice());
        lqw.eq(bo.getPlatformOperationFee() != null, OrderItemPrice::getPlatformOperationFee, bo.getPlatformOperationFee());
        lqw.eq(bo.getPlatformFinalDeliveryFee() != null, OrderItemPrice::getPlatformFinalDeliveryFee, bo.getPlatformFinalDeliveryFee());
        lqw.eq(bo.getPlatformPickUpPrice() != null, OrderItemPrice::getPlatformPickUpPrice, bo.getPlatformPickUpPrice());
        lqw.eq(bo.getPlatformDropShippingPrice() != null, OrderItemPrice::getPlatformDropShippingPrice, bo.getPlatformDropShippingPrice());
        lqw.eq(bo.getPlatformDepositUnitPrice() != null, OrderItemPrice::getPlatformDepositUnitPrice, bo.getPlatformDepositUnitPrice());
        lqw.eq(bo.getPlatformBalanceUnitPrice() != null, OrderItemPrice::getPlatformBalanceUnitPrice, bo.getPlatformBalanceUnitPrice());
        lqw.eq(bo.getMsrp() != null, OrderItemPrice::getMsrp, bo.getMsrp());
        return lqw;
    }

    /**
     * 新增子订单价格
     */
    @Override
    public Boolean insertByBo(OrderItemPriceBo bo) {
        OrderItemPrice add = MapstructUtils.convert(bo, OrderItemPrice.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改子订单价格
     */
    @Override
    public Boolean updateByBo(OrderItemPriceBo bo) {
        OrderItemPrice update = MapstructUtils.convert(bo, OrderItemPrice.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrderItemPrice entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除子订单价格
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public void saveOrSetNUll(List<OrderItemPrice> itemPrices, Integer exceptionCode) {
        if(OrderExceptionEnum.final_delivery_fee_exception.getValue().equals(exceptionCode)
            ||OrderExceptionEnum.measurement_anomaly.getValue().equals(exceptionCode)
            ||OrderExceptionEnum.out_of_stock_exception.getValue().equals(exceptionCode)){

            for (OrderItemPrice itemPrice : itemPrices) {
                itemPrice.setOriginalDropShippingPrice(null);
                itemPrice.setPlatformDropShippingPrice(null);
                itemPrice.setOriginalDropShippingPrice(null);
                itemPrice.setOriginalFinalDeliveryFee(null);
                itemPrice.setPlatformFinalDeliveryFee(null);
                itemPrice.setPlatformDropShippingPrice(null);
                itemPrice.setOriginalBalanceUnitPrice(null);
                itemPrice.setPlatformBalanceUnitPrice(null);
            }
        }
        if(CollUtil.isNotEmpty(itemPrices)){
            iOrderItemPriceService.saveBatch(itemPrices);
        }
    }

    @Override
    public void saveOrSetNull(List<OrderItemPrice> orderItemPriceList) {
        if(CollUtil.isNotEmpty(orderItemPriceList)){
            iOrderItemPriceService.saveBatch(orderItemPriceList);
        }
    }

    @Override
    public void updateSetNUll(List<String> itemNos, Integer exceptionCode) {
        if(OrderExceptionEnum.final_delivery_fee_exception.getValue().equals(exceptionCode)
            ||OrderExceptionEnum.measurement_anomaly.getValue().equals(exceptionCode)
            ||OrderExceptionEnum.out_of_stock_exception.getValue().equals(exceptionCode)){
            LambdaUpdateWrapper<OrderItemPrice> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(OrderItemPrice::getOriginalDropShippingPrice,null);
            updateWrapper.set(OrderItemPrice::getOriginalDropShippingPrice, null);
            updateWrapper.set(OrderItemPrice::getPlatformDropShippingPrice, null);
            updateWrapper.set(OrderItemPrice::getOriginalFinalDeliveryFee, null);
            updateWrapper.set(OrderItemPrice::getPlatformFinalDeliveryFee, null);
            updateWrapper.set(OrderItemPrice::getOriginalBalanceUnitPrice, null);
            updateWrapper.set(OrderItemPrice::getPlatformBalanceUnitPrice, null);
            updateWrapper.in(OrderItemPrice::getOrderItemNo,itemNos);
            baseMapper.update(null,updateWrapper);
        }

    }

    @Override
    public void saveUpdateOrSetNUll(List<OrderItemPrice> itemPrices, Integer exceptionCode) {
        if(OrderExceptionEnum.final_delivery_fee_exception.getValue().equals(exceptionCode)
            ||OrderExceptionEnum.measurement_anomaly.getValue().equals(exceptionCode)
            ||OrderExceptionEnum.out_of_stock_exception.getValue().equals(exceptionCode)){

            for (OrderItemPrice itemPrice : itemPrices) {
                itemPrice.setOriginalDropShippingPrice(null);
                itemPrice.setPlatformDropShippingPrice(null);
                itemPrice.setOriginalDropShippingPrice(null);
                itemPrice.setOriginalFinalDeliveryFee(null);
                itemPrice.setPlatformFinalDeliveryFee(null);
                itemPrice.setPlatformDropShippingPrice(null);
                itemPrice.setOriginalBalanceUnitPrice(null);
                itemPrice.setPlatformBalanceUnitPrice(null);
            }
        }
        if(CollUtil.isNotEmpty(itemPrices)){
            iOrderItemPriceService.saveOrUpdateBatch(itemPrices);
        }
    }
}
