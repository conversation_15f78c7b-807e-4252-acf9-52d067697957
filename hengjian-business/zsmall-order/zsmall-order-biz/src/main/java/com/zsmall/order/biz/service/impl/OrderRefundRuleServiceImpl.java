package com.zsmall.order.biz.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.enums.order.LogisticsProgress;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.order.biz.service.OrderRefundRuleService;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.OrderRefundRule;
import com.zsmall.order.entity.domain.bo.OrderRefundRuleBo;
import com.zsmall.order.entity.domain.vo.OrderRefundRuleVo;
import com.zsmall.order.entity.iservice.IOrderRefundRuleService;
import com.zsmall.order.entity.iservice.IOrderRefundService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 售后规则Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-06-08
 */
@RequiredArgsConstructor
@Service
public class OrderRefundRuleServiceImpl implements OrderRefundRuleService {

    private final IOrderRefundRuleService iOrderRefundRuleService;
    private final IOrderRefundService iOrderRefundService;
    private final BusinessParameterService businessParameterService;

    /**
     * 查询售后规则
     */
    @Override
    public OrderRefundRuleVo queryById(Long id){
        return iOrderRefundRuleService.queryById(id);
    }

    /**
     * 查询售后规则列表
     */
    @Override
    public TableDataInfo<OrderRefundRuleVo> queryPageList(OrderRefundRuleBo bo, PageQuery pageQuery) {
        return iOrderRefundRuleService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询售后规则列表
     */
    @Override
    public List<OrderRefundRuleVo> queryList(OrderRefundRuleBo bo) {
        return iOrderRefundRuleService.queryList(bo);
    }

    /**
     * 新增售后规则
     */
    @Override
    public Boolean insertByBo(OrderRefundRuleBo bo) {
        return iOrderRefundRuleService.insertByBo(bo);
    }

    /**
     * 修改售后规则
     */
    @Override
    public Boolean updateByBo(OrderRefundRuleBo bo) {
        return iOrderRefundRuleService.updateByBo(bo);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrderRefundRule entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除售后规则
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return iOrderRefundRuleService.deleteWithValidByIds(ids, isValid);
    }

    /**
     * 判断子订单是否符合退货规则
     *
     * @param orderItem
     * @return
     */
    @Override
    public Boolean compareRefundRules(OrderItem orderItem, Boolean existsOrderRefund) {
        if (!existsOrderRefund) {
            return false;
        }
        Boolean canRefund = true;
        if (Objects.equals(orderItem.getFulfillmentProgress(), LogisticsProgress.Fulfilled)) {
            Date fulfillmentTime = orderItem.getFulfillmentTime();
            if (fulfillmentTime != null) {
                Integer refundDays = businessParameterService.getValueFromInteger(BusinessParameterType.REFUND_DAYS);
                int between =(int) DateUtil.between(fulfillmentTime, new Date(), DateUnit.DAY);
                canRefund = refundDays.compareTo(between) >= 0;
            } else {
                canRefund = false;
            }
        }
        return canRefund;
    }


}
