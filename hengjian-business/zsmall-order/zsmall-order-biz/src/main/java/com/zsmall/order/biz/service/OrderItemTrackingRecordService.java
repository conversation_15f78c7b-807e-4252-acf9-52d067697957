package com.zsmall.order.biz.service;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.order.entity.domain.bo.OrderItemTrackingRecordBo;
import com.zsmall.order.entity.domain.dto.OrderItemTrackingDTO;
import com.zsmall.order.entity.domain.vo.OrderItemTrackingRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * 子订单物流跟踪单Service接口
 *
 * <AUTHOR>
 * @date 2023-06-06
 */
public interface OrderItemTrackingRecordService {

    /**
     * 查询子订单物流跟踪单
     */
    OrderItemTrackingRecordVo queryById(Long id);

    /**
     * 查询子订单物流跟踪单列表
     */
    TableDataInfo<OrderItemTrackingRecordVo> queryPageList(OrderItemTrackingRecordBo bo, PageQuery pageQuery);

    /**
     * 查询子订单物流跟踪单列表
     */
    R<Void> queryList(OrderItemTrackingRecordBo bo);

    /**
     * 新增子订单物流跟踪单
     */
    Boolean insertByBo(OrderItemTrackingRecordBo bo);

    /**
     * 修改子订单物流跟踪单
     */
    Boolean updateByBo(OrderItemTrackingRecordBo bo);

    /**
     * 校验并批量删除子订单物流跟踪单信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    void batchSetTracking(List<OrderItemTrackingDTO> dtos);
}
