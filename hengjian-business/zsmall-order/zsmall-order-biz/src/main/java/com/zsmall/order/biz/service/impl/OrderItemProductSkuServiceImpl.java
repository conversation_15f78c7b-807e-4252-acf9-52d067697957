package com.zsmall.order.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.order.biz.service.OrderItemProductSkuService;
import com.zsmall.order.entity.domain.OrderItemProductSku;
import com.zsmall.order.entity.domain.bo.OrderItemProductSkuBo;
import com.zsmall.order.entity.domain.vo.OrderItemProductSkuVo;
import com.zsmall.order.entity.mapper.OrderItemProductSkuMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 子订单商品SKU信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@RequiredArgsConstructor
@Service
public class OrderItemProductSkuServiceImpl implements OrderItemProductSkuService {

    private final OrderItemProductSkuMapper baseMapper;

    /**
     * 查询子订单商品SKU信息
     */
    @Override
    public OrderItemProductSkuVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询子订单商品SKU信息列表
     */
    @Override
    public TableDataInfo<OrderItemProductSkuVo> queryPageList(OrderItemProductSkuBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OrderItemProductSku> lqw = buildQueryWrapper(bo);
        Page<OrderItemProductSkuVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询子订单商品SKU信息列表
     */
    @Override
    public List<OrderItemProductSkuVo> queryList(OrderItemProductSkuBo bo) {
        LambdaQueryWrapper<OrderItemProductSku> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OrderItemProductSku> buildQueryWrapper(OrderItemProductSkuBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrderItemProductSku> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getSupplierTenantId()), OrderItemProductSku::getSupplierTenantId, bo.getSupplierTenantId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), OrderItemProductSku::getOrderNo, bo.getOrderNo());
        lqw.eq(bo.getOrderItemId() != null, OrderItemProductSku::getOrderItemId, bo.getOrderItemId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderItemNo()), OrderItemProductSku::getOrderItemNo, bo.getOrderItemNo());
        lqw.eq(StringUtils.isNotBlank(bo.getChannelType()), OrderItemProductSku::getChannelType, bo.getChannelType());
        lqw.eq(bo.getChannelId() != null, OrderItemProductSku::getChannelId, bo.getChannelId());
        lqw.eq(bo.getChannelVariantId() != null, OrderItemProductSku::getChannelVariantId, bo.getChannelVariantId());
        lqw.eq(StringUtils.isNotBlank(bo.getChannelWarehouseCode()), OrderItemProductSku::getChannelWarehouseCode, bo.getChannelWarehouseCode());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), OrderItemProductSku::getProductCode, bo.getProductCode());
        lqw.eq(StringUtils.isNotBlank(bo.getProductSkuCode()), OrderItemProductSku::getProductSkuCode, bo.getProductSkuCode());
        lqw.eq(StringUtils.isNotBlank(bo.getSku()), OrderItemProductSku::getSku, bo.getSku());
        lqw.eq(StringUtils.isNotBlank(bo.getUpc()), OrderItemProductSku::getUpc, bo.getUpc());
        lqw.eq(StringUtils.isNotBlank(bo.getErpSku()), OrderItemProductSku::getErpSku, bo.getErpSku());
        lqw.eq(StringUtils.isNotBlank(bo.getMappingSku()), OrderItemProductSku::getMappingSku, bo.getMappingSku());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), OrderItemProductSku::getProductName, bo.getProductName());
        lqw.eq(StringUtils.isNotBlank(bo.getDescription()), OrderItemProductSku::getDescription, bo.getDescription());
        lqw.eq(bo.getImageOssId() != null, OrderItemProductSku::getImageOssId, bo.getImageOssId());
        lqw.eq(StringUtils.isNotBlank(bo.getImageSavePath()), OrderItemProductSku::getImageSavePath, bo.getImageSavePath());
        lqw.eq(StringUtils.isNotBlank(bo.getImageShowUrl()), OrderItemProductSku::getImageShowUrl, bo.getImageShowUrl());
        lqw.like(StringUtils.isNotBlank(bo.getSpecComposeName()), OrderItemProductSku::getSpecComposeName, bo.getSpecComposeName());
        lqw.like(StringUtils.isNotBlank(bo.getSpecValName()), OrderItemProductSku::getSpecValName, bo.getSpecValName());
        lqw.eq(StringUtils.isNotBlank(bo.getSpecifyWarehouse()), OrderItemProductSku::getSpecifyWarehouse, bo.getSpecifyWarehouse());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseSystemCode()), OrderItemProductSku::getWarehouseSystemCode, bo.getWarehouseSystemCode());
        lqw.eq(StringUtils.isNotBlank(bo.getActivityType()), OrderItemProductSku::getActivityType, bo.getActivityType());
        lqw.eq(StringUtils.isNotBlank(bo.getActivityCode()), OrderItemProductSku::getActivityCode, bo.getActivityCode());
        return lqw;
    }

    /**
     * 新增子订单商品SKU信息
     */
    @Override
    public Boolean insertByBo(OrderItemProductSkuBo bo) {
        OrderItemProductSku add = MapstructUtils.convert(bo, OrderItemProductSku.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改子订单商品SKU信息
     */
    @Override
    public Boolean updateByBo(OrderItemProductSkuBo bo) {
        OrderItemProductSku update = MapstructUtils.convert(bo, OrderItemProductSku.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrderItemProductSku entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除子订单商品SKU信息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
