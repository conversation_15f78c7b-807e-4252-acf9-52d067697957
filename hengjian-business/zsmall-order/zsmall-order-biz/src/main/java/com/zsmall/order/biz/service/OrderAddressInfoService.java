package com.zsmall.order.biz.service;

import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.order.entity.domain.bo.OrderAddressInfoBo;
import com.zsmall.order.entity.domain.vo.OrderAddressInfoVo;

import java.util.Collection;
import java.util.List;

/**
 * 主订单地址信息Service接口
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
public interface OrderAddressInfoService {

    /**
     * 查询主订单地址信息
     */
    OrderAddressInfoVo queryById(Long id);

    /**
     * 查询主订单地址信息列表
     */
    TableDataInfo<OrderAddressInfoVo> queryPageList(OrderAddressInfoBo bo, PageQuery pageQuery);

    /**
     * 查询主订单地址信息列表
     */
    List<OrderAddressInfoVo> queryList(OrderAddressInfoBo bo);

    /**
     * 新增主订单地址信息
     */
    Boolean insertByBo(OrderAddressInfoBo bo);

    /**
     * 修改主订单地址信息
     */
    Boolean updateByBo(OrderAddressInfoBo bo);

    /**
     * 校验并批量删除主订单地址信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
