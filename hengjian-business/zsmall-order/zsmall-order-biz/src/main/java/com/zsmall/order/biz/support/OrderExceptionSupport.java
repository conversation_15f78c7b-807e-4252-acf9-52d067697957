package com.zsmall.order.biz.support;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.order.OrderExceptionEnum;
import com.zsmall.order.entity.domain.OrderExceptionHandleRecord;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.iservice.IOrderExceptionHandleRecordService;
import com.zsmall.order.entity.iservice.IOrdersService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年11月26日  11:58
 * @description: 订单异常处理支持类
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderExceptionSupport {

    private final IOrdersService ordersService;
    private final IOrderExceptionHandleRecordService orderExceptionHandleRecordService;
    private final OrderSupport orderSupport;

    /**
     * 获取面单异常订单处理
     */
    @InMethodLog("获取面单异常订单处理")
    @Transactional(rollbackFor = Exception.class)
    public void orderLogisticsAttachmentException(){
        LambdaQueryWrapper<Orders> lqw = Wrappers.lambdaQuery();
        lqw.eq(Orders::getExceptionCode, OrderExceptionEnum.logistics_attachment_exception.getValue());
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime fiveDaysAgo = now.minus(5, ChronoUnit.DAYS);
        lqw.between(Orders::getCreateTime, fiveDaysAgo, now);
        List<Orders> ordersList = TenantHelper.ignore(() -> ordersService.list(lqw));
        List<Orders> getLogisticsAttachmentOrderList = new ArrayList<>();
        log.info("获取面单异常订单数据:{}",ordersList);
        if(CollUtil.isNotEmpty(ordersList)){
            for(Orders orders : ordersList){
                OrderExceptionHandleRecord orderExceptionHandleRecord = orderExceptionHandleRecordService.getByOrderId(orders.getId());
                if(null == orderExceptionHandleRecord){
                    // 新增订单异常处理记录
                    OrderExceptionHandleRecord orderExceptionHandleRecordInsert = new OrderExceptionHandleRecord();
                    orderExceptionHandleRecordInsert.setOrderId(orders.getId()).setOrderNo(orders.getOrderNo()).setExceptionCode(orders.getExceptionCode()).setRetryCount(1).setMaxRetryCount(3);
                    orderExceptionHandleRecordInsert.setTenantId(orders.getTenantId());
                    orderExceptionHandleRecordService.save(orderExceptionHandleRecordInsert);
                    getLogisticsAttachmentOrderList.add(orders);
                }else {
                    // 编辑订单异常处理记录
                    if(null != orderExceptionHandleRecord.getRetryCount() && null != orderExceptionHandleRecord.getMaxRetryCount()
                    && orderExceptionHandleRecord.getRetryCount().compareTo(orderExceptionHandleRecord.getMaxRetryCount()) < 0){
                        orderExceptionHandleRecord.setRetryCount(orderExceptionHandleRecord.getRetryCount()+1);
                        orderExceptionHandleRecordService.updateById(orderExceptionHandleRecord);
                        getLogisticsAttachmentOrderList.add(orders);
                    }
                }
            }
        }
        log.info("需要重新发送获取面单队列的订单数据:{}",getLogisticsAttachmentOrderList);
        if(CollUtil.isNotEmpty(getLogisticsAttachmentOrderList)){
            orderSupport.sendAmazonVCGetLogisticsAttachmentMessage(getLogisticsAttachmentOrderList);
        }
    }

}
