package com.zsmall.order.biz.utils;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.math.BigInteger;
import java.util.Arrays;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/3/8 18:25
 */
public class HashUtil {

    private static final int DESIRED_HASH_LENGTH_IN_BYTES = 4; // 例如，截取4个字节作为哈希值

    private static final String HASH_ALGORITHM = "SHA-256";
    private static final int DESIRED_LENGTH = 8;
    private static final int DESIRED_HASH_LENGTH = 8;

    private static final long FNV_offset_basis = 0xcbf29ce484222325L;
    private static final long FNV_prime = 0x100000001b3L;
    public static String getSha256HashCode(String number) {
        try {
            // 初始化MessageDigest实例
            MessageDigest digest = MessageDigest.getInstance(HASH_ALGORITHM);
            // 对订单号进行哈希处理
            byte[] hashBytes = digest.digest(number.getBytes(StandardCharsets.UTF_8));
            // 将哈希值转换为BigInteger
            BigInteger hashCode = new BigInteger(1, hashBytes);
            // 提取指定长度的哈希值
            String hashedOrderNumber = hashCode.toString(16).substring(0, DESIRED_LENGTH);
            // 如果需要，可以填充前导零
            return hashedOrderNumber;
        } catch (NoSuchAlgorithmException e) {
            // 处理算法不存在的异常
            throw new RuntimeException("Hashing algorithm not found", e);
        }
    }

    public static int getSha256HashCode8(String input) throws NoSuchAlgorithmException {
        MessageDigest digest = MessageDigest.getInstance(HASH_ALGORITHM);
        byte[] hashBytes = digest.digest(input.getBytes(StandardCharsets.UTF_8));

        // 截取哈希值的前一个字节（8位）
        byte hashedByte = hashBytes[0];

        // 将字节转换为0-255之间的正整数
        int number = hashedByte & 0xFF;

        return number;
    }

    /**
     * 功能描述：获取fnv1a64
     *
     * @param data 数据
     * @return long
     * <AUTHOR>
     * @date 2024/03/08
     */
    public static long getFnv1a64(String data) {
        long hash = FNV_offset_basis;

        for (int i = 0; i < data.length(); i++) {
            hash ^= (data.charAt(i) & 0xff);
            hash *= FNV_prime;
        }

        return hash;
    }
}
