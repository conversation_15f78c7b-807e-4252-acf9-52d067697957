package com.zsmall.order.biz.service;

import com.hengjian.common.core.domain.R;
import com.zsmall.common.domain.dto.OrderReceiveFromThirdDTO;
import com.zsmall.common.domain.dto.ThirdReceiptDTO;
import com.zsmall.product.entity.domain.bo.siteBo.SitePriceCleanBo;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/21 22:14
 */
public interface OrderTripartiteEntryService {
    R tripartiteBatchEnterForErp(List<OrderReceiveFromThirdDTO> dto) throws InterruptedException;

    void testThreadPool();

    R tripartiteBatchReceiptForErp(List<ThirdReceiptDTO> dtos) throws InterruptedException;

    /**
     * 功能描述：
     * 功能描述：初始化价格
     *
     * @param sitePriceCleanBos 网站价格清洁bos
     * @param appointSwitch     指定清洗产品开关
     * @param acquiesceSwitch   默认清洗产品开关
     * @return {@link Boolean }
     * <AUTHOR>
     * @date 2024/12/23
     */
    Boolean initializePricesAndSites(List<SitePriceCleanBo> sitePriceCleanBos, boolean appointSwitch,
                                     boolean acquiesceSwitch);
}
