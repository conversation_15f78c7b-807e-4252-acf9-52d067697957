package com.zsmall.order.biz.service;

import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.order.entity.domain.bo.OrderRefundLogisticsBo;
import com.zsmall.order.entity.domain.vo.OrderRefundLogisticsVo;

import java.util.Collection;
import java.util.List;

/**
 * 售后退货物流Service接口
 *
 * <AUTHOR>
 * @date 2023-06-08
 */
public interface OrderRefundLogisticsService {

    /**
     * 查询售后退货物流
     */
    OrderRefundLogisticsVo queryById(Long id);

    /**
     * 查询售后退货物流列表
     */
    TableDataInfo<OrderRefundLogisticsVo> queryPageList(OrderRefundLogisticsBo bo, PageQuery pageQuery);

    /**
     * 查询售后退货物流列表
     */
    List<OrderRefundLogisticsVo> queryList(OrderRefundLogisticsBo bo);

    /**
     * 新增售后退货物流
     */
    Boolean insertByBo(OrderRefundLogisticsBo bo);

    /**
     * 修改售后退货物流
     */
    Boolean updateByBo(OrderRefundLogisticsBo bo);

    /**
     * 校验并批量删除售后退货物流信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
