package com.zsmall.order.biz.aspect.bussiness.aspectj;

import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.order.biz.aspect.bussiness.annotation.PullTikTok;
import com.zsmall.order.biz.aspect.bussiness.enums.TikTokBusinessEnums;
import com.zsmall.product.entity.domain.bo.productMapping.SaveSkuMappingBo;
import com.zsmall.product.entity.domain.bo.productMapping.SkuMappingBo;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/14 19:13
 */
@Slf4j
@Aspect
@Component
public class PullTikTokAspect {

    @Pointcut(value = "@annotation(com.zsmall.order.biz.aspect.bussiness.annotation.PullTikTok)")
    public void actionAspect() {
    }
    // 拿
    // @link RateLimiterAspect
    @After("@annotation(pullTikTok)")
    public void pullTikTok(JoinPoint joinPoint, PullTikTok pullTikTok) {
        Object[] args = joinPoint.getArgs();
        TikTokBusinessEnums business = pullTikTok.business();
        // 后续看业务扩展
        for (Object arg : args) {
            if (arg instanceof SaveSkuMappingBo) {
                SaveSkuMappingBo bo = (SaveSkuMappingBo) arg;
                SkuMappingBo skuMappingBo = bo.getSkuMappings().get(0);
                String channelType = skuMappingBo.getChannelType();
                if (ChannelTypeEnum.TikTok.name().equals(channelType) ) {

                }
            }
        }

    }

}
