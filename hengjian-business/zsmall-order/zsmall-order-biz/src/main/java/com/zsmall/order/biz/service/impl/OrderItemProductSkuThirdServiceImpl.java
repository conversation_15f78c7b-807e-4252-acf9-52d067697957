package com.zsmall.order.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.domain.dto.*;
import com.zsmall.common.domain.tiktok.domain.dto.address.TikTokDistrictInfo;
import com.zsmall.common.domain.tiktok.domain.dto.address.TikTokRecipientAddress;
import com.zsmall.common.enums.common.AttachmentTypeEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.OrderItemProductSku;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.dto.ErpSaleOrderDTO;
import com.zsmall.order.entity.domain.dto.ErpSaleOrderItemDTO;
import com.zsmall.order.entity.mapper.OrderItemProductSkuMapper;
import com.zsmall.product.biz.service.ProductSkuStockService;
import com.zsmall.product.entity.domain.Product;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductSkuAttachment;
import com.zsmall.product.entity.domain.ProductSkuStock;
import com.zsmall.product.entity.iservice.IProductService;
import com.zsmall.product.entity.iservice.IProductSkuAttachmentService;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.system.entity.domain.ConfZip;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.iservice.IConfZipService;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/9 17:52
 */
@RequiredArgsConstructor
@Service
@Slf4j

public class OrderItemProductSkuThirdServiceImpl extends ServiceImpl<OrderItemProductSkuMapper, OrderItemProductSku> {
    @Resource
    private IConfZipService iConfZipService;
    private final IProductSkuService productSkuService;
    private final IProductService productService;
    private final IWarehouseService warehouseService;
    @Resource
    private ProductSkuStockService productSkuStockService;
//    @XxlConf(value = "distribution.specify.warehouse.id.hj", defaultValue = "BG94930")
    @Value("${distribution.specify.warehouse.id.hj}")
    public String warehouseSystemCode;
    @Value("${distribution.specify.warehouse.id.bizArk}")
    public String bizArkWarehouseSystemCode;
    private final IProductSkuAttachmentService iProductSkuAttachmentService;

    @Resource
    private ITenantSalesChannelService iTenantSalesChannelService;

    /**
     * 功能描述：设置业务领域
     *
     * @param orderItemProductSku    订购项目 产品 SKU
     * @param orderItem              订购项目
     * @param orderReceiveFromThirdDTO 从ERP DTO接收订单
     * @param orders                 订单
     * @param itemDTO                项目 dto
     * @return {@link OrderItemProductSku }
     * <AUTHOR>
     * @date 2024/01/11
     */
    public OrderItemProductSku setBusinessField(OrderItemProductSku orderItemProductSku, OrderItem orderItem,
                                                OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders,
                                                SaleOrderItemDTO itemDTO) {
        SaleOrderItemDTO orderItemDTO = orderReceiveFromThirdDTO.getSaleOrderItemsList().get(0);
        TikTokRecipientAddress address = orderReceiveFromThirdDTO.getAddress();
        SaleOrderDetailDTO saleOrderDetails = orderReceiveFromThirdDTO.getSaleOrderDetails();
        String carrier = saleOrderDetails.getCarrier();
        orderItemProductSku.setSupplierTenantId(orderItem.getSupplierTenantId());
        orderItemProductSku.setOrderNo(orderItem.getOrderNo());
        LambdaQueryWrapper<ProductSku> lqw = new LambdaQueryWrapper<>();
        if (ChannelTypeEnum.TikTok.name().equals(orders.getChannelType().name())){
            lqw.eq(ProductSku::getProductSkuCode, itemDTO.getErpSku());
        }else {
            lqw.eq(ProductSku::getSku, itemDTO.getErpSku());
        }

        ProductSku productSku = TenantHelper.ignore(() -> productSkuService.getOne(lqw));
        Product product = TenantHelper.ignore(() -> productService.getOne(new LambdaQueryWrapper<Product>().eq(Product::getProductCode, productSku.getProductCode())));

        if (ObjectUtil.isEmpty(productSku)) {
            throw new RuntimeException("请先同步商品");
        }
        List<ProductSkuAttachment> skuAttachmentList = iProductSkuAttachmentService.queryBySkuIdAndAttachmentTypeOrderBySortAsc(productSku.getId(), AttachmentTypeEnum.Image);
        if (CollUtil.isNotEmpty(skuAttachmentList) && ObjectUtil.isNotEmpty(skuAttachmentList.get(0))) {
            ProductSkuAttachment firstImage = skuAttachmentList.get(0);
            Long ossId = firstImage.getOssId();
            String attachmentSavePath = firstImage.getAttachmentSavePath();
            String attachmentShowUrl = firstImage.getAttachmentShowUrl();
            orderItemProductSku.setImageOssId(ossId);
            orderItemProductSku.setImageSavePath(attachmentSavePath);
            orderItemProductSku.setImageShowUrl(attachmentShowUrl);
        }
        orderItemProductSku.setErpSku(orderItemDTO.getHavePrefixErpSku());
        orderItemProductSku.setOrderItemNo(orderItem.getOrderItemNo());
        orderItemProductSku.setChannelType(ChannelTypeEnum.TikTok);
        orderItemProductSku.setChannelId(orderItem.getChannelId());
        orderItemProductSku.setChannelVariantId(null);
        // 建一个恒建仓库-从sku上拿
        orderItemProductSku.setChannelWarehouseCode(null);
        orderItemProductSku.setProductCode(productSku.getProductCode());
        orderItemProductSku.setProductSkuCode(productSku.getProductSkuCode());
        orderItemProductSku.setSku(productSku.getSku());
        orderItemProductSku.setUpc(productSku.getUpc());
        // 这里的第三方不知道特指什么,理论上来说应该是要有一个映射的sku
        orderItemProductSku.setMappingSku(productSku.getErpSku());
        orderItemProductSku.setProductName(productSku.getName());
        orderItemProductSku.setDescription(product.getDescription());
        orderItemProductSku.setTenantId(orderItem.getTenantId());
        orderItemProductSku.setSpecComposeName(productSku.getSpecComposeName());
        orderItemProductSku.setSpecValName(productSku.getSpecValName());
        List<TikTokDistrictInfo> districtInfo = address.getDistrictInfo();
        String country = null;
        String state = null;
        String federalDistrict = null;
        String county = null;
        String city = null;
        String finalZipCode = null;
        for (TikTokDistrictInfo tikTokDistrictInfo : districtInfo) {

            String levelName = tikTokDistrictInfo.getAddressLevelName();
            String addressName = tikTokDistrictInfo.getAddressName();
            if ("country".equalsIgnoreCase(levelName)) {
                country = addressName;
            }
            if ("state".equalsIgnoreCase(levelName)) {
                state = addressName;
            }
            if ("Federal District".equalsIgnoreCase(levelName)) {
                federalDistrict = addressName;
            }
            if ("county".equalsIgnoreCase(levelName)) {
                county = addressName;
            }
            if ("city".equalsIgnoreCase(levelName)) {
                city = addressName;
            }

        }
        ConfZip confZip;
        confZip = iConfZipService.getStateCodeByStateName(state);
        if(ObjectUtil.isEmpty(confZip)&&ObjectUtil.isNotEmpty(federalDistrict)){
            confZip = iConfZipService.getStateCodeByStateName(federalDistrict);
        }
        if (ObjectUtil.isEmpty(confZip)) {
            confZip = iConfZipService.getStateCodeByCity(city);
        }
        finalZipCode  = confZip.getZip();
        if(ChannelTypeEnum.TikTok.equals(orders.getChannelType())){
            // tiktok 不需要指定仓库,通过邮编us匹配
            ProductSkuStock stock = productSkuStockService.getStockForDeliver(address.getRegionCode(),productSku.getProductSkuCode(), LogisticsTypeEnum.getLogisticsTypeEnumByName(orderReceiveFromThirdDTO.getLogisticsType()),orderItemDTO.getQuantity(),Boolean.FALSE, finalZipCode, carrier,orders.getTenantId() , productSku.getTenantId());
            orderItemProductSku.setSpecifyWarehouse(stock.getWarehouseSystemCode());
        }else{
            orderItemProductSku.setSpecifyWarehouse(warehouseSystemCode);
        }

        return orderItemProductSku;
    }
    public OrderItemProductSku setBusinessFieldForErp(OrderItemProductSku orderItemProductSku, OrderItem orderItem,
                                                      OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders,
                                                      SaleOrderItemDTO itemDTO) {
        SaleOrderItemDTO orderItemDTO = orderReceiveFromThirdDTO.getSaleOrderItemsList().get(0);
        TikTokRecipientAddress address = orderReceiveFromThirdDTO.getAddress();


        orderItemProductSku.setSupplierTenantId(orderItem.getSupplierTenantId());
        orderItemProductSku.setOrderNo(orderItem.getOrderNo());
        LambdaQueryWrapper<ProductSku> lqw = new LambdaQueryWrapper<>();
        if (ChannelTypeEnum.TikTok.name().equals(orders.getChannelType().name())){
            lqw.eq(ProductSku::getProductSkuCode, itemDTO.getErpSku());
        }else {
            lqw.eq(ProductSku::getSku, itemDTO.getErpSku());
        }

        ProductSku productSku = TenantHelper.ignore(() -> productSkuService.getOne(lqw));
        Product product = TenantHelper.ignore(() -> productService.getOne(new LambdaQueryWrapper<Product>().eq(Product::getProductCode, productSku.getProductCode())));

        if (ObjectUtil.isEmpty(productSku)) {
            throw new RuntimeException("请先同步商品");
        }

        List<ProductSkuAttachment> skuAttachmentList = iProductSkuAttachmentService.queryBySkuIdAndAttachmentTypeOrderBySortAsc(productSku.getId(), AttachmentTypeEnum.Image);
        if (CollUtil.isNotEmpty(skuAttachmentList) && ObjectUtil.isNotEmpty(skuAttachmentList.get(0))) {
            ProductSkuAttachment firstImage = skuAttachmentList.get(0);
            Long ossId = firstImage.getOssId();
            String attachmentSavePath = firstImage.getAttachmentSavePath();
            String attachmentShowUrl = firstImage.getAttachmentShowUrl();
            orderItemProductSku.setImageOssId(ossId);
            orderItemProductSku.setImageSavePath(attachmentSavePath);
            orderItemProductSku.setImageShowUrl(attachmentShowUrl);
        }
        orderItemProductSku.setErpSku(orderItemDTO.getHavePrefixErpSku());
        orderItemProductSku.setOrderItemNo(orderItem.getOrderItemNo());
        orderItemProductSku.setChannelType(ChannelTypeEnum.TikTok);
        orderItemProductSku.setChannelId(orderItem.getChannelId());
        orderItemProductSku.setChannelVariantId(null);
        // 建一个恒建仓库-从sku上拿
        orderItemProductSku.setChannelWarehouseCode(null);
        orderItemProductSku.setProductCode(productSku.getProductCode());
        orderItemProductSku.setProductSkuCode(productSku.getProductSkuCode());
        orderItemProductSku.setSku(productSku.getSku());
        orderItemProductSku.setUpc(productSku.getUpc());
        // 这里的第三方不知道特指什么,理论上来说应该是要有一个映射的sku
        orderItemProductSku.setMappingSku(productSku.getErpSku());
        orderItemProductSku.setProductName(productSku.getName());
        orderItemProductSku.setDescription(product.getDescription());
        orderItemProductSku.setTenantId(orderItem.getTenantId());
        orderItemProductSku.setSpecComposeName(productSku.getSpecComposeName());
        orderItemProductSku.setSpecValName(productSku.getSpecValName());
        orderItemProductSku.setSpecifyWarehouse(warehouseSystemCode);

        return orderItemProductSku;
    }

    /**
     * 功能描述：将业务字段设置为打开
     *
     * @param orderItemProductSku      订单项产品sku
     * @param orderItem                订单项目
     * @param orderReceiveFromThirdDTO 从第三dto接收订单
     * @param orders                   订单
     * @param itemDTO                  项目dto
     * @return {@link OrderItemProductSku }
     * <AUTHOR>
     * @date 2024/05/13
     */
    public OrderItemProductSku setBusinessFieldForOpen(OrderItemProductSku orderItemProductSku, OrderItem orderItem,
                                                OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders,
                                                SaleOrderItemDTO itemDTO) {
        SaleOrderItemDTO orderItemDTO = orderReceiveFromThirdDTO.getSaleOrderItemsList().get(0);

        orderItemProductSku.setSupplierTenantId(orderItem.getSupplierTenantId());
        orderItemProductSku.setOrderNo(orderItem.getOrderNo());
        LambdaQueryWrapper<ProductSku> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ProductSku::getProductSkuCode, itemDTO.getItemNo());

        ProductSku productSku = TenantHelper.ignore(() -> productSkuService.getOne(lqw));
        Product product = TenantHelper.ignore(() -> productService.getOne(new LambdaQueryWrapper<Product>().eq(Product::getProductCode, productSku.getProductCode())));

        if (ObjectUtil.isEmpty(productSku)) {
            throw new RuntimeException("请先同步商品");
        }
        List<ProductSkuAttachment> skuAttachmentList = iProductSkuAttachmentService.queryBySkuIdAndAttachmentTypeOrderBySortAsc(productSku.getId(), AttachmentTypeEnum.Image);
        if (CollUtil.isNotEmpty(skuAttachmentList) && ObjectUtil.isNotEmpty(skuAttachmentList.get(0))) {
            ProductSkuAttachment firstImage = skuAttachmentList.get(0);
            Long ossId = firstImage.getOssId();
            String attachmentSavePath = firstImage.getAttachmentSavePath();
            String attachmentShowUrl = firstImage.getAttachmentShowUrl();
            orderItemProductSku.setImageOssId(ossId);
            orderItemProductSku.setImageSavePath(attachmentSavePath);
            orderItemProductSku.setImageShowUrl(attachmentShowUrl);
        }
        orderItemProductSku.setErpSku(orderItemDTO.getHavePrefixErpSku());
        orderItemProductSku.setOrderItemNo(orderItem.getOrderItemNo());
        orderItemProductSku.setChannelType(ChannelTypeEnum.TikTok);
        orderItemProductSku.setChannelId(orderItem.getChannelId());
        orderItemProductSku.setChannelVariantId(null);
        // 建一个恒建仓库-从sku上拿
        orderItemProductSku.setChannelWarehouseCode(null);
        orderItemProductSku.setProductCode(productSku.getProductCode());
        orderItemProductSku.setProductSkuCode(productSku.getProductSkuCode());
        orderItemProductSku.setSku(productSku.getSku());
        orderItemProductSku.setUpc(productSku.getUpc());
        // 这里的第三方不知道特指什么,理论上来说应该是要有一个映射的sku
        orderItemProductSku.setMappingSku(productSku.getErpSku());
        orderItemProductSku.setProductName(productSku.getName());
        orderItemProductSku.setDescription(product.getDescription());
        orderItemProductSku.setTenantId(orderItem.getTenantId());
        orderItemProductSku.setSpecComposeName(productSku.getSpecComposeName());
        orderItemProductSku.setSpecValName(productSku.getSpecValName());
        //自提订单直接使用客户指定的仓库
        if (ObjectUtil.equal(orderReceiveFromThirdDTO.getLogisticsType(),LogisticsTypeEnum.PickUp.name())){
            String warehouseCode = orderReceiveFromThirdDTO.getSaleOrderDetails().getWarehouseCode();
            orderItemProductSku.setSpecifyWarehouse(warehouseCode);
            //查询仓库唯一编码
            String warehouseSystemCode = warehouseService.queryByWarehouse(warehouseCode, orderItem.getSupplierTenantId());
            orderItemProductSku.setWarehouseSystemCode(warehouseSystemCode);
        }else {
            String warehouseSystemCode = productSkuStockService.getWarehouseSystemCode(productSku,orderReceiveFromThirdDTO,orderItemDTO,Boolean.FALSE);
            orderItemProductSku.setSpecifyWarehouse(warehouseSystemCode);
        }
        return orderItemProductSku;
    }

    public OrderItemProductSku setBusinessFieldForTemu(OrderItemProductSku orderItemProductSku, OrderItem orderItem,
                                                       TemuOrderDTO temuOrderDTO, Orders orders,
                                                       TemuOrderItemDTO temuOrderItemDTO) {
        orderItemProductSku.setSupplierTenantId(orderItem.getSupplierTenantId());
        orderItemProductSku.setOrderNo(orderItem.getOrderNo());
//        LambdaQueryWrapper<ProductSku> lqw = new LambdaQueryWrapper<>();
//        lqw.eq(ProductSku::getSku, temuOrderItemDTO.getSku());
//
//        ProductSku productSku = TenantHelper.ignore(() -> productSkuService.getOne(lqw));

        ProductSku productSku = new ProductSku();
        if(StringUtils.isNotEmpty(temuOrderItemDTO.getProductSkuCode())){
            productSku = TenantHelper.ignore(() -> productSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, temuOrderItemDTO.getProductSkuCode())));
        }
//        if (ObjectUtil.isEmpty(productSku)) {
//            throw new RuntimeException("请先同步商品");
//        }
        if(ObjectUtil.isNotNull(productSku)){
            List<ProductSkuAttachment> skuAttachmentList = iProductSkuAttachmentService.queryBySkuIdAndAttachmentTypeOrderBySortAsc(productSku.getId(), AttachmentTypeEnum.Image);
            if (CollUtil.isNotEmpty(skuAttachmentList) && ObjectUtil.isNotEmpty(skuAttachmentList.get(0))) {
                ProductSkuAttachment firstImage = skuAttachmentList.get(0);
                Long ossId = firstImage.getOssId();
                String attachmentSavePath = firstImage.getAttachmentSavePath();
                String attachmentShowUrl = firstImage.getAttachmentShowUrl();
                orderItemProductSku.setImageOssId(ossId);
                orderItemProductSku.setImageSavePath(attachmentSavePath);
                orderItemProductSku.setImageShowUrl(attachmentShowUrl);
            }
            ProductSku finalProductSku = productSku;
            Product product = TenantHelper.ignore(() -> productService.getOne(new LambdaQueryWrapper<Product>().eq(Product::getProductCode, finalProductSku.getProductCode())));
            orderItemProductSku.setProductCode(productSku.getProductCode());
            orderItemProductSku.setProductSkuCode(productSku.getProductSkuCode());
            orderItemProductSku.setSku(productSku.getSku());
            orderItemProductSku.setUpc(productSku.getUpc());
            orderItemProductSku.setMappingSku(productSku.getErpSku());
            orderItemProductSku.setProductName(productSku.getName());
            orderItemProductSku.setSpecComposeName(productSku.getSpecComposeName());
            orderItemProductSku.setSpecValName(productSku.getSpecValName());
            if(null != product){
                orderItemProductSku.setDescription(product.getDescription());
            }
        }
        orderItemProductSku.setErpSku(temuOrderItemDTO.getSku());
        orderItemProductSku.setOrderItemNo(orderItem.getOrderItemNo());
        orderItemProductSku.setChannelType(ChannelTypeEnum.Temu);
        orderItemProductSku.setChannelId(orderItem.getChannelId());
        orderItemProductSku.setChannelVariantId(null);
        // 建一个恒建仓库-从sku上拿
        orderItemProductSku.setChannelWarehouseCode(null);

        // 这里的第三方不知道特指什么,理论上来说应该是要有一个映射的sku
        orderItemProductSku.setTenantId(orderItem.getTenantId());

//        if(ChannelTypeEnum.TikTok.equals(orders.getChannelType())){
//            orderItemProductSku.setSpecifyWarehouse(bizArkWarehouseSystemCode);
//        }else{
//            orderItemProductSku.setSpecifyWarehouse(warehouseSystemCode);
//        }
//        orderItemProductSku.setSpecifyWarehouse(warehouseSystemCode);

        return orderItemProductSku;
    }


    public OrderItemProductSku setBusinessFieldForAmazonVc(OrderItemProductSku orderItemProductSku, OrderItem orderItem,
                                                           AmazonVCOrderMessageDTO amazonVCOrderMessageDTO, Orders orders,
                                                           AmazonVCOrderItemMessageDTO amazonVCOrderItemMessageDTO) {
        orderItemProductSku.setSupplierTenantId(orderItem.getSupplierTenantId());
        orderItemProductSku.setOrderNo(orderItem.getOrderNo());
//        LambdaQueryWrapper<ProductSku> lqw = new LambdaQueryWrapper<>();
//        lqw.eq(ProductSku::getSku, temuOrderItemDTO.getSku());
//
//        ProductSku productSku = TenantHelper.ignore(() -> productSkuService.getOne(lqw));

        ProductSku productSku = new ProductSku();
        if(StringUtils.isNotEmpty(amazonVCOrderItemMessageDTO.getProductSkuCode())){
            productSku = TenantHelper.ignore(() -> productSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, amazonVCOrderItemMessageDTO.getProductSkuCode())));
        }
//        if (ObjectUtil.isEmpty(productSku)) {
//            throw new RuntimeException("请先同步商品");
//        }
        if(ObjectUtil.isNotNull(productSku)){
            List<ProductSkuAttachment> skuAttachmentList = iProductSkuAttachmentService.queryBySkuIdAndAttachmentTypeOrderBySortAsc(productSku.getId(), AttachmentTypeEnum.Image);
            if (CollUtil.isNotEmpty(skuAttachmentList) && ObjectUtil.isNotEmpty(skuAttachmentList.get(0))) {
                ProductSkuAttachment firstImage = skuAttachmentList.get(0);
                Long ossId = firstImage.getOssId();
                String attachmentSavePath = firstImage.getAttachmentSavePath();
                String attachmentShowUrl = firstImage.getAttachmentShowUrl();
                orderItemProductSku.setImageOssId(ossId);
                orderItemProductSku.setImageSavePath(attachmentSavePath);
                orderItemProductSku.setImageShowUrl(attachmentShowUrl);
            }
            ProductSku finalProductSku = productSku;
            Product product = TenantHelper.ignore(() -> productService.getOne(new LambdaQueryWrapper<Product>().eq(Product::getProductCode, finalProductSku.getProductCode())));
            orderItemProductSku.setProductCode(productSku.getProductCode());
            orderItemProductSku.setProductSkuCode(productSku.getProductSkuCode());
            orderItemProductSku.setSku(productSku.getSku());
            orderItemProductSku.setUpc(productSku.getUpc());
            orderItemProductSku.setMappingSku(productSku.getErpSku());
            orderItemProductSku.setProductName(productSku.getName());
            orderItemProductSku.setSpecComposeName(productSku.getSpecComposeName());
            orderItemProductSku.setSpecValName(productSku.getSpecValName());
            if(null != product){
                orderItemProductSku.setDescription(product.getDescription());
            }
        }
        orderItemProductSku.setErpSku(amazonVCOrderItemMessageDTO.getSku());
        orderItemProductSku.setOrderItemNo(orderItem.getOrderItemNo());
        orderItemProductSku.setChannelType(ChannelTypeEnum.Temu);
        orderItemProductSku.setChannelId(orderItem.getChannelId());
        orderItemProductSku.setChannelVariantId(null);
        // 建一个恒建仓库-从sku上拿
//        orderItemProductSku.setChannelWarehouseCode(null);

        // 这里的第三方不知道特指什么,理论上来说应该是要有一个映射的sku
        orderItemProductSku.setTenantId(orderItem.getTenantId());
//        orderItemProductSku.setWarehouseSystemCode(amazonVCOrderMessageDTO.getWarehouse_code());
        orderItemProductSku.setChannelWarehouseCode(amazonVCOrderMessageDTO.getWarehouse_code());

//        if(ChannelTypeEnum.TikTok.equals(orders.getChannelType())){
//            orderItemProductSku.setSpecifyWarehouse(bizArkWarehouseSystemCode);
//        }else{
//            orderItemProductSku.setSpecifyWarehouse(warehouseSystemCode);
//        }
//        orderItemProductSku.setSpecifyWarehouse(warehouseSystemCode);

        return orderItemProductSku;
    }


    public OrderItemProductSku setBusinessFieldForEc(OrderItemProductSku orderItemProductSku, OrderItem orderItem, EcOrderMessageDTO ecOrderMessageDTO, Orders orders,EcOrderItemMessageDTO ecOrderItemMessageDTO) {
        orderItemProductSku.setSupplierTenantId(orderItem.getSupplierTenantId());
        orderItemProductSku.setOrderNo(orderItem.getOrderNo());
//        LambdaQueryWrapper<ProductSku> lqw = new LambdaQueryWrapper<>();
//        lqw.eq(ProductSku::getSku, temuOrderItemDTO.getSku());
//
//        ProductSku productSku = TenantHelper.ignore(() -> productSkuService.getOne(lqw));

        ProductSku productSku = new ProductSku();
        if(StringUtils.isNotEmpty(ecOrderItemMessageDTO.getProductSkuCode())){
            productSku = TenantHelper.ignore(() -> productSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, ecOrderItemMessageDTO.getProductSkuCode())));
        }
//        if (ObjectUtil.isEmpty(productSku)) {
//            throw new RuntimeException("请先同步商品");
//        }
        if(ObjectUtil.isNotNull(productSku)){
            List<ProductSkuAttachment> skuAttachmentList = iProductSkuAttachmentService.queryBySkuIdAndAttachmentTypeOrderBySortAsc(productSku.getId(), AttachmentTypeEnum.Image);
            if (CollUtil.isNotEmpty(skuAttachmentList) && ObjectUtil.isNotEmpty(skuAttachmentList.get(0))) {
                ProductSkuAttachment firstImage = skuAttachmentList.get(0);
                Long ossId = firstImage.getOssId();
                String attachmentSavePath = firstImage.getAttachmentSavePath();
                String attachmentShowUrl = firstImage.getAttachmentShowUrl();
                orderItemProductSku.setImageOssId(ossId);
                orderItemProductSku.setImageSavePath(attachmentSavePath);
                orderItemProductSku.setImageShowUrl(attachmentShowUrl);
            }
            ProductSku finalProductSku = productSku;
            Product product = TenantHelper.ignore(() -> productService.getOne(new LambdaQueryWrapper<Product>().eq(Product::getProductCode, finalProductSku.getProductCode())));
            orderItemProductSku.setProductCode(productSku.getProductCode());
            orderItemProductSku.setProductSkuCode(productSku.getProductSkuCode());
            orderItemProductSku.setSku(productSku.getSku());
            orderItemProductSku.setUpc(productSku.getUpc());
            orderItemProductSku.setMappingSku(productSku.getErpSku());
            orderItemProductSku.setProductName(productSku.getName());
            orderItemProductSku.setSpecComposeName(productSku.getSpecComposeName());
            orderItemProductSku.setSpecValName(productSku.getSpecValName());
            if(null != product){
                orderItemProductSku.setDescription(product.getDescription());
            }
        }
        orderItemProductSku.setErpSku(ecOrderItemMessageDTO.getSku());
        orderItemProductSku.setOrderItemNo(orderItem.getOrderItemNo());
        orderItemProductSku.setChannelType(ChannelTypeEnum.Temu);
        orderItemProductSku.setChannelId(orderItem.getChannelId());
        orderItemProductSku.setChannelVariantId(null);
        // 建一个恒建仓库-从sku上拿
        orderItemProductSku.setChannelWarehouseCode(null);

        // 这里的第三方不知道特指什么,理论上来说应该是要有一个映射的sku
        orderItemProductSku.setTenantId(orderItem.getTenantId());
//        orderItemProductSku.setWarehouseSystemCode(ecOrderMessageDTO.getWarehouse_code());
        orderItemProductSku.setChannelWarehouseCode(ecOrderMessageDTO.getWarehouse_code());

//        if(ChannelTypeEnum.TikTok.equals(orders.getChannelType())){
//            orderItemProductSku.setSpecifyWarehouse(bizArkWarehouseSystemCode);
//        }else{
//            orderItemProductSku.setSpecifyWarehouse(warehouseSystemCode);
//        }
//        orderItemProductSku.setSpecifyWarehouse(warehouseSystemCode);

        return orderItemProductSku;
    }


    public OrderItemProductSku setBusinessFieldForAmazonSc(OrderItemProductSku orderItemProductSku, OrderItem orderItem,
                                                           AmazonScOrderDTO amazonScOrderDTO, Orders orders,
                                                           AmazonScOrderItemDTO amazonScOrderItemDTO) {
        orderItemProductSku.setSupplierTenantId(orderItem.getSupplierTenantId());
        orderItemProductSku.setOrderNo(orderItem.getOrderNo());

        ProductSku productSku = new ProductSku();
        if(StringUtils.isNotEmpty(amazonScOrderItemDTO.getProductSkuCode())){
            productSku = TenantHelper.ignore(() -> productSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, amazonScOrderItemDTO.getProductSkuCode())));
        }
        if(ObjectUtil.isNotNull(productSku)){
            List<ProductSkuAttachment> skuAttachmentList = iProductSkuAttachmentService.queryBySkuIdAndAttachmentTypeOrderBySortAsc(productSku.getId(), AttachmentTypeEnum.Image);
            if (CollUtil.isNotEmpty(skuAttachmentList) && ObjectUtil.isNotEmpty(skuAttachmentList.get(0))) {
                ProductSkuAttachment firstImage = skuAttachmentList.get(0);
                Long ossId = firstImage.getOssId();
                String attachmentSavePath = firstImage.getAttachmentSavePath();
                String attachmentShowUrl = firstImage.getAttachmentShowUrl();
                orderItemProductSku.setImageOssId(ossId);
                orderItemProductSku.setImageSavePath(attachmentSavePath);
                orderItemProductSku.setImageShowUrl(attachmentShowUrl);
            }
            ProductSku finalProductSku = productSku;
            Product product = TenantHelper.ignore(() -> productService.getOne(new LambdaQueryWrapper<Product>().eq(Product::getProductCode, finalProductSku.getProductCode())));
            orderItemProductSku.setProductCode(productSku.getProductCode());
            orderItemProductSku.setProductSkuCode(productSku.getProductSkuCode());
            orderItemProductSku.setSku(productSku.getSku());
            orderItemProductSku.setUpc(productSku.getUpc());
            orderItemProductSku.setMappingSku(productSku.getErpSku());
            orderItemProductSku.setProductName(productSku.getName());
            orderItemProductSku.setSpecComposeName(productSku.getSpecComposeName());
            orderItemProductSku.setSpecValName(productSku.getSpecValName());
            if(null != product){
                orderItemProductSku.setDescription(product.getDescription());
            }
        }
        orderItemProductSku.setErpSku(amazonScOrderItemDTO.getSku());
        orderItemProductSku.setOrderItemNo(orderItem.getOrderItemNo());
        orderItemProductSku.setChannelType(ChannelTypeEnum.Temu);
        orderItemProductSku.setChannelId(orderItem.getChannelId());
        orderItemProductSku.setChannelVariantId(null);
        // 建一个恒建仓库-从sku上拿
        orderItemProductSku.setChannelWarehouseCode(null);
        // 这里的第三方不知道特指什么,理论上来说应该是要有一个映射的sku
        orderItemProductSku.setTenantId(orderItem.getTenantId());
        return orderItemProductSku;
    }

    /**
     * 功能描述：设置业务领域
     *
     * @param orderItemProductSku 订购项目 产品 SKU
     * @param orderItem           订购项目
     * @param erpSaleOrderDTO     ERP销售订单DTO
     * @param orders              订单
     * @param itemDTO             项目 dto
     * @return {@link OrderItemProductSku }
     * <AUTHOR>
     * @date 2024/01/12
     */
    public OrderItemProductSku setBusinessField(OrderItemProductSku orderItemProductSku, OrderItem orderItem,
                                                ErpSaleOrderDTO erpSaleOrderDTO, Orders orders,
                                                ErpSaleOrderItemDTO itemDTO) {
        ErpSaleOrderItemDTO orderItemDTO = erpSaleOrderDTO.getSaleOrderItemsList().get(0);


        orderItemProductSku.setSupplierTenantId(orderItem.getSupplierTenantId());
        orderItemProductSku.setOrderNo(orderItem.getOrderNo());
        LambdaQueryWrapper<ProductSku> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ProductSku::getSku, itemDTO.getSellerSku());
        ProductSku productSku = TenantHelper.ignore(() -> productSkuService.getOne(lqw));
        Product product = TenantHelper.ignore(() -> productService.getOne(new LambdaQueryWrapper<Product>().eq(Product::getProductCode, productSku.getProductCode())));


        if (ObjectUtil.isEmpty(productSku)) {
            throw new RuntimeException("请先同步商品");
        }
        List<ProductSkuAttachment> skuAttachmentList = iProductSkuAttachmentService.queryBySkuIdAndAttachmentTypeOrderBySortAsc(productSku.getId(), AttachmentTypeEnum.Image);
        if (CollUtil.isNotEmpty(skuAttachmentList) && ObjectUtil.isNotEmpty(skuAttachmentList.get(0))) {
            ProductSkuAttachment firstImage = skuAttachmentList.get(0);
            Long ossId = firstImage.getOssId();
            String attachmentSavePath = firstImage.getAttachmentSavePath();
            String attachmentShowUrl = firstImage.getAttachmentShowUrl();
            orderItemProductSku.setImageOssId(ossId);
            orderItemProductSku.setImageSavePath(attachmentSavePath);
            orderItemProductSku.setImageShowUrl(attachmentShowUrl);
        }

        orderItemProductSku.setSku(orderItemDTO.getSellerSku());
        orderItemProductSku.setOrderItemNo(orderItem.getOrderItemNo());
        orderItemProductSku.setChannelType(ChannelTypeEnum.MultiChannel);
        orderItemProductSku.setChannelId(orderItem.getChannelId());
        orderItemProductSku.setChannelVariantId(null);
        // 建一个恒建仓库-从sku上拿
        orderItemProductSku.setChannelWarehouseCode(null);
        orderItemProductSku.setProductCode(productSku.getProductCode());
        orderItemProductSku.setProductSkuCode(productSku.getProductSkuCode());
        orderItemProductSku.setSku(productSku.getSku());
        orderItemProductSku.setUpc(productSku.getUpc());
        // 这里的第三方不知道特指什么,理论上来说应该是要有一个映射的sku
        orderItemProductSku.setMappingSku(productSku.getErpSku());
        orderItemProductSku.setProductName(productSku.getName());
        orderItemProductSku.setDescription(product.getDescription());

        orderItemProductSku.setTenantId(orderItem.getTenantId());
        orderItemProductSku.setSpecComposeName(productSku.getSpecComposeName());
        orderItemProductSku.setSpecValName(productSku.getSpecValName());
        orderItemProductSku.setSpecifyWarehouse(warehouseSystemCode);


        return orderItemProductSku;
    }

    /**
     * 功能描述：设置业务字段
     *
     * @param orderItemProductSku      订单项产品sku
     * @param orderReceiveFromThirdDTO 从第三dto接收订单
     * @param channelTypeEnum          通道类型枚举
     * <AUTHOR>
     * @date 2024/07/03
     */
    public void setBusinessField(OrderItemProductSku orderItemProductSku, OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, ChannelTypeEnum channelTypeEnum) {
        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getByChannelFlag(orderReceiveFromThirdDTO.getThirdChannelFlag(), channelTypeEnum.name());
        SaleOrderDetailDTO saleOrderDetails = orderReceiveFromThirdDTO.getSaleOrderDetails();
        SaleOrderItemDTO orderItemDTO = orderReceiveFromThirdDTO.getSaleOrderItemsList().get(0);
        String erpSku = orderItemDTO.getErpSku();
        if(ObjectUtil.isNotEmpty(erpSku)){
            TikTokRecipientAddress address = orderReceiveFromThirdDTO.getAddress();


                LambdaQueryWrapper<ProductSku> lqw = new LambdaQueryWrapper<>();
            if (ChannelTypeEnum.TikTok.name().equals(channelTypeEnum.name())){
                lqw.eq(ProductSku::getProductSkuCode, erpSku);
            }else {
                lqw.eq(ProductSku::getSku, erpSku);
            }

            ProductSku productSku = TenantHelper.ignore(() -> productSkuService.getOne(lqw));
            Product product = TenantHelper.ignore(() -> productService.getOne(new LambdaQueryWrapper<Product>().eq(Product::getProductCode, productSku.getProductCode())));
            orderItemProductSku.setSupplierTenantId(product.getTenantId());
            if (ObjectUtil.isEmpty(productSku)) {
                throw new RuntimeException("请先同步商品");
            }
            List<ProductSkuAttachment> skuAttachmentList = iProductSkuAttachmentService.queryBySkuIdAndAttachmentTypeOrderBySortAsc(productSku.getId(), AttachmentTypeEnum.Image);
            if (CollUtil.isNotEmpty(skuAttachmentList) && ObjectUtil.isNotEmpty(skuAttachmentList.get(0))) {
                ProductSkuAttachment firstImage = skuAttachmentList.get(0);
                Long ossId = firstImage.getOssId();
                String attachmentSavePath = firstImage.getAttachmentSavePath();
                String attachmentShowUrl = firstImage.getAttachmentShowUrl();
                orderItemProductSku.setImageOssId(ossId);
                orderItemProductSku.setImageSavePath(attachmentSavePath);
                orderItemProductSku.setImageShowUrl(attachmentShowUrl);
            }
            orderItemProductSku.setErpSku(orderItemDTO.getHavePrefixErpSku());

            orderItemProductSku.setChannelType(ChannelTypeEnum.TikTok);
            // 这个要在最外部的调用处传递,
            orderItemProductSku.setChannelId(tenantSalesChannel.getId());
            orderItemProductSku.setChannelVariantId(null);
            // 建一个恒建仓库-从sku上拿
//            orderItemProductSku.setChannelWarehouseCode(null);
            orderItemProductSku.setProductCode(productSku.getProductCode());
            orderItemProductSku.setProductSkuCode(productSku.getProductSkuCode());
            orderItemProductSku.setSku(productSku.getSku());
            orderItemProductSku.setUpc(productSku.getUpc());
            // 这里的第三方不知道特指什么,理论上来说应该是要有一个映射的sku
            orderItemProductSku.setMappingSku(productSku.getErpSku());
            orderItemProductSku.setProductName(productSku.getName());
            orderItemProductSku.setDescription(product.getDescription());
            orderItemProductSku.setTenantId(orderReceiveFromThirdDTO.getTenantId());
            orderItemProductSku.setSpecComposeName(productSku.getSpecComposeName());
            orderItemProductSku.setSpecValName(productSku.getSpecValName());
            List<TikTokDistrictInfo> districtInfo = address.getDistrictInfo();
            String country = null;
            String state = null;
            String federalDistrict = null;
            String county = null;
            String city = null;
            String finalZipCode = null;
            for (TikTokDistrictInfo tikTokDistrictInfo : districtInfo) {

                String levelName = tikTokDistrictInfo.getAddressLevelName();
                String addressName = tikTokDistrictInfo.getAddressName();
                if ("country".equalsIgnoreCase(levelName)) {
                    country = addressName;
                }
                if ("state".equalsIgnoreCase(levelName)) {
                    state = addressName;
                }
                if ("Federal District".equalsIgnoreCase(levelName)) {
                    federalDistrict = addressName;
                }
                if ("county".equalsIgnoreCase(levelName)) {
                    county = addressName;
                }
                if ("city".equalsIgnoreCase(levelName)) {
                    city = addressName;
                }

            }
            ConfZip confZip;
            confZip = iConfZipService.getStateCodeByStateName(state);
            if(ObjectUtil.isEmpty(confZip)&&ObjectUtil.isNotEmpty(federalDistrict)){
                confZip = iConfZipService.getStateCodeByStateName(federalDistrict);
            }
            if (ObjectUtil.isEmpty(confZip)) {
                confZip = iConfZipService.getStateCodeByCity(city);
            }
            finalZipCode  = confZip.getZip();
            if(ChannelTypeEnum.TikTok.equals(channelTypeEnum)){
                // tiktok 不需要指定仓库,通过邮编us匹配
                ProductSkuStock stock = productSkuStockService.getStockForDeliver(address.getRegionCode(),productSku.getProductSkuCode(), LogisticsTypeEnum.getLogisticsTypeEnumByName(orderReceiveFromThirdDTO.getLogisticsType()),orderItemDTO.getQuantity(),Boolean.FALSE, finalZipCode, saleOrderDetails.getCarrier(), orderReceiveFromThirdDTO.getTenantId(),productSku.getTenantId() );
                // todo 可能有null的情况
                if(ObjectUtil.isNotEmpty(stock)){
                    orderItemProductSku.setSpecifyWarehouse(stock.getWarehouseSystemCode());
                }

            }else{
                orderItemProductSku.setSpecifyWarehouse(warehouseSystemCode);
            }
        }

    }
}
