package com.zsmall.order.biz.service;

import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.order.entity.domain.OrderItemPrice;
import com.zsmall.order.entity.domain.bo.OrderItemPriceBo;
import com.zsmall.order.entity.domain.vo.OrderItemPriceVo;

import java.util.Collection;
import java.util.List;

/**
 * 子订单价格Service接口
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
public interface OrderItemPriceService {

    /**
     * 查询子订单价格
     */
    OrderItemPriceVo queryById(Long id);

    /**
     * 查询子订单价格列表
     */
    TableDataInfo<OrderItemPriceVo> queryPageList(OrderItemPriceBo bo, PageQuery pageQuery);

    /**
     * 查询子订单价格列表
     */
    List<OrderItemPriceVo> queryList(OrderItemPriceBo bo);

    /**
     * 新增子订单价格
     */
    Boolean insertByBo(OrderItemPriceBo bo);

    /**
     * 修改子订单价格
     */
    Boolean updateByBo(OrderItemPriceBo bo);

    /**
     * 校验并批量删除子订单价格信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    void saveOrSetNUll(List<OrderItemPrice> itemPrices, Integer exceptionCode);

    void saveOrSetNull(List<OrderItemPrice> orderItemPriceList);

    void updateSetNUll(List<String> itemNos, Integer value);

    void saveUpdateOrSetNUll(List<OrderItemPrice> itemPrices, Integer exceptionCode);
}
