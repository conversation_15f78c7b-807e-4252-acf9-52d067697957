package com.zsmall.order.biz.service;

import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.common.domain.dto.*;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.bo.OrderItemBo;
import com.zsmall.order.entity.domain.dto.ErpSaleOrderDTO;
import com.zsmall.order.entity.domain.dto.ErpSaleOrderItemDTO;
import com.zsmall.order.entity.domain.vo.OrderItemVo;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;

/**
 * 子订单Service接口
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
public interface OrderItemService {

    /**
     * 查询子订单
     */
    OrderItemVo queryById(Long id);

    /**
     * 查询子订单列表
     */
    TableDataInfo<OrderItemVo> queryPageList(OrderItemBo bo, PageQuery pageQuery);

    /**
     * 查询子订单列表
     */
    List<OrderItemVo> queryList(OrderItemBo bo);

    /**
     * 新增子订单
     */
    Boolean insertByBo(OrderItemBo bo);

    /**
     * 修改子订单
     */
    Boolean updateByBo(OrderItemBo bo);

    /**
     * 校验并批量删除子订单信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 功能描述：设置订单业务字段
     *
     * @param orderItem              订购项目
     * @param orderReceiveFromThirdDTO 从ERP DTO接收订单
     * @param orders                 订单
     * @param saleOrderItemDTO       销售订单项目 DTO
     * @return {@link OrderItem }
     * <AUTHOR>
     * @date 2024/01/11
     */
    OrderItem setOrderBusinessField(OrderItem orderItem, OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders, SaleOrderItemDTO saleOrderItemDTO);

    OrderItem setOrderBusinessFieldForOpen(OrderItem orderItem, OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders, SaleOrderItemDTO saleOrderItemDTO);

    OrderItem setOrderBusinessFieldForTemu(OrderItem orderItem, TemuOrderDTO temuOrderDTO, Orders orders, TemuOrderItemDTO temuOrderItemDTO);

    OrderItem setOrderBusinessFieldForAmazonVc(OrderItem orderItem, AmazonVCOrderMessageDTO amazonVCOrderMessageDTO, Orders orders, AmazonVCOrderItemMessageDTO amazonVCOrderItemMessageDTO);

    OrderItem setOrderBusinessFieldForEc(OrderItem orderItem, EcOrderMessageDTO ecOrderMessageDTO, Orders orders, EcOrderItemMessageDTO ecOrderItemMessageDTO);

    OrderItem setOrderBusinessFieldForAmazonSc(OrderItem orderItem, AmazonScOrderDTO amazonScOrderDTO, Orders orders, AmazonScOrderItemDTO amazonScOrderItemDTO);

    OrderItem setChannelTag(OrderItem orderItem, OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders, SaleOrderItemDTO saleOrderItemDTO);
    OrderItem setChannelTagForOpen(OrderItem orderItem, OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders, SaleOrderItemDTO saleOrderItemDTO);

    OrderItem setChannelTagForTemu(OrderItem orderItem, TemuOrderDTO temuOrderDTO, Orders orders, TemuOrderItemDTO temuOrderItemDTO);
    OrderItem setChannelTagForAmazonVc(OrderItem orderItem, AmazonVCOrderMessageDTO amazonVCOrderMessageDTO, Orders orders, AmazonVCOrderItemMessageDTO amazonVCOrderItemMessageDTO);
    OrderItem setChannelTagForAmazonEc(OrderItem orderItem, EcOrderMessageDTO ecOrderMessageDTO, Orders orders, EcOrderItemMessageDTO ecOrderItemMessageDTO);
    OrderItem setChannelTagForAmazonSc(OrderItem orderItem, AmazonScOrderDTO amazonScOrderDTO, Orders orders, AmazonScOrderItemDTO amazonScOrderItemDTO);

    OrderItem setOrderBusinessField(OrderItem orderItem, ErpSaleOrderDTO erpSaleOrderDTO, Orders orders, ErpSaleOrderItemDTO saleOrderItemDTO);

    OrderItem setChannelTag(OrderItem orderItem, ErpSaleOrderDTO erpSaleOrderDTO, Orders orders, ErpSaleOrderItemDTO saleOrderItemDTO);

    /**
     * 功能描述：设置订单业务字段 V3专用
     *
     * @param orderItem                订单项目
     * @param orderReceiveFromThirdDTO 从第三dto接收订单
     * @param channelTypeEnum          通道类型枚举
     * @param saleOrderItemDTO         销售订单项目dto
     * <AUTHOR>
     * @date 2024/07/03
     */
    void setOrderBusinessField(OrderItem orderItem, OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, ChannelTypeEnum channelTypeEnum, SaleOrderItemDTO saleOrderItemDTO);

    /**
     * 功能描述：设置通道标签 V3专用
     *
     * @param orderItem                订单项目
     * @param orderReceiveFromThirdDTO 从第三dto接收订单
     * @param channelTypeEnum          通道类型枚举
     * @param saleOrderItemDTO         销售订单项目dto
     * <AUTHOR>
     * @date 2024/07/03
     */
    void setChannelTag(OrderItem orderItem, OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, ChannelTypeEnum channelTypeEnum, SaleOrderItemDTO saleOrderItemDTO);

    /**
     * 功能描述：更新或设置为空 实际orderItemUpdateList长度为1
     *
     * @param orderItemUpdateList 订单项目更新列表
     * @param exceptionCode       异常代码
     * <AUTHOR>
     * @date 2024/07/31
     */
    void updateOrSetNUll(List<OrderItem> orderItemUpdateList, Integer exceptionCode);

    /**
     * 功能描述：更新批次或设置为空
     *
     * @param orderItemNewList 订单项目新列表
     * @param codesMap         代码映射
     * <AUTHOR>
     * @date 2024/07/31
     */
    void updateBatchOrSetNUll(List<OrderItem> orderItemNewList, HashMap<String, Integer> codesMap);

    void setSiteField(OrderItem orderItem, OrderReceiveFromThirdDTO orderReceiveFromThirdDTO);
}
