package com.zsmall.order.biz.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.orderImportRecord.ImportStateEnum;
import com.zsmall.order.biz.service.OrderItemTrackingImportRecordService;
import com.zsmall.order.entity.domain.OrderItemTrackingImportRecord;
import com.zsmall.order.entity.iservice.OrderCodeGenerator;
import com.zsmall.order.entity.mapper.OrderItemTrackingImportRecordMapper;
import com.zsmall.system.entity.domain.DownloadRecord;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
* <AUTHOR>
* @description 针对表【order_item_tracking_import_record】的数据库操作Service实现
* @createDate 2025-03-28 14:00:33
*/
@Service
public class OrderItemTrackingImportRecordServiceImpl extends ServiceImpl<OrderItemTrackingImportRecordMapper, OrderItemTrackingImportRecord>
    implements OrderItemTrackingImportRecordService {

    @Resource
    private OrderCodeGenerator orderCodeGenerator;
    @Override
    public OrderItemTrackingImportRecord initialize(DownloadRecord newRecord, int dataRowCount) {
        String importRecordNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderItemTrackingRecordNo);
        OrderItemTrackingImportRecord orderItemTrackingImportRecord = new OrderItemTrackingImportRecord();
        orderItemTrackingImportRecord.setImportRecordNo(importRecordNo);
        orderItemTrackingImportRecord.setImportFileName(newRecord.getFileName());
        orderItemTrackingImportRecord.setImportTrackingNum(dataRowCount);
        orderItemTrackingImportRecord.setImportState(ImportStateEnum.Importing);
        orderItemTrackingImportRecord.setDelFlag("0");
        if(ObjectUtil.isNotEmpty(LoginHelper.getUserId())){
            orderItemTrackingImportRecord.setCreateBy(LoginHelper.getUserId());
            orderItemTrackingImportRecord.setUpdateBy(LoginHelper.getUserId());
        }else {
            orderItemTrackingImportRecord.setCreateBy(01L);
        }
        return orderItemTrackingImportRecord;
    }
}




