package com.zsmall.order.biz.manager;

import com.zsmall.common.annotaion.Manager;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.product.entity.iservice.IProductSkuService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * lty notes 订单和订单项复合管理层
 *
 * <AUTHOR> Theo
 * @create 2023/12/18 17:15
 */
@Slf4j
@Manager
@RequiredArgsConstructor
public class OrderAndItemManager {


    private final IOrdersService iOrdersService;
    private final IOrderItemService iOrderItemService;
    private final IProductSkuService iProductSkuService;
    private final IOrderItemProductSkuService iOrderItemProductSkuService;
    private final IOrderLogisticsInfoService iOrderLogisticsInfoService;
    private final IOrderAddressInfoService iOrderAddressInfoService;
    private final IOrderItemPriceService iOrderItemPriceService;

    /**
     * 功能描述：保存订单和项目
     *
     * @param orders    订单
     * @param orderItem 订购项目
     * @param skus      SKU系列
     * @return {@link Boolean }
     * <AUTHOR>
     * @date 2023/12/28
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrderAndItem(Orders orders, List<OrderItem> orderItem, List<OrderItemProductSku> skus) {
        iOrdersService.save(orders);
        orderItem.forEach(item -> item.setOrderId(orders.getId()));
        iOrderItemService.saveBatch(orderItem);
        for (OrderItem item : orderItem) {
            for (OrderItemProductSku sku : skus) {
                if(item.getOrderItemNo().equals(sku.getOrderItemNo())){
                    sku.setOrderItemId(item.getId());
                    sku.setTenantId(orders.getTenantId());
                }
            }
        }

        iOrderItemProductSkuService.saveBatch(skus);
        return true;
    }


    /**
     * 功能描述：保存订单 关于
     *
     * @param logisticsInfo 物流信息
     * @param addressInfo   地址信息
     * @param itemPrices    商品价格
     * <AUTHOR>
     * @date 2023/12/28
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrderAbout(Object logisticsInfo, Object addressInfo, List<OrderItemPrice> itemPrices) {
        OrderLogisticsInfo info = (OrderLogisticsInfo) logisticsInfo;
        OrderAddressInfo address = (OrderAddressInfo) addressInfo;
        iOrderLogisticsInfoService.save(info);
        iOrderAddressInfoService.save(address);
        iOrderItemPriceService.saveBatch(itemPrices);

    }
}
