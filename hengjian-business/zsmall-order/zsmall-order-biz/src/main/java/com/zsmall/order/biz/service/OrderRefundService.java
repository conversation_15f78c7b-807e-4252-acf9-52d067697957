package com.zsmall.order.biz.service;

import cn.hutool.json.JSONObject;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.order.entity.domain.OrderRefundMsgReq;
import com.zsmall.order.entity.domain.bo.RefundApplyBo;
import com.zsmall.order.entity.domain.bo.SubmitRefundApplyBo;
import com.zsmall.order.entity.domain.bo.refund.RefundApplyHandleBo;
import com.zsmall.order.entity.domain.bo.refund.RefundBo;
import com.zsmall.order.entity.domain.bo.refund.SupplementRefundInfoBo;
import com.zsmall.order.entity.domain.dto.OrderRefundMsgDTO;
import com.zsmall.order.entity.domain.export.OrderRefundExportDTO;
import com.zsmall.order.entity.domain.vo.RefundApplyVo;
import com.zsmall.order.entity.domain.vo.RefundDetailVo;
import com.zsmall.order.entity.domain.vo.refundOrder.RefundReasonListVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 售后申请主单Service接口
 *
 * <AUTHOR>
 * @date 2023-06-08
 */
public interface OrderRefundService {

    /**
     * 查询售后申请主单
     */
    R<RefundDetailVo> queryByRefundNo(String orderRefundNo);

    /**
     * 查询售后申请主单列表
     */
    TableDataInfo<RefundApplyVo> queryPageList(RefundApplyBo bo, PageQuery pageQuery);

    /**
     *获取退款单列表
     */
    List<RefundApplyVo> getOrderRefunds(RefundApplyBo bo);

    /**
     * 提交退款申请
     * @param bo
     * @return
     */
    R<Void> submitRefundApply(SubmitRefundApplyBo bo) throws RStatusCodeException;

    /**
     * 补全退货物流信息
     * @param bo
     * @return
     */
    R<Void> supplementRefundInfo(SupplementRefundInfoBo bo);

    /**
     * 确认收到退货
     * @param bo
     * @return
     */
    R<Void> confirmReceipt(RefundBo bo) throws RStatusCodeException;

    /**
     * 查询售后原因列表
     * @return
     */
    R<RefundReasonListVo> queryRefundReasonList();

    /**
     * 获取退款相关选项
     * @return
     */
    R<JSONObject> getRefundReasonOptions();

    /**
     * 退款申请处理
     * @param bo
     * @return
     */
    R<Void> refundApplyHandle(RefundApplyHandleBo bo) throws Exception;

    /**
     * 退款订单导出
     * @param bo
     * @param response
     */
    List<OrderRefundExportDTO> excelExport(RefundApplyBo bo, HttpServletResponse response);

    List<OrderRefundMsgDTO> getOrderRefundInformationForRefundable(List<OrderRefundMsgReq> orderRefundMsgReq);
}
