package com.zsmall.order.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.ServletUtils;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.extend.event.OSSUploadEvent;
import com.hengjian.openapi.service.ISysInfService;
import com.hengjian.system.domain.vo.SysOssVo;
import com.zsmall.common.constant.FileNameConstants;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.enums.downloadRecord.DownloadTypePlusEnum;
import com.zsmall.common.enums.downloadRecord.RecordStateEnum;
import com.zsmall.common.enums.order.LogisticsProgress;
import com.zsmall.common.enums.order.OrderStateType;
import com.zsmall.common.enums.orderTrackingRecord.LogisticsProgressType;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.properties.FileProperties;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.order.biz.service.OrderItemTrackingRecordService;
import com.zsmall.order.entity.domain.bo.OrderItemTrackingRecordBo;
import com.zsmall.order.entity.domain.dto.OrderItemTrackingDTO;
import com.zsmall.order.entity.domain.dto.TrackingPageDTO;
import com.zsmall.order.entity.domain.vo.OrderItemTrackingRecordVo;
import com.zsmall.order.entity.iservice.IOrderItemTrackingRecordService;
import com.zsmall.system.entity.domain.DownloadRecord;
import com.zsmall.system.entity.iservice.IDownloadRecordService;
import lombok.Cleanup;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.InputStream;
import java.util.*;

/**
 * 子订单物流跟踪单Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-06
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class OrderItemTrackingRecordServiceImpl implements OrderItemTrackingRecordService {

    private final IOrderItemTrackingRecordService iOrderItemTrackingRecordService;
    private final BusinessParameterService businessParameterService;
    private final IDownloadRecordService iDownloadRecordService;
    private final FileProperties fileProperties;
    private final ISysInfService sysInfService;

    /**
     * 查询子订单物流跟踪单
     */
    @Override
    public OrderItemTrackingRecordVo queryById(Long id) {
        return iOrderItemTrackingRecordService.queryById(id);
    }

    /**
     * 查询子订单物流跟踪单列表
     */
    @Override
    public TableDataInfo<OrderItemTrackingRecordVo> queryPageList(OrderItemTrackingRecordBo bo, PageQuery pageQuery) {

        log.info("调用【分页查询跟踪单】接口");
        log.info("接口请求参数：{} ", JSONUtil.toJsonStr(bo));

        Page<OrderItemTrackingRecordVo> recordPage = iOrderItemTrackingRecordService.queryPageList(bo, pageQuery);
        List<OrderItemTrackingRecordVo> records = recordPage.getRecords();
        List<OrderItemTrackingRecordVo> results = getOrderItemTrackingRecordVos(records);
        return TableDataInfo.build(results, recordPage.getTotal());
    }

    @NotNull
    private List<OrderItemTrackingRecordVo> getOrderItemTrackingRecordVos(List<OrderItemTrackingRecordVo> records) {
        List<OrderItemTrackingRecordVo> results = new ArrayList<>();

        String TRACKING_REDIRECT = businessParameterService.getValueFromString(BusinessParameterType.TRACKING_REDIRECT);
        JSONObject trackingRedirect = JSONUtil.parseObj(TRACKING_REDIRECT);

        for (OrderItemTrackingRecordVo record : records) {
            String channelAlias = record.getChannelAlias();

//            if (!ObjectUtil.equals(LoginHelper.getTenantTypeEnum(), TenantType.Distributor)) {
//                record.setProductSkuCode(record.getSku());
//            }
            Date orderTime = DateUtil.parse(record.getOrderTime());
            record.setOrderTime(DateUtil.format(orderTime, "yyyy-MM-dd HH:mm"));
            if (record.getChannelOrderTime() != null) {
                Date channelOrderTime = DateUtil.parse(record.getChannelOrderTime());
                record.setChannelOrderTime(DateUtil.format(channelOrderTime, "yyyy-MM-dd HH:mm"));
            }

            StringBuilder carrierOrTrackingNo = new StringBuilder();
            record.setChannelAlias(channelAlias);
            String logisticsName = record.getLogisticsCarrier();
            String trackingNo = record.getLogisticsTrackingNo();
            String logisticsService = record.getLogisticsService();
            if (StrUtil.isNotBlank(logisticsName)) {
                record.setLogisticsName(logisticsName);
                carrierOrTrackingNo.append(logisticsName).append("/");
            }
            if (StrUtil.isNotBlank(logisticsService)) {
                carrierOrTrackingNo.append(logisticsService).append("/");
            }
            if (StrUtil.isNotBlank(trackingNo)) {
                carrierOrTrackingNo.append(trackingNo);
            }
            record.setCarrierOrTrackingNo(carrierOrTrackingNo.toString());

            String urlTemplate = trackingRedirect.getStr(logisticsName);
            if (StrUtil.isNotBlank(urlTemplate)
                && StrUtil.isNotBlank(trackingNo)
                && StrUtil.equalsAny(logisticsName, "FedEx", "UPS")) {
                String redirectUrl = StrUtil.replace(urlTemplate, "{trackingNo}", trackingNo);
                record.setTrackingRedirectUrl(redirectUrl);
            }

            String logisticsProgress = record.getLogisticsProgress();
            LogisticsProgressType logisticsProgressType = LogisticsProgressType.fromFulfillmentName(logisticsProgress);
            if (logisticsProgressType != null) {
                record.setLogisticsProgress(logisticsProgressType.getShowName());
            }

            // 处理前端展示状态
            String orderState = record.getOrderState();
            String orderItemState = record.getOrderItemState();
            String fulfillmentProgress = record.getFulfillmentProgress();
            log.info("fulfillmentProgress = {}", fulfillmentProgress);
            String showStatus = "";
            if (StrUtil.equals(orderState, OrderStateType.Canceled.name())) {
                showStatus = "Canceled";
            } else if (StrUtil.equalsAny(orderState, OrderStateType.UnPaid.name(), OrderStateType.Pending.name(),
                OrderStateType.Failed.name())) {
                showStatus = "UnPaid";
            } else {
                if (StrUtil.equalsAny(fulfillmentProgress, LogisticsProgress.Dispatched.name(), LogisticsProgress.Fulfilled.name())) {
                    showStatus = "Shipped";
                } else {
                    showStatus = "UnShipped";
                }
            }
            record.setOrderState(showStatus);
//            record.setChannelOrderNo();

            results.add(record);
        }
        return results;
    }

    /**
     * 查询子订单物流跟踪单列表
     */
    @Override
    public R<Void> queryList(OrderItemTrackingRecordBo bo) {

        TenantType tenantType = LoginHelper.getTenantTypeEnum();
        Boolean existsed = iDownloadRecordService.existsByRecordState(RecordStateEnum.Generating);
        if (existsed) {
            return R.fail(ZSMallStatusCodeEnum.DOWNLOAD_RECORD_GENERATING);
        } else {
            String headerLanguage = ServletUtils.getHeaderLanguage();
            String fileName = StrUtil.format(FileNameConstants.TRACKING_RECORD_LIST, DateUtil.format(new Date(), "yyMMdd-HH:mm:ss.SSSS"));
            // 创建新的下载记录
            DownloadRecord newRecord = new DownloadRecord();
            newRecord.setRecordState(RecordStateEnum.Generating);
            newRecord.setFileName(fileName);
            newRecord.setDownloadType(DownloadTypePlusEnum.Bill);
            iDownloadRecordService.save(newRecord);

            TrackingPageDTO trackingPageDTO = iOrderItemTrackingRecordService.generateDTO(bo);
            trackingPageDTO.setTenantType(LoginHelper.getTenantType());
            trackingPageDTO.setTenantId(LoginHelper.getTenantId());
            log.info("trackingPageDTO = {}", JSONUtil.toJsonStr(trackingPageDTO));
            ThreadUtil.execute(() -> {
                BigExcelWriter excelWriter = null;
                try {
                    List<String> titleName;
                    if (StringUtils.equals(headerLanguage, "zh_CN")) {
                        titleName =
                            CollUtil.newLinkedList("订单编号", "Item No", "下单时间", "渠道类型", "发货方式", "承运商/跟踪单", "订单状态", "物流动态");
                    } else {
                        titleName = CollUtil
                            .newLinkedList("Order ID", "Item No", "Order Time", "Channel Type", "Logistics Type",
                                "Carrier/Tracking No", "Order State", "Logistics Progress");
                    }
                    List<OrderItemTrackingRecordVo> records = iOrderItemTrackingRecordService.queryList(trackingPageDTO);
                    List<OrderItemTrackingRecordVo> results = getOrderItemTrackingRecordVos(records);

                    List<Map<String, Object>> rows = new ArrayList<>();
                    if (CollUtil.isNotEmpty(results)) {
                        for (OrderItemTrackingRecordVo recordVo : results) {
                            Map<String, Object> dataMap = new LinkedHashMap<>();
                            int index = 0;
                            String orderNo = recordVo.getOrderNo();
                            String productSkuCode = recordVo.getProductSkuCode();
                            String orderTime = recordVo.getOrderTime();
                            String channelType = recordVo.getChannelType();
                            String logisticsType = recordVo.getLogisticsType();
                            String carrierOrTrackingNo = recordVo.getCarrierOrTrackingNo();
                            String orderState = recordVo.getOrderState();
                            String logisticsProgress = recordVo.getLogisticsProgress();
                            dataMap.put(titleName.get(index++), orderNo);
                            dataMap.put(titleName.get(index++), productSkuCode);
                            dataMap.put(titleName.get(index++), orderTime);
                            dataMap.put(titleName.get(index++), channelType);
                            dataMap.put(titleName.get(index++), logisticsType);
                            dataMap.put(titleName.get(index++), carrierOrTrackingNo);
                            dataMap.put(titleName.get(index++), orderState);
                            dataMap.put(titleName.get(index++), logisticsProgress);
                            rows.add(dataMap);
                        }
                    }
                    String tempSavePath = fileProperties.getTempSavePath();
                    String tempFile = tempSavePath + File.separator + UUID.fastUUID().toString(true) + ".xlsx";

                    excelWriter = ExcelUtil.getBigWriter();
                    excelWriter.write(rows, true);
                    excelWriter.autoSizeColumnAll();
                    File file = FileUtil.newFile(tempFile);
                    excelWriter.flush(file);

                    @Cleanup InputStream inputStream = FileUtil.getInputStream(file);
                    OSSUploadEvent uploadEvent = new OSSUploadEvent(inputStream, fileName);
                    SpringUtils.publishEvent(uploadEvent);
                    SysOssVo sysOssVo = uploadEvent.getSysOssVo();

                    newRecord.setOssId(sysOssVo.getOssId());
                    newRecord.setFileSaveKey(sysOssVo.getFileName());
                    newRecord.setFileUrl(sysOssVo.getUrl());
                    newRecord.setFileSize(StrUtil.toString(file.length()));
                    newRecord.setRecordState(RecordStateEnum.Ready);
                    iDownloadRecordService.updateById(newRecord);

                    FileUtil.del(file);
                } catch (Exception e) {
                    log.error("【根据类型导出表格】导出跟踪单列表，{}", e.getMessage(), e);
                    newRecord.setRecordState(RecordStateEnum.Failed);
                    iDownloadRecordService.updateById(newRecord);
                } finally {
                    if (excelWriter != null) {
                        excelWriter.close();
                    }
                }
            });
        }
        return R.ok();
    }

    /**
     * 新增子订单物流跟踪单
     */
    @Override
    public Boolean insertByBo(OrderItemTrackingRecordBo bo) {
        return iOrderItemTrackingRecordService.insertByBo(bo);
    }

    /**
     * 修改子订单物流跟踪单
     */
    @Override
    public Boolean updateByBo(OrderItemTrackingRecordBo bo) {
        return iOrderItemTrackingRecordService.updateByBo(bo);
    }

    /**
     * 批量删除子订单物流跟踪单
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return iOrderItemTrackingRecordService.deleteWithValidByIds(ids, isValid);
    }

    @Override
    public void batchSetTracking(List<OrderItemTrackingDTO> dtos) {

    }

}
