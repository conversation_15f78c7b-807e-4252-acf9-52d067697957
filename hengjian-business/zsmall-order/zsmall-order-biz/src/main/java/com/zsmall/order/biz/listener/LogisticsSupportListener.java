package com.zsmall.order.biz.listener;

import com.zsmall.extend.event.order.QueryLogisticsEvent;
import com.zsmall.order.biz.support.ThirdPartyLogisticsSupport;
import com.zsmall.order.entity.domain.OrderItemTrackingRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

/**
 * 物流相关事件监听
 *
 * <AUTHOR>
 * @date 2023/7/13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LogisticsSupportListener {

    private final ThirdPartyLogisticsSupport thirdPartyLogisticsSupport;

    /**
     * 监听查询物流事件
     * @param event
     */
    @EventListener
    public void listenInQueryLogistics(QueryLogisticsEvent event) {
        OrderItemTrackingRecord trackingRecord = event.getTrackingRecord();
        thirdPartyLogisticsSupport.queryLogistics(trackingRecord);
    }

}
