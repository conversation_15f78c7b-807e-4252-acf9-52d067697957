package com.zsmall.order.biz.service;

import com.hengjian.common.core.domain.R;
import com.zsmall.order.entity.domain.bo.ConfirmDispatchedBo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 主订单物流信息Service接口
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
public interface OrderLogisticsInfoService {

    /**
     * 确认发货
     */
    R<Void> confirmDispatched(ConfirmDispatchedBo bo);

    /**
     * 批量确认发货
     * @param file
     * @return
     */
    R<Void> batchConfirmDispatched(MultipartFile file) throws Exception;
}
