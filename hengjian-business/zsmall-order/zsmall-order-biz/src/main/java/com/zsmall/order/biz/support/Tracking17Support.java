package com.zsmall.order.biz.support;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.zsmall.common.domain.dto.LogisticsRespDTO;
import com.zsmall.common.enums.order.LogisticsProgress;
import com.zsmall.common.enums.order.OrderStateType;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.extend.logistics.config.properties.Tracking17Properties;
import com.zsmall.extend.logistics.core.Tracking17Api;
import com.zsmall.extend.logistics.enums.LogisticsMainStatus;
import com.zsmall.extend.logistics.model.request.InBase;
import com.zsmall.extend.logistics.model.request.InGetTrackInfo;
import com.zsmall.extend.logistics.model.request.InRegister;
import com.zsmall.extend.logistics.model.response.OutBase;
import com.zsmall.extend.logistics.model.response.OutData;
import com.zsmall.extend.logistics.model.response.OutNotifyBase;
import com.zsmall.extend.logistics.model.response.trackinfo.LatestEvent;
import com.zsmall.order.entity.domain.OrderItemTrackingRecord;
import com.zsmall.order.entity.domain.Tracking17Carrier;
import com.zsmall.order.entity.iservice.IOrderItemTrackingRecordService;
import com.zsmall.order.entity.iservice.ITracking17CarrierService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-12-12
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class Tracking17Support {

    private final Tracking17Properties tracking17Properties;
    private final IOrderItemTrackingRecordService iOrderItemTrackingRecordService;
    private final ITracking17CarrierService iTracking17CarrierService;

    /**
     * 注册运单
     *
     * @param trackingNumber
     * @return 承运商
     */
    public Integer register(String carrier, String trackingNumber) throws RStatusCodeException {
        LogisticsRespDTO respEntity = new LogisticsRespDTO();

        Integer carrierKey = null;
        if (StrUtil.isNotBlank(carrier)) {
            Tracking17Carrier Tracking17Carrier = iTracking17CarrierService.queryByCarrierName(carrier);
            if (Tracking17Carrier != null) {
                carrierKey = Tracking17Carrier.getCarrierKey();
            }
        }

        Tracking17Api tracking17ApiSdk = new Tracking17Api(tracking17Properties);
        List<InRegister> inRegister = new ArrayList<>();
        inRegister.add(new InRegister(trackingNumber, carrierKey));
        OutBase register = tracking17ApiSdk.register(inRegister);
        log.info("注册运单 register = {}", JSONUtil.toJsonStr(register));
        Map<String, Integer> errorCodeMap = register.getErrorCodeMap();
        log.info("errorCodeMap = {}", JSONUtil.toJsonStr(errorCodeMap));

        OutData data;
        if (CollUtil.size(errorCodeMap) <= 0) {
            data = register.getData();
        } else {
            Integer respCode = errorCodeMap.get(trackingNumber);
            if (respCode != null && ObjectUtil.equals(respCode, -18019901)) {
                InBase<InGetTrackInfo> inBase = new InBase<>();
                inBase.addList(new InGetTrackInfo(trackingNumber));
                OutBase trackInfo = tracking17ApiSdk.getTrackInfo(inBase);
                Map<String, Integer> errorCodeMap1 = trackInfo.getErrorCodeMap();
                if (CollUtil.size(errorCodeMap1) <= 0) {
                    data = trackInfo.getData();
                } else {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.TRACK_17_DISCERN_LOGISTICS_ERROR.args("Unknown"));
                }
            } else {
                String errorMessage = register.getErrorMessage();
                throw new RStatusCodeException(ZSMallStatusCodeEnum.TRACK_17_DISCERN_LOGISTICS_ERROR.args(errorMessage));
            }
        }

        if (data != null) {
            List<OutData.Accepted> accepted = data.getAccepted();
            if (CollUtil.isNotEmpty(accepted)) {
                for (OutData.Accepted accepted1 : accepted) {
                    String number = accepted1.getNumber();
                    if (StrUtil.equals(number, trackingNumber)) {
                        return accepted1.getCarrier();
                    }
                }
            }
        }

        throw new RStatusCodeException(ZSMallStatusCodeEnum.TRACK_17_DISCERN_LOGISTICS_ERROR.args("Unknown"));
    }

    /**
     * 查询物流信息并更新
     *
     * @param trackingNumber
     * @return
     */
    public LogisticsRespDTO track(Integer carrierCode, String trackingNumber) throws RStatusCodeException {
        log.info("trackingNumber =  {} ", trackingNumber);
        LogisticsRespDTO respEntity = new LogisticsRespDTO();
        LogisticsProgress logisticsProgress = null;
        Integer carrier = null;
        String respCode = null;
        String respMessage = null;
        String activityDateTime = null;

        try {
            Tracking17Api tracking17ApiSdk = new Tracking17Api(tracking17Properties);
            InBase<InGetTrackInfo> inBase = new InBase<>();
            inBase.addList(new InGetTrackInfo(trackingNumber).setCarrier(carrierCode));
            OutBase trackInfo = tracking17ApiSdk.getTrackInfo(inBase);
            log.info("trackInfo = {} ", JSONUtil.toJsonStr(trackInfo));
            respCode = StrUtil.toString(trackInfo.getCode());
            OutData data = trackInfo.getData();
            Map<String, LogisticsMainStatus> statusMap = data.getStatusMap();
            LatestEvent latestEvent = data.getLatestEvent(trackingNumber);

            log.info("latestEvent = {}", JSONUtil.toJsonStr(latestEvent));
            //状态转换
            if (MapUtil.isNotEmpty(statusMap)) {
                LogisticsMainStatus logisticsMainStatus = statusMap.get(trackingNumber);
                logisticsProgress = getFulfillmentType(logisticsMainStatus);
                //物流信息
                if (latestEvent != null) {
                    respMessage = latestEvent.getDescription();
                    DateTime dateTime = DateUtil.parseUTC(latestEvent.getTimeUtc());
                    activityDateTime = DateUtil.format(dateTime, "yyyy-MM-dd HH:mm:ss");
                }

                if (data != null) {
                    List<OutData.Accepted> accepted = data.getAccepted();
                    if (CollUtil.isNotEmpty(accepted)) {
                        for (OutData.Accepted accepted1 : accepted) {
                            String number = accepted1.getNumber();
                            if (StrUtil.equals(number, trackingNumber)) {
                                carrier = accepted1.getCarrier();
                            }
                        }
                    }
                }
            } else {
                //错误信息
                Map<String, Integer> errorCodeMap = trackInfo.getErrorCodeMap();
                respCode = StrUtil.toString(errorCodeMap.get(trackingNumber));
                respMessage = trackInfo.getErrorMessage();
                activityDateTime = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");

                OutData registerData = null;
                if (StrUtil.equals(respCode, "-18019902")) {
                    List<InRegister> inRegister = new ArrayList<>();
                    inRegister.add(new InRegister(trackingNumber, carrierCode));
                    OutBase register = tracking17ApiSdk.register(inRegister);
                    registerData = register.getData();
                } else if (StrUtil.equals(respCode, "-18019909")) {
                    List<InRegister> inRegister = new ArrayList<>();
                    inRegister.add(new InRegister(trackingNumber, carrierCode));
                    OutBase register = tracking17ApiSdk.register(inRegister);
                    registerData = register.getData();
                } else if (StrUtil.equals(respCode, "-18019910")) {
                    List<InRegister> inRegister = new ArrayList<>();
                    inRegister.add(new InRegister(trackingNumber, carrierCode));
                    OutBase register = tracking17ApiSdk.register(inRegister);
                    registerData = register.getData();
                }

                if (registerData != null) {
                    List<OutData.Accepted> accepted = registerData.getAccepted();
                    if (CollUtil.isNotEmpty(accepted)) {
                        for (OutData.Accepted accepted1 : accepted) {
                            String number = accepted1.getNumber();
                            if (StrUtil.equals(number, trackingNumber)) {
                                carrier = accepted1.getCarrier();
                            }
                        }
                    }
                }
            }

            respEntity.setCarrier(carrier);
            respEntity.setRespCode(respCode);
            respEntity.setRespMessage(respMessage);
            respEntity.setLogisticsProgress(logisticsProgress);
            respEntity.setActivityDateTime(activityDateTime);
            return respEntity;
        } catch (Exception e) {
            log.info("Tracking17Support - track error - trackingNumber = {}", trackingNumber);
            log.error("Tracking17Support - track error = {}", e.getMessage(), e);
            throw new RStatusCodeException(ZSMallStatusCodeEnum.THIRD_LOGISTICS_ERROR.args(trackingNumber, e.getMessage()));
        }
    }

    private LogisticsProgress getFulfillmentType(LogisticsMainStatus logisticsMainStatus) {
        LogisticsProgress fulfillmentStatus = null;
        if (ObjectUtil.equal(logisticsMainStatus, LogisticsMainStatus.NotFound) || ObjectUtil
            .equal(logisticsMainStatus, LogisticsMainStatus.Expired) || ObjectUtil
            .equal(logisticsMainStatus, LogisticsMainStatus.DeliveryFailure) || ObjectUtil
            .equal(logisticsMainStatus, LogisticsMainStatus.Exception)) {
            fulfillmentStatus = LogisticsProgress.Abnormal;
        } else if (ObjectUtil.equal(logisticsMainStatus, LogisticsMainStatus.InfoReceived)) {
            fulfillmentStatus = LogisticsProgress.LabelCreated;
        } else if (ObjectUtil.equal(logisticsMainStatus, LogisticsMainStatus.InTransit) || ObjectUtil
            .equal(logisticsMainStatus, LogisticsMainStatus.AvailableForPickup)) {
            //运输途中、自取 == 已发货
            fulfillmentStatus = LogisticsProgress.Dispatched;
        } else if (ObjectUtil.equal(logisticsMainStatus, LogisticsMainStatus.OutForDelivery)) {
            fulfillmentStatus = LogisticsProgress.OutForDelivery;
        } else if (ObjectUtil.equal(logisticsMainStatus, LogisticsMainStatus.Delivered)) {
            fulfillmentStatus = LogisticsProgress.Fulfilled;
        }
        return fulfillmentStatus;
    }


    public void handleTrackingUpdated(OutNotifyBase outNotifyBase) {
        log.info("handleTrackingUpdated outBase = {}", JSONUtil.toJsonStr(outNotifyBase));
        OutNotifyBase.OutNotifyData data = outNotifyBase.getData();
        log.info("data = {} ", JSONUtil.toJsonStr(data));
        log.info("data = {} ", JSONUtil.toJsonStr(data));
        if (data != null) {
            // 标志，在注册物流单时，这个字段是MD平台的子订单号，所以该Webhook回调接口传回来的也是子订单编号
            //      String tag = data.getTag();
            //快递单号
            String trackingNo = data.getNumber();
            Integer carrierCode = data.getCarrier();

            Tracking17Carrier Tracking17Carrier = iTracking17CarrierService.queryByCarrierCode(carrierCode);
            String carrier = null;
            if (Tracking17Carrier != null) {
                carrier = Tracking17Carrier.getCarrierName();
            }

            // 快递信息
            List<OrderItemTrackingRecord> trackingRecordList =
                iOrderItemTrackingRecordService.queryInTransitTrackingRecord(carrier, trackingNo, OrderStateType.Paid, LogisticsProgress.Fulfilled);
            log.info("物流跟踪单表.size = {}", CollUtil.size(trackingRecordList));
            if (CollUtil.isNotEmpty(trackingRecordList)) {
                // 获取简略跟踪状态Map信息（Key为跟踪单号，Value为状态Code）
                Map<String, LogisticsMainStatus> statusMap = outNotifyBase.getStatusMap();
                LogisticsMainStatus logisticsMainStatus = statusMap.get(trackingNo);
                LogisticsProgress fulfillmentStatus = this.getFulfillmentType(logisticsMainStatus);
                // 获取最后一次跟踪信息
                LatestEvent latestEvent = outNotifyBase.getLatestEvent();
                String description = null;
                String timeUtcFormat = null;
                if (latestEvent != null) {
                    // 跟踪信息详情
                    description = latestEvent.getDescription();
                    DateTime dateTime = DateUtil.parseUTC(latestEvent.getTimeUtc());
                    timeUtcFormat = DateUtil.format(dateTime, "yyyy-MM-dd HH:mm:ss");
                }

                for (OrderItemTrackingRecord trackingRecord : trackingRecordList) {
                    trackingRecord.setThirdPartyCode(StrUtil.toString(outNotifyBase));
                    trackingRecord.setThirdPartyMessage(description);
                    trackingRecord.setThirdPartyDateTime(timeUtcFormat);
                    trackingRecord.setSystemManaged(true);
                    trackingRecord.setLogisticsProgress(fulfillmentStatus);
                    trackingRecord.setConfirmDate(null);
                    if (ObjectUtil.equals(fulfillmentStatus, LogisticsProgress.Fulfilled)) {
                        trackingRecord.setFulfillmentTime(new Date());
                    }
                }
                iOrderItemTrackingRecordService.updateBatchById(trackingRecordList);
            }
        }
    }
}
