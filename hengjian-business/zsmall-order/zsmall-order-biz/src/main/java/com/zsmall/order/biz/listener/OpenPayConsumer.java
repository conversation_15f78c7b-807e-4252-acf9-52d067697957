package com.zsmall.order.biz.listener;

import com.alibaba.fastjson.JSON;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.rabbitmq.client.Channel;
import com.zsmall.common.domain.dto.OrderReceiveFromThirdDTO;
import com.zsmall.order.biz.factory.ThirdOrderOperationFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/11/5 11:08
 */
@Slf4j
@Component
public class OpenPayConsumer {
    @Resource
    private ThirdOrderOperationFactory factory;
    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.OPEN_PAY_QUEUE)
    public void openPayReceive(Message message, @Header(AmqpHeaders.CHANNEL) Channel channel) throws IOException {
        String messageContext = new String(message.getBody());
        // OrderReceiveFromThirdDTO
        if (StringUtils.isEmpty(messageContext)) {
            log.warn("消息队列: {}, 信息为空",RabbitMqConstant.OPEN_PAY_QUEUE);
            return;
        }
        log.info("接收到open-pay: {}",messageContext);
        // messageContext 是json转换为OrderReceiveFromThirdDTO
        OrderReceiveFromThirdDTO orderDTO = JSON.parseObject(messageContext, OrderReceiveFromThirdDTO.class);

        try {
            factory.getInvokeStrategy("openOperationHandler").payOrderForAsync(orderDTO, false);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("open-pay信息队列错误日志",e);
        } finally {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(),true);
        }
    }
}
