package com.zsmall.order.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.common.domain.tiktok.domain.dto.order.TikTokLineOrderCompensation;
import com.zsmall.order.biz.service.TiktokLineOrderCompensationService;
import com.zsmall.order.entity.mapper.TiktokLineOrderCompensationMapper;
import org.springframework.stereotype.Service;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/6/5 18:43
 */
@Service(value = "tiktokLineOrderCompensationForTransactional")
public class ITikTokLineOrderCompensationServiceImpl  extends ServiceImpl<TiktokLineOrderCompensationMapper, TikTokLineOrderCompensation>
    implements TiktokLineOrderCompensationService {
    @Override
    public void cancellationCompensationPlan(String type) {
        // do nothing
    }
}
