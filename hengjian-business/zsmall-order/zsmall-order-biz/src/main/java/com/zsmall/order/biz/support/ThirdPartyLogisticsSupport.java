package com.zsmall.order.biz.support;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.domain.dto.LogisticsRespDTO;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.enums.order.LogisticsProgress;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.OrderItemTrackingRecord;
import com.zsmall.order.entity.domain.Tracking17Carrier;
import com.zsmall.order.entity.iservice.IOrderItemTrackingRecordService;
import com.zsmall.order.entity.iservice.ITracking17CarrierService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 第三方物流支持
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ThirdPartyLogisticsSupport {


    private final Tracking17Support tracking17Support;
    private final BusinessParameterService businessParameterService;
    private final IOrderItemTrackingRecordService iOrderItemTrackingRecordService;
    private final ITracking17CarrierService iTracking17CarrierService;

    /**
     * 根据子订单查询物流信息
     *
     * @param orderItem
     */
    public void queryLogisticsByOrderItem(OrderItem orderItem) {
        String orderItemNo = orderItem.getOrderItemNo();
        log.info("子订单{}准备查询物流信息", orderItem);
        // 查出子订单关联的所有追踪单
        TenantHelper.ignore(() -> {
            List<OrderItemTrackingRecord> trackingList = iOrderItemTrackingRecordService.getListByOrderItemNo(orderItemNo);
            if (CollUtil.isNotEmpty(trackingList)) {
                for (OrderItemTrackingRecord trackingOrders : trackingList) {
                    if (ObjectUtil.notEqual(trackingOrders.getLogisticsProgress(), LogisticsProgress.Fulfilled)) {
                        try{
                            this.queryLogistics(trackingOrders);
                        }catch (Exception e){
                            log.error("iTracking17,物流更新异常");
                        }
                    }
                }
                iOrderItemTrackingRecordService.saveOrUpdateBatch(trackingList);
            }
        });
    }

    /**
     * 根据承运商和追踪单号查询子单集合的物流信息
     *
     * @param trackingRecord
     */
    public void queryLogistics(OrderItemTrackingRecord trackingRecord) {
        log.info("准备查询物流信息 trackingRecord = {}", JSONUtil.toJsonStr(trackingRecord));
        Integer day = businessParameterService.getValueFromInteger(BusinessParameterType.TWICE_CONFIRM_TRACKING);
        // 调用物流API次数上限
        Integer maximumApi =
            businessParameterService.getValueFromInteger(BusinessParameterType.MAXIMUM_NUMBER_OF_LOGISTICS);
        DateTime nowDate = DateUtil.date();
        String confirmDateStr = DateUtil.format(nowDate.offset(DateField.DAY_OF_MONTH, day), "yyyy-MM-dd");
        String nowDateStr = DateUtil.format(nowDate, "yyyy-MM-dd");

        String carrier = trackingRecord.getLogisticsCarrier();
        String trackingNo = trackingRecord.getLogisticsTrackingNo();

        Integer carrierKey = null;
        if (StrUtil.isNotBlank(carrier)) {
            Tracking17Carrier tracking17Carrier = iTracking17CarrierService.queryByCarrierName(carrier);
            if (tracking17Carrier != null) {
                carrierKey = tracking17Carrier.getCarrierKey();
            }
        }

        try {
            LogisticsRespDTO dto = tracking17Support.track(carrierKey, trackingNo);
            // 如果当前运单无承运商，则使用17Track自动识别出来的承运商
            if (StrUtil.isBlank(carrier) && dto.getCarrier() != null) {
                Tracking17Carrier tracking17Carrier = iTracking17CarrierService.queryByCarrierCode(dto.getCarrier());
                if (tracking17Carrier != null) {
                    trackingRecord.setLogisticsCarrier(tracking17Carrier.getCarrierName());
                }
            }
            log.info("查询物流信息 - trackingRecord.id = {}, dto = {}", trackingRecord.getId(), JSONUtil.toJsonStr(dto));
            trackingRecord.setThirdPartyCode(dto.getRespCode());
            trackingRecord.setThirdPartyMessage(dto.getRespMessage());
            trackingRecord.setThirdPartyDateTime(dto.getActivityDateTime());
            trackingRecord.setSystemManaged(true);
            trackingRecord.setLastQueryTime(DateUtil.date());
            trackingRecord.setConfirmDate(null);

            // 调用API次数
            Integer nowCalling = trackingRecord.getCallingApi() + 1;
            trackingRecord.setCallingApi(nowCalling);
            if (nowCalling >= maximumApi) {
                trackingRecord.setSystemManaged(false);
            }
            LogisticsProgress logisticsProgress = dto.getLogisticsProgress();
            if (logisticsProgress != null) {
                trackingRecord.setLogisticsProgress(logisticsProgress);
                if (Objects.equals(logisticsProgress, LogisticsProgress.Fulfilled)) {
                    trackingRecord.setFulfillmentTime(nowDate);
                }
            } else {
                trackingRecord.setLogisticsProgress(LogisticsProgress.UnDispatched);
            }
        } catch (RStatusCodeException e) {
            log.error("物流单号{}查询物流信息，出现异常 {}", trackingNo, e.getMessage(), e);

            trackingRecord.setThirdPartyCode(e.getStatusCode().getSubCode());
            trackingRecord.setThirdPartyMessage(e.getStatusCode().getMessage());
            trackingRecord.setLastQueryTime(DateUtil.date());
            trackingRecord.setLogisticsProgress(LogisticsProgress.Abnormal);

            // 调用API次数
            Integer nowCalling = trackingRecord.getCallingApi() + 1;
            trackingRecord.setCallingApi(nowCalling);
            // 当前物流信息的二次确认时间
            String nowConfirmDate = trackingRecord.getConfirmDate();

            /**
             * 2021-8-26新逻辑，当一个物流单出现异常时，判断是否存在二次确认日期
             * 若不存在，则记录新的二次确认日期，并保持系统托管状态
             * 若存在，则判断当前日期是否和二次确认日期为同一天，如果是同一天，则取消系统托管状态，反之则继续保持系统托管状态
             */
            log.info("物流单号{}查询物流信息，下次确认日期 = {} 当前日期 = {}", nowConfirmDate, nowDate);
            if (StrUtil.isBlank(nowConfirmDate)) {
                trackingRecord.setConfirmDate(confirmDateStr);
                trackingRecord.setSystemManaged(true);
            } else {
                if (StrUtil.equals(nowDateStr, nowConfirmDate)) {
                    trackingRecord.setSystemManaged(false);
                } else {
                    trackingRecord.setSystemManaged(true);
                }
            }
        }
    }

    /*private LogTrackingOrders generateTrackingLog(TrackingOrders trackingOrders, String respCode, String respMessage) {
        Long trackingId = trackingOrders.getId();
        String orderItemNo = trackingOrders.getOrderItemNo();
        Integer callingApi = trackingOrders.getCallingApi();
        String confirmDate = trackingOrders.getConfirmDate();

        String carrier = trackingOrders.getCarrier();
        String trackingNo = trackingOrders.getTrackingNo();
        ConditionType systemManaged = trackingOrders.getSystemManaged();
        FulfillmentSelectType fulfillmentType = trackingOrders.getFulfillmentType();

        LogTrackingOrders logTrackingOrders = new LogTrackingOrders();
        logTrackingOrders.setTrackingOrdersId(trackingId);
        logTrackingOrders.setOrderItemNo(orderItemNo);

        logTrackingOrders.setCarrier(carrier);
        logTrackingOrders.setTrackingNo(trackingNo);
        logTrackingOrders.setSystemManaged(systemManaged);
        logTrackingOrders.setFulfillmentType(fulfillmentType);
        logTrackingOrders.setThirdPartyCode(respCode);
        logTrackingOrders.setThirdPartyMessage(respMessage);
        logTrackingOrders.setCallingApi(callingApi);
        logTrackingOrders.setConfirmDate(confirmDate);
        return logTrackingOrders;
    }*/


}
