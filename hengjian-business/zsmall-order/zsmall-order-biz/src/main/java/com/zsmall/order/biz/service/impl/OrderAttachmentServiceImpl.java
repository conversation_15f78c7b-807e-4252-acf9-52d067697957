package com.zsmall.order.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.order.biz.service.OrderAttachmentService;
import com.zsmall.order.entity.domain.OrderAttachment;
import com.zsmall.order.entity.domain.bo.OrderAttachmentBo;
import com.zsmall.order.entity.domain.vo.OrderAttachmentVo;
import com.zsmall.order.entity.mapper.OrderAttachmentMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 主订单附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@RequiredArgsConstructor
@Service
public class OrderAttachmentServiceImpl implements OrderAttachmentService {

    private final OrderAttachmentMapper baseMapper;

    /**
     * 查询主订单附件
     */
    @Override
    public OrderAttachmentVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询主订单附件列表
     */
    @Override
    public TableDataInfo<OrderAttachmentVo> queryPageList(OrderAttachmentBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OrderAttachment> lqw = buildQueryWrapper(bo);
        Page<OrderAttachmentVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询主订单附件列表
     */
    @Override
    public List<OrderAttachmentVo> queryList(OrderAttachmentBo bo) {
        LambdaQueryWrapper<OrderAttachment> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OrderAttachment> buildQueryWrapper(OrderAttachmentBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrderAttachment> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getOssId() != null, OrderAttachment::getOssId, bo.getOssId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), OrderAttachment::getOrderNo, bo.getOrderNo());
        lqw.like(StringUtils.isNotBlank(bo.getAttachmentName()), OrderAttachment::getAttachmentName, bo.getAttachmentName());
        lqw.like(StringUtils.isNotBlank(bo.getAttachmentOriginalName()), OrderAttachment::getAttachmentOriginalName, bo.getAttachmentOriginalName());
        lqw.eq(StringUtils.isNotBlank(bo.getAttachmentSuffix()), OrderAttachment::getAttachmentSuffix, bo.getAttachmentSuffix());
        lqw.eq(StringUtils.isNotBlank(bo.getAttachmentSavePath()), OrderAttachment::getAttachmentSavePath, bo.getAttachmentSavePath());
        lqw.eq(StringUtils.isNotBlank(bo.getAttachmentShowUrl()), OrderAttachment::getAttachmentShowUrl, bo.getAttachmentShowUrl());
        lqw.eq(bo.getAttachmentSort() != null, OrderAttachment::getAttachmentSort, bo.getAttachmentSort());
        lqw.eq(StringUtils.isNotBlank(bo.getAttachmentType()), OrderAttachment::getAttachmentType, bo.getAttachmentType());
        return lqw;
    }

    /**
     * 新增主订单附件
     */
    @Override
    public Boolean insertByBo(OrderAttachmentBo bo) {
        OrderAttachment add = MapstructUtils.convert(bo, OrderAttachment.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改主订单附件
     */
    @Override
    public Boolean updateByBo(OrderAttachmentBo bo) {
        OrderAttachment update = MapstructUtils.convert(bo, OrderAttachment.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrderAttachment entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除主订单附件
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
