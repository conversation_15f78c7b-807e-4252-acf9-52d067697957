package com.zsmall.order.biz.handler.impl;

import cn.hutool.core.util.StrUtil;
import com.hengjian.common.core.domain.R;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.common.BusinessTypeMappingEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.LogisticsProgress;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.order.OrderAddressType;
import com.zsmall.common.enums.order.OrderStateType;
import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import com.zsmall.common.enums.warehouse.WarehouseChannelCodeEnum;
import com.zsmall.common.handler.AbstractOrderOperationHandler;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.common.util.RegexUtil;
import com.zsmall.order.biz.manager.OrderAndItemManager;
import com.zsmall.order.biz.parse.ParseUtil;
import com.zsmall.order.biz.service.OrderItemService;
import com.zsmall.order.biz.service.OrdersService;
import com.zsmall.order.biz.service.impl.OrderItemProductSkuThirdServiceImpl;
import com.zsmall.order.biz.service.impl.OrderItemThirdServiceImpl;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.dto.*;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.product.entity.domain.Product;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.system.entity.iservice.ITenantShippingAddressService;
import com.zsmall.system.entity.iservice.IWorldLocationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/12 15:58
 */
@Slf4j
@Lazy
@Component("multiOperationHandler")
public class MultiOrderOperationHandler extends AbstractOrderOperationHandler<String, ErpSaleOrderDTO, Map<String, Object>, Orders, Object> {
    @Resource
    private  IOrderItemTrackingRecordService iOrderItemTrackingRecordService;

    @Resource
    private IProductSkuPriceService iProductSkuPriceService;

    @Resource
    private IOrderRefundService iOrderRefundService;


    @Resource
    private ITenantShippingAddressService tenantShippingAddressService;

    @Resource
    private OrderItemService orderItemService;

    @Resource
    private OrderSupport orderSupport;

    @Resource
    private IProductSkuService iProductSkuService;

    @Resource
    private IProductService iProductService;

    @Resource
    private OrderAndItemManager orderAndItemManager;

    @Resource
    private IOrdersService iOrdersService;

//    @Resource
//    private IProductActivityPriceItemService iProductActivityPriceItemService;

    @Value("${distribution.specify.warehouse.id.hj}")
    public String warehouseSystemCode;

    @Resource
    private OrdersService ordersService;

    @Resource
    private ITenantSalesChannelService iTenantSalesChannelService;
    @Resource
    private IProductChannelControlService iProductChannelControlService;


    @Resource
    private BusinessParameterService businessParameterService;

    @Autowired
    ApplicationContext applicationContext;

    @Resource
    private IWorldLocationService iWorldLocationService;

    @Resource
    private OrderCodeGenerator orderCodeGenerator;


    @Resource
    private IProductSkuAttachmentService iProductSkuAttachmentService;

    @Value("${distribution.tenant.id.erp}")
    private String tenantId;

    @Resource
    private IOrderItemService iOrderItemService;
    @Resource
    private OrderItemThirdServiceImpl iOrderItemThirdService;


    @Resource
    private IOrderItemProductSkuService iOrderItemProductSkuService;
    @Resource
    private IOrderLogisticsInfoService iOrderLogisticsInfoService;
    @Resource
    private IOrderAddressInfoService iOrderAddressInfoService;
    @Resource
    private IOrderItemPriceService iOrderItemPriceService;


    @Resource
    private IProductSkuService productSkuService;

    @Resource
    private OrderItemProductSkuThirdServiceImpl orderItemProductSkuThirdService;

    @Resource
    private ParseUtil util;

    @Override
    public void initialMessageSave(ErpSaleOrderDTO erpSaleOrderDTO) {

    }

    @Override
    public ErpSaleOrderDTO parseThirdData(String msg) throws Exception {
        ErpSaleOrderDTO dto = util.parseXmlForErpToDistribution(msg);
        // 订单存在拆分的情况 所以下述判断要参照erp录入
//        Orders orders = iOrdersService.getOne(new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, dto.getOrderNo())
//                                                                              .last("limit 1"));
//        if(ObjectUtil.isNotNull(orders)){
//            throw new AppRuntimeException("渠道订单已经收录,请勿重复提交,渠道订单号:"+orders.getOrderNo());
//        }
        return dto;
    }

    @Override
    public Boolean msgVerify(ErpSaleOrderDTO erpSaleOrderDTO) {

        // 渠道单号重复Set
        erpSaleOrderDTO.getSaleOrderItemsList();

        Set<String> channelOrderNoSet = new HashSet<>();

        List<ErpSaleOrderItemDTO> orderItemsList = erpSaleOrderDTO.getSaleOrderItemsList();

        for (ErpSaleOrderItemDTO itemDTO : orderItemsList) {

            // 仓库号,可以通过渠道写死 提前配置好仓库
            ProductSku productSku = iProductSkuService.queryByProductSkuCodeAndWarehouseSystemCode(itemDTO.getErpSku(), WarehouseChannelCodeEnum.OTHER_CHANNEL.getWarehouseSystemCode());
            if (productSku == null) {

                continue;
            } else {
                // 如果商品被管控，则报商品不存在
                String tenantId = "1";
                boolean checkUserAllow = iProductChannelControlService.checkUserAllow(tenantId, itemDTO.getErpSku(), ChannelTypeEnum.Others.name());

                if (!checkUserAllow) {
                    // 日志记录商品不存在
                    return Boolean.FALSE;
                }
            }

            Product product = iProductService.queryByProductSkuCode(itemDTO.getErpSku());
            if (StrUtil.isNotBlank(erpSaleOrderDTO.getOrderNo())) {
                if (channelOrderNoSet.contains(erpSaleOrderDTO.getOrderNo())) {
                    // ?
                } else {
                    // 查询此账户所有订单判断是否有重复的，排除Canceled的
                    boolean orderExists = iOrdersService.existsChannelOrderNo(erpSaleOrderDTO.getOrderNo(), OrderStateType.Canceled);
                    // 查询导入缓存表
                    if (orderExists) {
                        return Boolean.FALSE;
                        // 存在同一个订单
                    } else {
                        channelOrderNoSet.add(erpSaleOrderDTO.getOrderNo());
                    }
                }
            }
            // 规则校验
            if (!RegexUtil.matchQuantity(itemDTO.getQuantity().toString())) {
                // 数量异常-正则表达式
                return Boolean.FALSE;
            }


            SupportedLogisticsEnum supportedLogistics = product.getSupportedLogistics();

            // 三方默认代发
            String logisticsType = "DropShipping";
            if (StrUtil.equals(logisticsType, "DropShipping")) {
                // 仅支持自提
                if (SupportedLogisticsEnum.PickUpOnly.equals(supportedLogistics)) {
                    // 商品仅支持自提
                    log.info("订单与商品物流类型冲突,订单号:{},商品sku:{},商品仅支持自提", erpSaleOrderDTO.getOrderNo(), itemDTO.getErpSku());
                    return Boolean.FALSE;
                }
            }


        }
        return Boolean.TRUE;
    }

    @Override
    public Orders formalOrderAndItemEntry(Map<String, List> map, Orders orders) {
        List<OrderItem> orderItems = (List<OrderItem>) map.get("orderItems");
        List<OrderItemProductSku> skus = (List<OrderItemProductSku>) map.get("orderItemProductsSku");
        iOrdersService.save(orders);
        orderItems.forEach(item -> item.setOrderId(orders.getId()));
        iOrderItemService.saveBatch(orderItems);
        for (OrderItem item : orderItems) {
            for (OrderItemProductSku sku : skus) {
                if (item.getOrderItemNo().equals(sku.getOrderItemNo())) {
                    sku.setOrderItemId(item.getId());
                    sku.setOrderNo(item.getOrderNo());
                }
            }
        }
        iOrderItemProductSkuService.saveBatch(skus);
        return orders;
    }

    @Override
    public void formalOrderAboutEntry(Map<String, Object> map) {
        OrderLogisticsInfo info = (OrderLogisticsInfo) map.get("logisticsInfo");
        List<OrderAddressInfo> address = (List<OrderAddressInfo>) map.get("addressInfo");
        List<OrderItemPrice> itemPrices = (List<OrderItemPrice>) map.get("orderItemPrice");
        iOrderLogisticsInfoService.save(info);
        iOrderAddressInfoService.saveBatch(address);
        iOrderItemPriceService.saveBatch(itemPrices);
    }

    @Override
    public Map<String, Object> msgForLogistics(ErpSaleOrderDTO erpSaleOrderDTO, Orders orders,
                                               Map<String, List> itemMap) {
        HashMap<String, Object> map = new HashMap<>();
        List<OrderItem> orderItems = (List<OrderItem>) itemMap.get("orderItems");
        OrderLogisticsInfo orderLogisticsInfo = new OrderLogisticsInfo();
        ArrayList<OrderAddressInfo> addressInfos = new ArrayList<>();


//        List<String> result = Arrays.asList(addressDTO.getPostalCode().split("-"));
        List<ErpSaleOrderItemDTO> saleOrderItemsList = erpSaleOrderDTO.getSaleOrderItemsList();

        ErpSaleOrderAddressDTO addressDTO = erpSaleOrderDTO.getSaleOrderAddressList().get(0);
        ErpSaleOrderDetailDTO details = erpSaleOrderDTO.getSaleOrderDetails();

//        TenantShippingAddressVo addressVo = TenantHelper.ignore(() -> tenantShippingAddressService.queryById(Long.valueOf(addressId)));

        for (ErpSaleOrderItemDTO itemDTO : saleOrderItemsList) {
            // 拿默认地址模版

//            WorldLocation country = iWorldLocationService.getOne(new LambdaQueryWrapper<WorldLocation>().eq(WorldLocation::getLocationCode, addressVo.getCountryCode()));

            OrderAddressInfo orderAddressInfo = new OrderAddressInfo();

            orderAddressInfo.setOrderId(orders.getId());
            orderAddressInfo.setOrderNo(orders.getOrderNo());
            orderAddressInfo.setAddressType(OrderAddressType.ShipAddress);

            orderAddressInfo.setRecipient(details.getBuyName());

            orderLogisticsInfo.setZipCode(StrUtil.trim(addressDTO.getPostalCode()));
            orderLogisticsInfo.setLogisticsCountryCode(addressDTO.getCountryCode());

            orderAddressInfo.setOrderId(orders.getId());
            orderAddressInfo.setOrderNo(orders.getOrderNo());
            orderAddressInfo.setAddressType(OrderAddressType.ShipAddress);
            orderAddressInfo.setRecipient(erpSaleOrderDTO.getShippingName());
            orderAddressInfo.setPhoneNumber(addressDTO.getPhone());
            orderAddressInfo.setCountry(addressDTO.getCounty());
            orderAddressInfo.setCountryCode(addressDTO.getCountryCode());
            orderAddressInfo.setState(addressDTO.getState());
            orderAddressInfo.setStateCode(addressDTO.getState());
            orderAddressInfo.setCity(addressDTO.getCity());
            orderAddressInfo.setAddress1(addressDTO.getAddressline1());
            orderAddressInfo.setAddress2(addressDTO.getAddressline2());
            orderAddressInfo.setZipCode(addressDTO.getPostalCode());
            orderAddressInfo.setEmail(details.getBuyEmail());

            addressInfos.add(orderAddressInfo);
        }


        orderLogisticsInfo.setOrderId(orders.getId());
        orderLogisticsInfo.setOrderNo(orders.getOrderNo());
        orderLogisticsInfo.setLogisticsType(LogisticsTypeEnum.DropShipping);
        // 物流
        orderLogisticsInfo.setLogisticsCompanyName(erpSaleOrderDTO.getSaleOrderDetails().getCarrier());
        orderLogisticsInfo.setLogisticsServiceName(erpSaleOrderDTO.getSaleOrderDetails().getCarrier());
        orderLogisticsInfo.setZipCode(addressDTO.getPostalCode());


        List<OrderItemTrackingRecord> trackingList = new ArrayList<>();
        // 多渠道没有推送物流单


        ErpSaleOrderItemDTO saleOrderItemDTO = saleOrderItemsList.get(0);
        String trackNo = saleOrderItemDTO.getTrackNo();
        OrderItem item = orderItems.get(0);
        String trimTracking = StrUtil.trim(trackNo);
        if (StrUtil.isNotBlank(trimTracking)) {
            OrderItemTrackingRecord trackingRecord = new OrderItemTrackingRecord();
            trackingRecord.setSku(saleOrderItemDTO.getErpSku());
            trackingRecord.setProductSkuCode(item.getProductSkuCode());
            trackingRecord.setLogisticsCarrier(details.getCarrier());
            trackingRecord.setLogisticsTrackingNo(trimTracking);
            trackingRecord.setOrderNo(orders.getOrderNo());
            trackingRecord.setOrderItemNo(item.getOrderItemNo());
            trackingRecord.setLogisticsProgress(LogisticsProgress.UnDispatched);
            // 用的henjian仓库ID
            trackingRecord.setWarehouseSystemCode(warehouseSystemCode);

            trackingRecord.setQuantity(item.getTotalQuantity());

            trackingList.add(trackingRecord);
        }


        iOrderItemTrackingRecordService.saveBatch(trackingList);


        List<OrderItemPrice> itemPrices = new ArrayList<>();
        OrderPriceCalculateDTO paramDTO = new OrderPriceCalculateDTO();

        for (OrderItem orderItem : orderItems) {
            paramDTO.setOrderItem(orderItem);
            paramDTO.setLogisticsType(LogisticsTypeEnum.DropShipping);
            OrderPriceCalculateDTO calculateDTO = orderSupport.calculationOrderItemPriceForThird(paramDTO);
            OrderItemPrice itemPrice = calculateDTO.getOrderItemPrice();
            itemPrice.setOrderItemId(orderItem.getId());
            itemPrices.add(itemPrice);
        }

        map.put("logisticsInfo", orderLogisticsInfo);
        map.put("addressInfo", addressInfos);
        map.put("orderItemPrice", itemPrices);
        return map;
    }

    @Override
    public Map<String, List> msgForItems(ErpSaleOrderDTO erpSaleOrderDTO, Orders orders) {
        List<ErpSaleOrderItemDTO> saleOrderItemsList = erpSaleOrderDTO.getSaleOrderItemsList();
        List<OrderItem> orderItems = new ArrayList();
        List<OrderItemProductSku> orderItemProductSkus = new ArrayList<>();
        HashMap<String, List> hashMap = new HashMap<>();

        for (ErpSaleOrderItemDTO saleOrderItemDTO : saleOrderItemsList) {
            OrderItem orderItem = new OrderItem();

            orderItemService.setOrderBusinessField(orderItem, erpSaleOrderDTO, orders, saleOrderItemDTO);
            orderItemService.setChannelTag(orderItem, erpSaleOrderDTO, orders, saleOrderItemDTO);

            iOrderItemThirdService.setOrderTagSystem(orderItem, erpSaleOrderDTO, orders, saleOrderItemDTO);
            orderItems.add(orderItem);
            OrderItemProductSku orderItemProductSku = new OrderItemProductSku();
            // 通过 自订单编号进行关联,
            orderItemProductSkuThirdService.setBusinessField(orderItemProductSku, orderItem, erpSaleOrderDTO, orders, saleOrderItemDTO);
            orderItemProductSkus.add(orderItemProductSku);
        }
        hashMap.put("orderItems", orderItems);
        hashMap.put("orderItemProductsSku", orderItemProductSkus);
        return hashMap;
    }

    @Override
    public void getShippingLabels(List<Map<String, List>> orderData) {

    }

    @Override
    public Boolean isNeedPay() {
        return Boolean.FALSE;
    }

    @Override
    public String attachmentsFlow(ErpSaleOrderDTO erpSaleOrderDTO, Orders orders) {
        return null;
    }

    @Override
    public Boolean isNeedPay(ErpSaleOrderDTO erpSaleOrderDTO) {
        return null;
    }

    @Override
    public Boolean payOrder(ErpSaleOrderDTO multiOrderDTO) {
        return null;
    }

    @Override
    public Boolean payOrderForAsync(ErpSaleOrderDTO erpSaleOrderDTO, Boolean isAsync) throws Exception {
        return null;
    }

    @Override
    public Orders thirdToDistribution(ErpSaleOrderDTO erpSaleOrderDTO, Orders o) {
        Orders orders = new Orders();
        String orderNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderNo);
        orders.setOrderNo(orderNo);
        // 业务属性
        orders = ordersService.setOrderBusinessField(erpSaleOrderDTO, orders);
        orders = ordersService.setOrderTagSystem(erpSaleOrderDTO, orders);
        orders = ordersService.setChannelTag(erpSaleOrderDTO, orders);

        return orders;
    }

    @Override
    public List<ErpSaleOrderDTO> ordersDisassemble(ErpSaleOrderDTO erpSaleOrderDTO) {
        List<ErpSaleOrderItemDTO> saleOrderItemsList = erpSaleOrderDTO.getSaleOrderItemsList();
        List<ErpSaleOrderDTO> erpDTOS = new ArrayList<>();
        for (ErpSaleOrderItemDTO itemDTO : saleOrderItemsList) {
            List<ErpSaleOrderItemDTO> saleOrderItemDTOS = new ArrayList<>();
            ErpSaleOrderDTO dto = new ErpSaleOrderDTO();
            BeanUtils.copyProperties(erpSaleOrderDTO, dto);
            saleOrderItemDTOS.add(itemDTO);
            dto.pushSaleOrderItemsList(saleOrderItemDTOS);
            erpDTOS.add(dto);
        }
        return erpDTOS;
    }

    @Override
    public R tripartiteUpdate(Object o) {
        return null;
    }


    @Override
    public Boolean tripartiteReceiptGoods(Object o) {
        return true;
    }

    @Override
    public Boolean tripartiteDeliverGoods(Object o) {

        return true;
    }

    @Override
    public R<Void> test() {
        return null;
    }

    @Override
    public void orderOperationHandler(ErpSaleOrderDTO i, ConcurrentHashMap<String, List<Object>> businessMap,
                                      ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                                      ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum) {

    }

    @Override
    public void priceOperation(ConcurrentHashMap<String, List<Object>> businessMap,
                               ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                               ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum) {

    }

    @Override
    public void orderOperationHandlerSave(ChannelTypeEnum channelTypeEnum,
                                          ConcurrentHashMap<String, List<Object>> businessMap) {

    }

    @Override
    public List<ErpSaleOrderDTO> ordersDisassembleForList(List<ErpSaleOrderDTO> erpSaleOrderDTOS,
                                                          BusinessTypeMappingEnum mappingEnum) {
        return null;
    }

    @Override
    public List<ErpSaleOrderDTO> parseThirdDataForList(String s) {
        return null;
    }

//    @Override
//    public void testEntry(ErpSaleOrderDTO bo) {


//    }
}
