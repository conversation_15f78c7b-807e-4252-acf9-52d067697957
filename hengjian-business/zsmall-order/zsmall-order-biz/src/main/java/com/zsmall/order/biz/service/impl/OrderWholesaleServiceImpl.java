package com.zsmall.order.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.ServletUtils;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.extend.event.OSSUploadEvent;
import com.hengjian.system.domain.vo.SysOssVo;
import com.zsmall.common.constant.WholesaleConstant;
import com.zsmall.common.domain.dto.stock.AdjustStockDTO;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.common.AttachmentTypeEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.*;
import com.zsmall.common.enums.product.ProductTypeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.transaction.TransactionSubTypeEnum;
import com.zsmall.common.enums.wholesale.WholesaleDeliveryType;
import com.zsmall.common.enums.wholesale.WholesaleIntentionOrderStatusEnum;
import com.zsmall.common.enums.worldLocation.LocationTypeEnum;
import com.zsmall.common.exception.StockException;
import com.zsmall.common.properties.FileProperties;
import com.zsmall.common.util.DecimalUtil;
import com.zsmall.order.biz.service.OrderWholesaleService;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.biz.support.ThirdPartyLogisticsSupport;
import com.zsmall.order.biz.support.WholesaleOrderSupport;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.bo.order.PlaceOrderBo;
import com.zsmall.order.entity.domain.bo.wholesaleOrder.WholesaleOrderBaseBo;
import com.zsmall.order.entity.domain.bo.wholesaleOrder.WholesaleOrderListBo;
import com.zsmall.order.entity.domain.bo.wholesaleOrder.WholesalePlaceOrderBo;
import com.zsmall.order.entity.domain.dto.WIOrderPageDTO;
import com.zsmall.order.entity.domain.vo.wholesaleOrder.*;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.product.biz.service.ProductSkuStockService;
import com.zsmall.product.entity.domain.*;
import com.zsmall.product.entity.domain.bo.wholesale.WholesaleEnterPriceBo;
import com.zsmall.product.entity.domain.dto.wholesale.WholesaleTieredPriceMatchDTO;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.system.entity.domain.TenantShippingAddress;
import com.zsmall.system.entity.domain.WorldLocation;
import com.zsmall.system.entity.domain.event.CheckPaymentPasswordEvent;
import com.zsmall.system.entity.domain.vo.worldLocation.WorldLocationVo;
import com.zsmall.system.entity.iservice.ITenantShippingAddressService;
import com.zsmall.system.entity.iservice.IWorldLocationService;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.domain.vo.warehouse.WarehouseAddressVo;
import com.zsmall.warehouse.entity.iservice.IWarehouseAddressService;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/24 9:54
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class OrderWholesaleServiceImpl implements OrderWholesaleService {

    private final IWholesaleIntentionOrderItemService iWholesaleIntentionOrderItemService;
    private final IWholesaleIntentionOrderService iWholesaleIntentionOrderService;
    private final IWholesaleIntentionOrderAddressService iWholesaleIntentionOrderAddressService;
    private final IWholesaleIntentionOrderLogisticsService iWholesaleIntentionOrderLogisticsService;
    private final IProductService iProductService;
    private final ProductSkuStockService productSkuStockService;
    private final IProductSkuService iProductSkuService;
    private final IProductSkuAttachmentService iProductSkuAttachmentService;
    private final IProductSkuAttributeService iProductSkuAttributeService;
    private final IProductWholesaleDetailService iProductWholesaleDetailService;
    private final WholesaleOrderSupport wholesaleSupport;
    private final OrderCodeGenerator orderCodeGenerator;
    private final IWarehouseService iWarehouseService;
    private final IWarehouseAddressService iWarehouseAddressService;
    private final IWorldLocationService iWorldLocationService;
    private final ITenantShippingAddressService iTenantShippingAddressService;
    private final IOrdersService iOrdersService;
    private final IOrderItemService iOrderItemService;
    private final IOrderItemPriceService iOrderItemPriceService;
    private final IOrderItemProductSkuService iOrderItemProductSkuService;
    private final IOrderAddressInfoService iOrderAddressInfoService;
    private final IOrderLogisticsInfoService iOrderLogisticsInfoService;
    private final IOrderAttachmentService iOrderAttachmentService;
    private final IOrderItemTrackingRecordService iOrderItemTrackingRecordService;
    private final OrderSupport orderSupport;
    private final ThirdPartyLogisticsSupport thirdPartyLogisticsSupport;
    private final FileProperties fileProperties;

    private static final DateTimeFormatter dateTimeFormatter =
        DateTimeFormatter.ofPattern("MMM d, yyyy hh:mm:ss a", Locale.ENGLISH);

    @InMethodLog(value = "下现货批发意向订单")
    @Override
    @Transactional(rollbackFor = {StockException.class, RStatusCodeException.class, Exception.class})
    public R<Void> placeIntentionOrder(WholesalePlaceOrderBo bo) {
        LoginHelper.getLoginUser(TenantType.Distributor);

        String paymentPassword = bo.getPaymentPassword();
        String req_productCode = bo.getProductCode();
        Long req_userShippingAddress = bo.getUserShippingAddress();
        List<WholesalePlaceOrderBo.ProductSku> req_productSkuList = bo.getProductSkuList();

        // 验证钱包是否冻结，支付密码是否正确
        SpringUtils.context().publishEvent(new CheckPaymentPasswordEvent(paymentPassword));

        if (StrUtil.isBlank(req_productCode) || req_userShippingAddress == null || CollUtil.isEmpty(req_productSkuList)) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        // 查询收货地址
        TenantShippingAddress tenantShippingAddress = iTenantShippingAddressService.getById(req_userShippingAddress);

        if (tenantShippingAddress == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.SHIPPING_ADDRESS_NOT_EXIST);
        }

        Product product = iProductService.queryByProductCodeAndProductTypeNotDelete(req_productCode, ProductTypeEnum.WholesaleProduct);
        if (product != null) {
            Long productId = product.getId();
            String productCode = product.getProductCode();
            String productName = product.getName();

            ProductWholesaleDetail productWholesaleDetail =
                iProductWholesaleDetailService.queryByProductId(productId);
            // 订金比例
            BigDecimal depositRatio = productWholesaleDetail.getDepositRatio();
            Integer minimumQuantity = productWholesaleDetail.getMinimumQuantity();
            log.info("{} 最小起订量 {}", productCode, minimumQuantity);

            Integer totalQuantity =
                req_productSkuList.stream().filter(sku -> sku.getQuantity() != null)
                    .map(WholesalePlaceOrderBo.ProductSku::getQuantity).reduce(Integer::sum).get();
            log.info("订购总数量 {}", totalQuantity);

            // 未达到最小起订量要求
            if (NumberUtil.compare(totalQuantity, minimumQuantity) < 0) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.MINIMUM_QUANTITY_NOT_REACHED);
            }

            // 匹配阶梯价
            WholesaleTieredPriceMatchDTO tieredPriceMatch = wholesaleSupport.tieredPriceMatch(productCode, totalQuantity);
            log.info("匹配阶梯价结果 = {}", JSONUtil.toJsonStr(tieredPriceMatch));

            ProductWholesaleTieredPrice tieredPrice = tieredPriceMatch.getTieredPrice();
            Map<String, ProductSkuWholesalePrice> productSkuWholesalePriceMap =
                tieredPriceMatch.getProductSkuWholesalePriceMap();

            // 实体类集合
            List<WholesaleIntentionOrderItem> wiOrderItems = new ArrayList<>();

            // 主订单金额字段
            BigDecimal productAmount = BigDecimal.ZERO;
            BigDecimal productAmountPlatform = BigDecimal.ZERO;

            BigDecimal productBalanceAmount = BigDecimal.ZERO;
            BigDecimal productBalanceAmountPlatform = BigDecimal.ZERO;

            BigDecimal orderDepositAmount = BigDecimal.ZERO;
            BigDecimal orderDepositAmountPlatform = BigDecimal.ZERO;

            BigDecimal orderBalanceAmount = BigDecimal.ZERO;
            BigDecimal orderBalanceAmountPlatform = BigDecimal.ZERO;

            BigDecimal orderTotalAmount = BigDecimal.ZERO;
            BigDecimal orderTotalAmountPlatform = BigDecimal.ZERO;

            BigDecimal orderPayableAmount = BigDecimal.ZERO;

            // 已使用的商品SKU
            List<ProductSku> usedProductSku = new ArrayList<>();
            for (WholesalePlaceOrderBo.ProductSku req_productSku : req_productSkuList) {
                Integer req_quantity = req_productSku.getQuantity();
                String req_productSkuCode = req_productSku.getProductSkuCode();

                // 需求库存为零不处理
                if (req_quantity == null || req_quantity.equals(0)) {
                    continue;
                }

                ProductSku productSku = iProductSkuService.queryByProductSkuCode(req_productSkuCode);
                // SKU不存在
                if (productSku == null) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_SKU_NOT_EXIST);
                }

                // 库存不足
                Integer stockTotal = productSku.getStockTotal();
                if (NumberUtil.compare(req_quantity, stockTotal) > 0) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_SKU_INSUFFICIENT_INVENTORY.args(req_productSkuCode));
                }

                Long productSkuId = productSku.getId();
                String productSkuCode = productSku.getProductSkuCode();
                String supplierTenantId = productSku.getTenantId();

                // 查询SKU图片
                List<ProductSkuAttachment> enableAscAttachmentList = iProductSkuAttachmentService.queryBySkuIdAndAttachmentTypeOrderBySortAsc(productSkuId, AttachmentTypeEnum.Image);
                ProductSkuAttachment productSkuAttachment = enableAscAttachmentList.get(0);
                String showUrl = productSkuAttachment.getAttachmentShowUrl();
                String savePath = productSkuAttachment.getAttachmentSavePath();

                // 新子订单商品特有规格组合
                JSONObject newOwnSpec = new JSONObject();

                // 获取规格组合
                List<ProductSkuAttribute> productSkuAttributes = iProductSkuAttributeService.queryByProductSkuId(productSkuId);
                for (ProductSkuAttribute skuAttribute : productSkuAttributes) {
                    String attributeName = skuAttribute.getAttributeName();
                    String attributeValue = skuAttribute.getAttributeValue();
//                    Long productSkuId = skuAttribute.getProductSkuId();
//                    Long attributeSourceId = skuAttribute.getAttributeSourceId();
//                    Long productAttributeId = skuAttribute.getProductAttributeId();
                    newOwnSpec.set(attributeName, attributeValue);
                }

                String wholesaleIntentionOrderItemNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.WholesaleIntentionItem);

                // 国外现货批发商品SKU价格表
                ProductSkuWholesalePrice productSkuWholesalePrice = productSkuWholesalePriceMap.get(productSkuCode);

                // 商品单价
                BigDecimal originUnitPrice = productSkuWholesalePrice.getOriginUnitPrice();
                // 商品单价（平台）
                BigDecimal platformUnitPrice = productSkuWholesalePrice.getPlatformUnitPrice();
                // 订金单价
                BigDecimal depositUnitPrice = NumberUtil.mul(originUnitPrice, depositRatio).setScale(2, RoundingMode.HALF_UP);
                // 订金单价（平台）
                BigDecimal depositUnitPricePlatform = NumberUtil.mul(platformUnitPrice, depositRatio).setScale(2, RoundingMode.HALF_UP);
                // 尾款单价
                BigDecimal balanceUnitPrice = NumberUtil.sub(originUnitPrice, depositUnitPrice);
                // 尾款单价（平台）
                BigDecimal balanceUnitPricePlatform = NumberUtil.sub(platformUnitPrice, depositUnitPricePlatform);

                // 订金总金额
                BigDecimal depositTotalAmount = NumberUtil.mul(depositUnitPrice, req_quantity);
                // 订金总金额（平台）
                BigDecimal depositTotalAmountPlatform = NumberUtil.mul(depositUnitPricePlatform, req_quantity);
                // 尾款总金额
                BigDecimal balanceTotalAmount = NumberUtil.mul(balanceUnitPrice, req_quantity);
                // 尾款总金额（平台）
                BigDecimal balanceTotalAmountPlatform = NumberUtil.mul(balanceUnitPricePlatform, req_quantity);

                // 子订单总金额
                BigDecimal totalAmount = NumberUtil.mul(originUnitPrice, req_quantity);
                // 子订单总金额（平台）
                BigDecimal totalAmountPlatform = NumberUtil.mul(platformUnitPrice, req_quantity);

                // 主订单商品金额（总）
                productAmount = productAmount.add(totalAmount);
                productAmountPlatform = productAmountPlatform.add(totalAmountPlatform);
                // 主订单商品尾款金额（总）
                productBalanceAmount = productBalanceAmount.add(balanceTotalAmount);
                productBalanceAmountPlatform = productBalanceAmountPlatform.add(balanceTotalAmountPlatform);
                // 主订单订金总金额（总）
                orderDepositAmount = orderDepositAmount.add(depositTotalAmount);
                orderDepositAmountPlatform = orderDepositAmountPlatform.add(depositTotalAmountPlatform);
                // 主订单尾款总金额（总）
                orderBalanceAmount = orderBalanceAmount.add(balanceTotalAmount);
                orderBalanceAmountPlatform = orderBalanceAmountPlatform.add(balanceTotalAmountPlatform);
                // 主订单总金额
                orderTotalAmount = orderTotalAmount.add(totalAmount);
                orderTotalAmountPlatform = orderTotalAmountPlatform.add(totalAmountPlatform);
                // 应付金额
                orderPayableAmount = orderPayableAmount.add(balanceTotalAmountPlatform);

                WholesaleIntentionOrderItem wiOrderItem = new WholesaleIntentionOrderItem();
                wiOrderItem.setWholesaleIntentionOrderItemNo(wholesaleIntentionOrderItemNo);
                wiOrderItem.setSupplierTenantId(supplierTenantId);
                wiOrderItem.setOwnSpec(newOwnSpec.toString());
                wiOrderItem.setProductName(productName);
                wiOrderItem.setProductSkuCode(productSkuCode);
                wiOrderItem.setImageShowUrl(showUrl);
                wiOrderItem.setImageSavePath(savePath);
                wiOrderItem.setQuantity(req_quantity);
                wiOrderItem.setDepositRatio(depositRatio);

                wiOrderItem.setUnitPrice(originUnitPrice);
                wiOrderItem.setUnitPricePlatform(platformUnitPrice);
                wiOrderItem.setDepositUnitPrice(depositUnitPrice);
                wiOrderItem.setDepositUnitPricePlatform(depositUnitPricePlatform);
                wiOrderItem.setBalanceUnitPrice(balanceUnitPrice);
                wiOrderItem.setBalanceUnitPricePlatform(balanceUnitPricePlatform);

                wiOrderItem.setDepositTotalAmount(depositTotalAmount);
                wiOrderItem.setDepositTotalAmountPlatform(depositTotalAmountPlatform);
                wiOrderItem.setBalanceTotalAmount(balanceTotalAmount);
                wiOrderItem.setBalanceTotalAmountPlatform(balanceTotalAmountPlatform);
                wiOrderItem.setTotalAmount(totalAmount);
                wiOrderItem.setTotalAmountPlatform(totalAmountPlatform);

                // 记录库存变化，下面需要批量开锁更改库存
                productSku.setStockChange(req_quantity);
                wiOrderItems.add(wiOrderItem);
                usedProductSku.add(productSku);
            }

            if (CollUtil.isEmpty(usedProductSku)) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.MINIMUM_QUANTITY_NOT_REACHED);
            }

            // 预留时间（天）
            Integer reservedTime = productWholesaleDetail.getReservedTime();
            // 预估操作费
            BigDecimal estimatedOperationFee = tieredPrice.getEstimatedOperationFee();
            // 预估运费费
            BigDecimal estimatedShippingFee = tieredPrice.getEstimatedShippingFee();
            // 预估处理时间（天）
            Integer estimatedHandleTime = tieredPrice.getEstimatedHandleTime();

            WholesaleIntentionOrder wiOrder = new WholesaleIntentionOrder();
            String wholesaleIntentionOrderNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.WholesaleIntention);
            String orderNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderNo);

            wiOrder.setWholesaleIntentionOrderNo(wholesaleIntentionOrderNo);
            wiOrder.setOrderNo(orderNo);
            wiOrder.setProductCode(productCode);
            wiOrder.setTotalQuantity(totalQuantity);
            wiOrder.setEstimatedOperationFee(estimatedOperationFee);
            wiOrder.setEstimatedShippingFee(estimatedShippingFee);
            wiOrder.setEstimatedHandleTime(estimatedHandleTime);
            wiOrder.setReservedTime(reservedTime);
            wiOrder.setProductAmount(productAmount);
            wiOrder.setProductAmountPlatform(productAmountPlatform);
            wiOrder.setDepositRatio(depositRatio);
            wiOrder.setProductBalanceAmount(productBalanceAmount);
            wiOrder.setProductBalanceAmountPlatform(productBalanceAmountPlatform);

            wiOrder.setOrderDepositAmount(orderDepositAmount);
            wiOrder.setOrderDepositAmountPlatform(orderDepositAmountPlatform);
            wiOrder.setOrderBalanceAmount(orderBalanceAmount);
            wiOrder.setOrderBalanceAmountPlatform(orderBalanceAmountPlatform);

            wiOrder.setOrderTotalAmount(orderTotalAmount);
            wiOrder.setOrderTotalAmountPlatform(orderTotalAmountPlatform);
            wiOrder.setOrderPayableAmount(orderPayableAmount);

            wiOrder.setOrderStage(WholesaleConstant.IntentionOrderStage.NOT_ENTERED_PRICE);
            wiOrder.setOrderStatus(WholesaleConstant.IntentionOrderStatus.IN_PROGRESS);

            // 物流信息
            String warehouseSystemCode = productWholesaleDetail.getWarehouseSystemCode();
            String logisticsTemplateNo = productWholesaleDetail.getLogisticsTemplateNo();
            WholesaleIntentionOrderLogistics wiOrderLogistics = this.generateOrderLogistics(warehouseSystemCode, logisticsTemplateNo);
            // 地址信息
            WholesaleIntentionOrderAddress wiOrderAddress = this.generateOrderAddress(tenantShippingAddress);

            // 开始扣减库存
            if (CollUtil.isNotEmpty(usedProductSku)) {
                List<AdjustStockDTO> adjustStockDTOS = new ArrayList<>();
                for (ProductSku productSku : usedProductSku) {
                    AdjustStockDTO adjustStockDTO = new AdjustStockDTO();
                    adjustStockDTO.setAdjustQuantity(productSku.getStockChange() * -1);
                    adjustStockDTO.setProductSkuCode(productSku.getProductSkuCode());
                    adjustStockDTO.setSpecifyWarehouse(warehouseSystemCode);
                    adjustStockDTOS.add(adjustStockDTO);
                    productSku.setStockChanged(true);
                }
                try {
                    productSkuStockService.adjustStock(adjustStockDTOS);
                }catch (StockException e) {
                    throw new RStatusCodeException(e.getLocaleMessage().toMessage());
                }
            }
            log.info("SPU[{}] 扣减库存失败成功，开始扣款", productCode);

            // 保存数据
            iWholesaleIntentionOrderService.save(wiOrder);
            Long wiOrderId = wiOrder.getId();

            wiOrderAddress.setWholesaleIntentionOrderId(wiOrderId);
            iWholesaleIntentionOrderAddressService.save(wiOrderAddress);

            wiOrderLogistics.setWholesaleIntentionOrderId(wiOrderId);
            iWholesaleIntentionOrderLogisticsService.save(wiOrderLogistics);

            for (WholesaleIntentionOrderItem wiOrderItem : wiOrderItems) {
                wiOrderItem.setWholesaleIntentionOrderId(wiOrderId);
            }
            iWholesaleIntentionOrderItemService.saveBatch(wiOrderItems);

            orderSupport.wholesaleOrderPayChain(wiOrder, orderDepositAmountPlatform, TransactionSubTypeEnum.WholesaleDeposit);
        } else {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_NOT_EXIST);
        }
        return R.ok(ZSMallStatusCodeEnum.ORDER_SUCCESS);
    }

    @InMethodLog(value = "下订单（支付尾款）")
    @Override
    @Transactional(rollbackFor = {RStatusCodeException.class, Exception.class})
    public R<Void> placeOrder(PlaceOrderBo bo) {

        String wiOrderNo = bo.getWiOrderNo();
        String deliveryType = bo.getShippingMethod();
        String appointmentTime = bo.getAppointmentDate();
        String paymentPassword = bo.getPaymentPassword();

        // 验证钱包是否冻结，支付密码是否正确
        SpringUtils.context().publishEvent(new CheckPaymentPasswordEvent(paymentPassword));

        if (StrUtil.hasBlank(wiOrderNo, deliveryType)) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        WholesaleIntentionOrder wiOrder =
            iWholesaleIntentionOrderService.queryByWIOrderNo(wiOrderNo);
        if (wiOrder == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.WHOLESALE_ORDER_NOT_EXIST);
        }

        Integer orderStatus = wiOrder.getOrderStatus();
        if (!WholesaleConstant.IntentionOrderStatus.IN_PROGRESS.equals(orderStatus)) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.OPERATION_CANNOT_BE_PERFORMED);
        }

        String orderNo = wiOrder.getOrderNo();

        String productCode = wiOrder.getProductCode();
        Product product = iProductService.queryByProductCodeAndProductTypeNotDelete(productCode, ProductTypeEnum.WholesaleProduct);
        Long productId = product.getId();

        ProductWholesaleDetail productWholesaleDetail =
            iProductWholesaleDetailService.queryByProductId(productId);
        // 支持的发货方式
        JSONArray supportDeliveryType = productWholesaleDetail.getDeliveryType();
        // 是否支持该发货方式
        if (CollUtil.contains(supportDeliveryType, deliveryType)) {
            String carrier = bo.getLogisticsCarrier();
            List<String> trackingInfoList = bo.getLogisticsTrackingNo();
            OrderAttachment shippingLabel = bo.getShippingLabel();
            OrderAttachment orderAttachmentBo = bo.getOrderAttachment();
            LogisticsTypeEnum logisticsType = LogisticsTypeEnum.valueOf(deliveryType);

            // 订单附件
            OrderAttachment orderAttachment = null;
            if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
                // 判断承运商、物流单号是否漏填
                if (StrUtil.isBlank(carrier) || StrUtil.isBlank(appointmentTime) || CollUtil.isEmpty(trackingInfoList)) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.INCOMPLETE_LOGISTICS_INFORMATION);
                }

                // 判断是否上传快递标签
                if (shippingLabel == null) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.PLEASE_UPLOAD_SHIPPING_LABEL);
                }

                // 初始化订单附件
                if (orderAttachmentBo != null) {
                    orderAttachment = orderAttachmentBo;
                    orderAttachment.setOrderNo(orderNo);
                    iOrderAttachmentService.saveOrUpdate(orderAttachment);
                }
            } else {
                // 非自提，不需要使用一下信息，全部值为空，防止前端传值后保存进数据库
                carrier = null;
                trackingInfoList = null;
                shippingLabel = null;
                orderAttachmentBo = null;
            }

            Long wiOrderId = wiOrder.getId();
            List<WholesaleIntentionOrderItem> wiOrderItemList =
                iWholesaleIntentionOrderItemService.queryWIOrderId(wiOrderId);

            WholesaleIntentionOrderAddress wiOrderAddress =
                iWholesaleIntentionOrderAddressService.queryWIOrderId(wiOrderId);

            WholesaleIntentionOrderLogistics wiOrderLogistics =
                iWholesaleIntentionOrderLogisticsService.queryWIOrderId(wiOrderId);

            String recipientName = wiOrderAddress.getRecipientName();
            String recipientPhone = wiOrderAddress.getRecipientPhone();
            String recipientCountry = wiOrderAddress.getRecipientCountry();
            String recipientCountryCode = wiOrderAddress.getRecipientCountryCode();
            String recipientState = wiOrderAddress.getRecipientState();
            String recipientStateCode = wiOrderAddress.getRecipientStateCode();
            String recipientCity = wiOrderAddress.getRecipientCity();
            String recipientAddress1 = wiOrderAddress.getRecipientAddress1();
            String recipientAddress2 = wiOrderAddress.getRecipientAddress2();
            String recipientZipCode = wiOrderAddress.getRecipientZipCode();
            String logisticsTemplateNo = wiOrderLogistics.getLogisticsTemplateNo();
            String warehouseSystemCode = wiOrderLogistics.getWarehouseSystemCode();

            ChannelTypeEnum channelType = ChannelTypeEnum.Others;

            Orders order = new Orders();
            order.setOrderNo(orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderNo));
            order.setChannelType(channelType);
            order.setOrderType(OrderType.Wholesale);
            order.setOrderState(OrderStateType.Paid);
            order.setPayTime(new Date());
            order.setOrderNo(orderNo);
            order.setLogisticsType(logisticsType);
            iOrdersService.saveOrUpdate(order);

            OrderLogisticsInfo orderLogisticsInfo = new OrderLogisticsInfo();
            orderLogisticsInfo.setOrderId(order.getId());
            orderLogisticsInfo.setOrderNo(order.getOrderNo());
            orderLogisticsInfo.setLogisticsType(LogisticsTypeEnum.valueOf(logisticsType.name()));
            orderLogisticsInfo.setLogisticsCompanyName(carrier);
            orderLogisticsInfo.setLogisticsCountryCode(carrier);
            orderLogisticsInfo.setZipCode(StrUtil.trim(recipientZipCode));
            if (shippingLabel != null) {
                orderLogisticsInfo.setShippingLabelExist(true);
            }
            iOrderLogisticsInfoService.saveOrUpdate(orderLogisticsInfo);

            // 地址信息处理
            OrderAddressInfo orderAddressInfo = new OrderAddressInfo();
            orderAddressInfo.setOrderId(order.getId());
            orderAddressInfo.setOrderNo(order.getOrderNo());
            orderAddressInfo.setAddressType(OrderAddressType.ShipAddress);
            orderAddressInfo.setRecipient(recipientName);
            orderAddressInfo.setCountryCode(recipientCountryCode);
            orderAddressInfo.setCountry(recipientCountry);
            orderAddressInfo.setState(recipientState);
            orderAddressInfo.setStateCode(recipientStateCode);
            orderAddressInfo.setCity(recipientCity);
            orderAddressInfo.setAddress1(recipientAddress1);
            orderAddressInfo.setAddress2(recipientAddress2);
            orderAddressInfo.setPhoneNumber(recipientPhone);
            orderAddressInfo.setZipCode(StrUtil.trim(recipientZipCode));
            // 邮编处理
            if (StrUtil.contains(recipientZipCode, "-")) {
                // 存在-的邮编，需要分割出前面5位的主邮编
                String mainZipCode = StrUtil.trim(StrUtil.split(recipientZipCode, "-").get(0));
                orderLogisticsInfo.setZipCode(mainZipCode);
                orderAddressInfo.setZipCode(mainZipCode);
            }
            iOrderAddressInfoService.saveOrUpdate(orderAddressInfo);

            List<OrderItem> orderItems = new ArrayList<>();
            List<OrderItemTrackingRecord> trackingOrdersList = new ArrayList<>();
            for (WholesaleIntentionOrderItem wiOrderItem : wiOrderItemList) {
                Integer quantity = wiOrderItem.getQuantity();
                BigDecimal unitPrice = wiOrderItem.getUnitPrice();
                BigDecimal depositUnitPrice = wiOrderItem.getDepositUnitPrice();
                BigDecimal balanceUnitPrice = wiOrderItem.getBalanceUnitPrice();

                BigDecimal unitPricePlatform = wiOrderItem.getUnitPricePlatform();
                BigDecimal depositUnitPricePlatform = wiOrderItem.getDepositUnitPricePlatform();
                BigDecimal balanceUnitPricePlatform = wiOrderItem.getBalanceUnitPricePlatform();

                BigDecimal totalAmount = wiOrderItem.getTotalAmount();
                BigDecimal totalAmountPlatform = wiOrderItem.getTotalAmountPlatform();

                String productSkuCode = wiOrderItem.getProductSkuCode();
                ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
                List<ProductSkuAttachment> skuAttachmentList = iProductSkuAttachmentService.queryBySkuIdAndAttachmentTypeOrderBySortAsc(productSku.getId(), AttachmentTypeEnum.Image);
                ProductSkuAttachment firstImage = skuAttachmentList.get(0);
                Long ossId = firstImage.getOssId();
                String attachmentSavePath = firstImage.getAttachmentSavePath();
                String attachmentShowUrl = firstImage.getAttachmentShowUrl();


                OrderItem orderItem = new OrderItem();
                OrderItemProductSku orderItemProductSku = new OrderItemProductSku();

                String orderItemNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderItemNo);

                orderItem.setOrderId(order.getId());
                orderItem.setOrderNo(order.getOrderNo());
                orderItem.setOrderItemNo(orderItemNo);
                orderItem.setOrderState(OrderStateType.Paid);
                orderItem.setProductSkuCode(productSku.getProductSkuCode());
                orderItem.setLogisticsType(logisticsType);
                orderItem.setSupplierTenantId(productSku.getTenantId());
                orderItem.setTotalQuantity(quantity);
                // 委托ZSMall一件代发，付款后直接完结
                if (LogisticsTypeEnum.ZSMallDropShipping.equals(logisticsType)) {
                    orderItem.setFulfillmentProgress(LogisticsProgress.Fulfilled);
                    orderItem.setFulfillmentTime(new Date());
                } else {
                    orderItem.setFulfillmentProgress(LogisticsProgress.UnDispatched);
                }
                //子订单价格
                orderItem.setSupplierIncomeEarned(totalAmount);
                orderItem.setOriginalPrepaidUnitPrice(BigDecimal.ZERO);
                orderItem.setOriginalPayableUnitPrice(unitPrice);
                orderItem.setOriginalPayableTotalAmount(totalAmount);
                orderItem.setOriginalActualUnitPrice(unitPrice);
                orderItem.setOriginalActualTotalAmount(totalAmount);
                orderItem.setOriginalRefundExecutableAmount(totalAmount);

                orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
                orderItem.setPlatformPayableUnitPrice(unitPricePlatform);
                orderItem.setPlatformPayableTotalAmount(totalAmountPlatform);
                orderItem.setPlatformActualUnitPrice(unitPricePlatform);
                orderItem.setPlatformActualTotalAmount(totalAmountPlatform);
                orderItem.setPlatformRefundExecutableAmount(totalAmountPlatform);

                //保存
                iOrderItemService.saveOrUpdate(orderItem);
                orderItems.add(orderItem);

                // 保存子订单价格表
                OrderItemPrice orderItemPrice = new OrderItemPrice();
                orderItemPrice.setOrderItemId(orderItem.getId());
                orderItemPrice.setOrderItemNo(orderItem.getOrderItemNo());
                orderItemPrice.setProductSkuCode(productSkuCode);
                orderItemPrice.setLogisticsType(orderItem.getLogisticsType());
                orderItemPrice.setTotalQuantity(orderItem.getTotalQuantity());
                orderItemPrice.setOriginalUnitPrice(unitPrice);
                orderItemPrice.setOriginalOperationFee(BigDecimal.ZERO);
                orderItemPrice.setOriginalFinalDeliveryFee(BigDecimal.ZERO);
                orderItemPrice.setOriginalPickUpPrice(unitPrice);
                orderItemPrice.setOriginalDropShippingPrice(unitPrice);
                orderItemPrice.setOriginalDepositUnitPrice(depositUnitPrice);
                orderItemPrice.setOriginalBalanceUnitPrice(balanceUnitPrice);
                orderItemPrice.setPlatformUnitPrice(unitPricePlatform);
                orderItemPrice.setPlatformOperationFee(BigDecimal.ZERO);
                orderItemPrice.setPlatformFinalDeliveryFee(BigDecimal.ZERO);
                orderItemPrice.setPlatformPickUpPrice(unitPricePlatform);
                orderItemPrice.setPlatformDropShippingPrice(unitPricePlatform);
                orderItemPrice.setPlatformDepositUnitPrice(depositUnitPricePlatform);
                orderItemPrice.setPlatformBalanceUnitPrice(balanceUnitPricePlatform);
                iOrderItemPriceService.saveOrUpdate(orderItemPrice);

                orderItemProductSku.setProductSkuCode(productSku.getProductSkuCode());
                orderItemProductSku.setSupplierTenantId(productSku.getTenantId());
                orderItemProductSku.setOrderNo(orderNo);
                orderItemProductSku.setUpc(productSku.getUpc());
                orderItemProductSku.setOrderItemId(orderItem.getId());
                orderItemProductSku.setOrderItemNo(orderItem.getOrderItemNo());
                orderItemProductSku.setChannelType(channelType);
                orderItemProductSku.setChannelId(order.getChannelId());
                orderItemProductSku.setProductCode(productCode);
                orderItemProductSku.setProductName(product.getName());
                orderItemProductSku.setWarehouseSystemCode(warehouseSystemCode);
                orderItemProductSku.setSku(productSku.getSku());
                orderItemProductSku.setImageSavePath(attachmentSavePath);
                orderItemProductSku.setImageShowUrl(attachmentShowUrl);
                orderItemProductSku.setImageOssId(ossId);
                iOrderItemProductSkuService.saveOrUpdate(orderItemProductSku);

                wiOrderItem.setOrderItemNo(orderItemNo);

                // 处理物流信息
                if (CollUtil.isNotEmpty(trackingInfoList)) {
                    for (String trackingNo : trackingInfoList) {
                        String trimTracking = StrUtil.trim(trackingNo);

                        OrderItemTrackingRecord trackingOrders = new OrderItemTrackingRecord();
                        trackingOrders.setOrderNo(orderNo);
                        trackingOrders.setSku(productSku.getSku());
                        trackingOrders.setLogisticsCarrier(carrier);
                        trackingOrders.setSystemManaged(false);
                        trackingOrders.setLogisticsTrackingNo(trimTracking);
                        trackingOrders.setOrderItemNo(orderItemPrice.getOrderItemNo());
                        trackingOrders.setQuantity(quantity);
                        trackingOrdersList.add(trackingOrders);
                    }
                }
            }

            Integer totalQuantity = wiOrder.getTotalQuantity();
            BigDecimal productAmount = wiOrder.getProductAmount();
            BigDecimal productAmountPlatform = wiOrder.getProductAmountPlatform();
            BigDecimal finalShippingFee = wiOrder.getFinalShippingFee();
            BigDecimal finalShippingFeePlatform = wiOrder.getFinalShippingFeePlatform();
            BigDecimal finalOperationFee = wiOrder.getFinalOperationFee();
            BigDecimal finalOperationFeePlatform = wiOrder.getFinalOperationFeePlatform();
            BigDecimal orderTotalAmount = wiOrder.getOrderTotalAmount();
            BigDecimal orderTotalAmountPlatform = wiOrder.getOrderTotalAmountPlatform();

            // 订单尾款
            BigDecimal orderBalanceAmount = wiOrder.getOrderBalanceAmount();
            BigDecimal orderBalanceAmountPlatform = wiOrder.getOrderBalanceAmountPlatform();
            BigDecimal orderPayableAmount = wiOrder.getOrderPayableAmount();

            // 自提不收取运费
            if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
                orderTotalAmount = NumberUtil.sub(orderTotalAmount, finalShippingFee);
                orderTotalAmountPlatform = NumberUtil.sub(orderTotalAmountPlatform, finalShippingFeePlatform);

                orderBalanceAmount = NumberUtil.sub(orderBalanceAmount, finalShippingFee);
                orderBalanceAmountPlatform = NumberUtil.sub(orderBalanceAmountPlatform, finalShippingFeePlatform);
                orderPayableAmount = NumberUtil.sub(orderPayableAmount, finalShippingFeePlatform);

                finalShippingFee = BigDecimal.ZERO;
                finalShippingFeePlatform = BigDecimal.ZERO;

                wiOrder.setFinalShippingFee(finalShippingFee);
                wiOrder.setFinalShippingFeePlatform(finalShippingFeePlatform);

                wiOrder.setOrderTotalAmount(orderTotalAmount);
                wiOrder.setOrderTotalAmountPlatform(orderTotalAmountPlatform);
                wiOrder.setOrderBalanceAmount(orderBalanceAmount);
                wiOrder.setOrderBalanceAmountPlatform(orderBalanceAmountPlatform);
                wiOrder.setOrderPayableAmount(orderPayableAmount);
            } else if (LogisticsTypeEnum.ZSMallDropShipping.equals(logisticsType)) {  // ZSMall一件代发不收取运费和操作费

                orderTotalAmount = NumberUtil.sub(orderTotalAmount, finalShippingFee, finalOperationFee);
                orderTotalAmountPlatform = NumberUtil.sub(orderTotalAmountPlatform, finalShippingFeePlatform, finalOperationFeePlatform);

                orderBalanceAmount = NumberUtil.sub(orderBalanceAmount, finalShippingFee, finalOperationFee);
                orderBalanceAmountPlatform = NumberUtil.sub(orderBalanceAmountPlatform, finalShippingFeePlatform, finalOperationFeePlatform);
                orderPayableAmount = NumberUtil.sub(orderPayableAmount, finalShippingFeePlatform, finalOperationFeePlatform);

                finalShippingFee = BigDecimal.ZERO;
                finalShippingFeePlatform = BigDecimal.ZERO;

                finalOperationFee = BigDecimal.ZERO;
                finalOperationFeePlatform = BigDecimal.ZERO;

                wiOrder.setFinalShippingFee(finalShippingFee);
                wiOrder.setFinalShippingFeePlatform(finalShippingFeePlatform);
                wiOrder.setFinalOperationFee(finalOperationFee);
                wiOrder.setFinalOperationFeePlatform(finalOperationFeePlatform);

                wiOrder.setOrderTotalAmount(orderTotalAmount);
                wiOrder.setOrderTotalAmountPlatform(orderTotalAmountPlatform);
                wiOrder.setOrderBalanceAmount(orderBalanceAmount);
                wiOrder.setOrderBalanceAmountPlatform(orderBalanceAmountPlatform);
                wiOrder.setOrderPayableAmount(orderPayableAmount);

            }

            order.setTotalQuantity(totalQuantity);

            order.setOriginalTotalProductAmount(productAmount);
            order.setOriginalTotalOperationFee(BigDecimal.ZERO);
            order.setOriginalTotalFinalDeliveryFee(BigDecimal.ZERO);
            order.setOriginalTotalPickUpPrice(BigDecimal.ZERO);
            order.setOriginalTotalDropShippingPrice(BigDecimal.ZERO);
            order.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
            order.setOriginalPayableTotalAmount(orderTotalAmount);
            order.setOriginalActualTotalAmount(orderTotalAmount);
            order.setOriginalRefundExecutableAmount(orderTotalAmount);

            order.setPlatformTotalProductAmount(productAmountPlatform);
            order.setPlatformTotalOperationFee(BigDecimal.ZERO);
            order.setPlatformTotalFinalDeliveryFee(BigDecimal.ZERO);
            order.setPlatformTotalPickUpPrice(BigDecimal.ZERO);
            order.setPlatformTotalDropShippingPrice(BigDecimal.ZERO);
            order.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
            order.setPlatformPayableTotalAmount(orderTotalAmountPlatform);
            order.setPlatformActualTotalAmount(orderTotalAmountPlatform);
            order.setPlatformRefundExecutableAmount(orderTotalAmountPlatform);

            // 委托ZSMall一件代发，付款后直接完结
            if (LogisticsTypeEnum.ZSMallDropShipping.equals(logisticsType)) {
                order.setFulfillmentProgress(LogisticsProgress.Fulfilled);
            } else {
                order.setFulfillmentProgress(LogisticsProgress.UnDispatched);
            }

            iOrdersService.updateById(order);

            if (CollUtil.isNotEmpty(trackingOrdersList)) {
                iOrderItemTrackingRecordService.saveOrUpdateBatch(trackingOrdersList);
            }

            wiOrder.setOrderStatus(WholesaleConstant.IntentionOrderStatus.COMPLETED);
            wiOrder.setOrderStage(WholesaleConstant.IntentionOrderStage.CONFIRMED_PICKUP);
            iWholesaleIntentionOrderService.saveOrUpdate(wiOrder);
            iWholesaleIntentionOrderItemService.saveOrUpdateBatch(wiOrderItemList);

            // 自提才需要设置预约取货时间
            if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
                wiOrderLogistics.setAppointmentTime(appointmentTime);
            }
            wiOrderLogistics.setDeliveryType(logisticsType.name());
            iWholesaleIntentionOrderLogisticsService.saveOrUpdate(wiOrderLogistics);

            BigDecimal orderPayableAmount_pay = wiOrder.getOrderPayableAmount();
            log.info("{} 准备进行尾款支付，金额 = {}", wiOrderNo, orderPayableAmount_pay);
            orderSupport.wholesaleOrderPayChain(wiOrder, orderPayableAmount_pay, TransactionSubTypeEnum.WholesaleBalance);
            // 支付成功，更新物流状态信息
            List<OrderItemTrackingRecord> finalTrackingList = trackingOrdersList;
            log.info("finalTrackingList.size = {}", CollUtil.size(finalTrackingList));
            for (OrderItemTrackingRecord finalTracking : finalTrackingList) {
                thirdPartyLogisticsSupport.queryLogistics(finalTracking);
            }
            iOrderItemTrackingRecordService.saveOrUpdateBatch(finalTrackingList);

            if (LogisticsTypeEnum.ZSMallDropShipping.equals(logisticsType)) {
                // 记账
                wholesaleSupport.wholesaleOrderAddToBill(order, orderItems, wiOrder, wiOrderItemList);
                return R.ok(ZSMallStatusCodeEnum.ZSMALL_DROPSHIPPING_PAY_SUCCESS);
            }
        } else {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.LOGISTICS_METHOD_NOT_SUPPORTED);
        }
        return R.ok();
    }

    @InMethodLog(value = "查询批发订单列表")
    @Override
    public TableDataInfo<WholesaleOrderListVo> queryOrderPage(WholesaleOrderListBo bo, PageQuery pageQuery) {
        String queryType = bo.getQueryType();
        String queryValue = bo.getQueryValue();
        String status = bo.getStatus();

        WholesaleIntentionOrder wiOrder = null;
        WIOrderPageDTO wiOrderPageDTO = new WIOrderPageDTO();

        WholesaleIntentionOrderStatusEnum reqStatusEnum = null;
        if (StrUtil.isNotBlank(status)) {
            reqStatusEnum = WholesaleIntentionOrderStatusEnum.valueOf(status);
            if (reqStatusEnum != null) {
                wiOrderPageDTO.setStatus(reqStatusEnum.getCode());
            }
        }
        if (TenantType.Supplier.equals(LoginHelper.getTenantTypeEnum())) {
            wiOrderPageDTO.setSupplierTenantId(LoginHelper.getTenantId());
        }

        if (StrUtil.isNotBlank(queryType) && StrUtil.isNotBlank(queryValue)) {
            wiOrderPageDTO.setQueryType(queryType);
            wiOrderPageDTO.setQueryValue(StrUtil.trim(queryValue));
        }

        Page<WholesaleIntentionOrder> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());
        Page<WholesaleIntentionOrder> orderPage =
            iWholesaleIntentionOrderService.queryPage(wiOrderPageDTO, page);

        List<WholesaleIntentionOrder> records = orderPage.getRecords();
        List<WholesaleOrderListVo> orderList = new ArrayList<>();
        for (WholesaleIntentionOrder record : records) {
            Long wiOrderId = record.getId();
            String productCode = record.getProductCode();
            Integer orderStatus = record.getOrderStatus();
            String wiOrderNo = record.getWholesaleIntentionOrderNo();
            Date createDateTime = record.getCreateTime();

            List<WholesaleIntentionOrderItem> wholesaleIntentionOrderItems =
                iWholesaleIntentionOrderItemService.queryWIOrderId(wiOrderId);
            WholesaleIntentionOrderItem wiOrderItem = wholesaleIntentionOrderItems.get(0);
            String imageShowUrl = wiOrderItem.getImageShowUrl();
            String productName = wiOrderItem.getProductName();

            WholesaleIntentionOrderStatusEnum statusEnum = WholesaleIntentionOrderStatusEnum.fromCode(orderStatus);

            WholesaleOrderListVo orderBody = new WholesaleOrderListVo();
            orderBody.setWiOrderNo(wiOrderNo);
            orderBody.setProductCode(productCode);
            orderBody.setProductName(productName);
            orderBody.setImageShowUrl(imageShowUrl);
            orderBody.setOrderDateTime(DateUtil.format(createDateTime, dateTimeFormatter) + "(EST)");
            orderBody.setOrderStatus_zh_CN(statusEnum.getZh_CN());
            orderBody.setOrderStatus_en_US(statusEnum.getEn_US());
            orderList.add(orderBody);
        }
        return TableDataInfo.build(orderList, orderPage.getTotal());
    }

    @InMethodLog(value = "查询批发订单详情")
    @Override
    public R<WholesaleOrderVo> queryOrderDetail(WholesaleOrderBaseBo bo) {

        TenantType tenantType = LoginHelper.getTenantTypeEnum();
        String wiOrderNo = bo.getWiOrderNo();
        String shippingMethod = bo.getShippingMethod();

        WholesaleIntentionOrder wiOrder = iWholesaleIntentionOrderService.queryByWIOrderNo(wiOrderNo);

        if (wiOrder == null) {
            return R.fail(ZSMallStatusCodeEnum.WHOLESALE_ORDER_NOT_EXIST);
        }

        Date createTime = wiOrder.getCreateTime();
        Long wiOrderId = wiOrder.getId();
        List<WholesaleIntentionOrderItem> wiOrderItemList =
            iWholesaleIntentionOrderItemService.queryWIOrderId(wiOrderId);

        WholesaleIntentionOrderAddress wiOrderAddress =
            iWholesaleIntentionOrderAddressService.queryWIOrderId(wiOrderId);

        WholesaleIntentionOrderLogistics wiOrderLogistics =
            iWholesaleIntentionOrderLogisticsService.queryWIOrderId(wiOrderId);

        // 构建订单商品信息
        List<WholesaleOrderProductVo> productList = new ArrayList<>();
        for (WholesaleIntentionOrderItem wiOrderItem : wiOrderItemList) {
            String productSkuCode = wiOrderItem.getProductSkuCode();
            String imageShowUrl = wiOrderItem.getImageShowUrl();
            String productName = wiOrderItem.getProductName();
            String ownSpec = wiOrderItem.getOwnSpec();
            Integer quantity = wiOrderItem.getQuantity();

            BigDecimal unitPrice = wiOrderItem.getUnitPricePlatform();
            if (TenantType.Supplier.equals(tenantType)) {
                unitPrice = wiOrderItem.getUnitPrice();
            }

            BigDecimal totalAmount = wiOrderItem.getTotalAmountPlatform();
            if (TenantType.Supplier.equals(tenantType)) {
                totalAmount = wiOrderItem.getTotalAmount();
            }

            List<String> specValueList = new ArrayList<>();
            JSONObject ownSpecJSON = JSONUtil.parseObj(ownSpec);
            ownSpecJSON.forEach((key, value) -> {
                specValueList.add(StrUtil.toString(value));
            });

            WholesaleOrderProductVo productBody = new WholesaleOrderProductVo();
            productBody.setImageShowUrl(imageShowUrl);
            productBody.setProductName(productName);
            productBody.setItemNo(productSkuCode);
            productBody.setOwnSpec(CollUtil.join(specValueList, " "));
            productBody.setQuantity(quantity);
            productBody.setUnitPrice(DecimalUtil.bigDecimalToString(unitPrice));
            productBody.setTotalAmount(DecimalUtil.bigDecimalToString(totalAmount));
            productList.add(productBody);
        }

        // 构建订单信息
        WholesaleOrderInfoVo orderInfo = new WholesaleOrderInfoVo();
        Integer totalQuantity = wiOrder.getTotalQuantity();
        BigDecimal estimatedOperationFee = wiOrder.getEstimatedOperationFee();
        BigDecimal estimatedShippingFee = wiOrder.getEstimatedShippingFee();
        Integer finalHandleTime = wiOrder.getFinalHandleTime();
        Integer reservedTime = wiOrder.getReservedTime();
        orderInfo.setTotalQuantity(totalQuantity);
        orderInfo.setEstimatedOperationFee(DecimalUtil.bigDecimalToString(estimatedOperationFee));
        orderInfo.setEstimatedShippingFee(DecimalUtil.bigDecimalToString(estimatedShippingFee));
        orderInfo.setHandleTime(finalHandleTime);
        orderInfo.setReservedTime(reservedTime);

        // 构建订单金额信息
        WholesaleOrderAmountVo amountInfo = new WholesaleOrderAmountVo();
        BigDecimal orderTotalAmount = wiOrder.getOrderTotalAmountPlatform();
        BigDecimal productAmount = wiOrder.getProductAmountPlatform();
        BigDecimal finalOperationFee = wiOrder.getFinalOperationFeePlatform();
        BigDecimal finalShippingFee = wiOrder.getFinalShippingFeePlatform();
        BigDecimal orderPayableAmount = wiOrder.getOrderPayableAmount();
        BigDecimal orderDepositAmount = wiOrder.getOrderDepositAmountPlatform();

        if (TenantType.Supplier.equals(tenantType)) {
            orderTotalAmount = wiOrder.getOrderTotalAmount();
            productAmount = wiOrder.getProductAmount();
            finalOperationFee = wiOrder.getFinalOperationFee();
            finalShippingFee = wiOrder.getFinalShippingFee();
            orderPayableAmount = wiOrder.getOrderBalanceAmount();
            orderDepositAmount = wiOrder.getOrderDepositAmount();
        }

        amountInfo.setProductAmount(DecimalUtil.bigDecimalToString(productAmount));
        if (finalOperationFee != null) {
            amountInfo.setOperationFee(DecimalUtil.bigDecimalToString(finalOperationFee));
        }

        if (finalShippingFee != null) {
            amountInfo.setShippingFee(DecimalUtil.bigDecimalToString(finalShippingFee));
        }

        // 若是自提，则要扣除运费
        if (WholesaleDeliveryType.PickUp.name().equals(shippingMethod)) {
            amountInfo.setShippingFee("0.00");
            orderTotalAmount = NumberUtil.sub(orderTotalAmount, finalShippingFee);
            orderPayableAmount = NumberUtil.sub(orderPayableAmount, finalShippingFee);
        } else if (WholesaleDeliveryType.ZSMallDropShipping.name().equals(shippingMethod)) {
            amountInfo.setOperationFee("0.00");
            amountInfo.setShippingFee("0.00");
            orderTotalAmount = NumberUtil.sub(orderTotalAmount, finalShippingFee, finalOperationFee);
            orderPayableAmount = NumberUtil.sub(orderPayableAmount, finalShippingFee, finalOperationFee);
        }

        amountInfo.setOrderTotalAmount(DecimalUtil.bigDecimalToString(orderTotalAmount));
        amountInfo.setOrderPayableAmount(DecimalUtil.bigDecimalToString(orderPayableAmount));
        amountInfo.setOrderDepositAmount(DecimalUtil.bigDecimalToString(orderDepositAmount));

        // 构建订单收货地址信息
        WholesaleOrderAddressVo addressInfo = BeanUtil.toBean(wiOrderAddress, WholesaleOrderAddressVo.class);
        String addressIn = generateIntactAddress(wiOrderAddress.getRecipientCountryCode(),
            wiOrderAddress.getRecipientStateCode(),
            wiOrderAddress.getRecipientState(),
            wiOrderAddress.getRecipientCity(),
            wiOrderAddress.getRecipientAddress1(),
            wiOrderAddress.getRecipientAddress2());
        addressInfo.setIntactAddress(addressIn);
        // 构建订单仓库地址信息
        WholesaleOrderWarehouseVo warehouseInfo = BeanUtil.toBean(wiOrderLogistics, WholesaleOrderWarehouseVo.class);
        String warehouseAddress = generateIntactAddress(warehouseInfo.getWarehouseCountryCode(),
            warehouseInfo.getWarehouseStateCode(),
            warehouseInfo.getWarehouseState(),
            warehouseInfo.getWarehouseCity(),
            warehouseInfo.getWarehouseAddress1(),
            warehouseInfo.getWarehouseAddress2());
        warehouseInfo.setIntactAddress(warehouseAddress);

        WholesaleOrderVo orderVo = new WholesaleOrderVo();
        orderVo.setOrderInfo(orderInfo);
        orderVo.setProductList(productList);
        orderVo.setAmountInfo(amountInfo);
        orderVo.setAddressInfo(addressInfo);
        orderVo.setWarehouseInfo(warehouseInfo);
        orderVo.setOrderStage(wiOrder.getOrderStage());
        orderVo.setWiOrderNo(wiOrderNo);
        orderVo.setOrderDateTime(DateUtil.format(createTime, dateTimeFormatter) + "(EST)");

        Integer orderStatus = wiOrder.getOrderStatus();
        WholesaleIntentionOrderStatusEnum statusEnum = WholesaleIntentionOrderStatusEnum.fromCode(orderStatus);

        orderVo.setOrderStatus(statusEnum.getCode());
        orderVo.setOrderStatus_zh_CN(statusEnum.getZh_CN());
        orderVo.setOrderStatus_en_US(statusEnum.getEn_US());

        // 查询商品支持的发货方式
        String productCode = wiOrder.getProductCode();
        Product product = iProductService.queryByProductCodeAndProductTypeNotDelete(productCode, ProductTypeEnum.WholesaleProduct);
        Long productId = product.getId();
        ProductWholesaleDetail wholesaleDetail = iProductWholesaleDetailService.queryByProductId(productId);
        JSONArray deliveryType = wholesaleDetail.getDeliveryType();
        WholesaleDeliveryType[] deliveryTypes = WholesaleDeliveryType.values();
        List<String> supportDeliveryTypes = Arrays.stream(deliveryTypes).filter(item -> CollUtil.contains(deliveryType, item.name()))
            .map(WholesaleDeliveryType::name).collect(Collectors.toList());
        orderVo.setSupportDeliveryTypes(supportDeliveryTypes);

        return R.ok(orderVo);
    }

    @InMethodLog(value = "生成完整地址")
    private String generateIntactAddress(String countryCode, String stateCode, String warehouseState, String warehouseCity, String warehouseAddress1, String warehouseAddress2) {
        List<String> addressList = new LinkedList<>();
        addressList.add(StrUtil.isNotBlank(warehouseAddress2) ? warehouseAddress2 : null);
        addressList.add(StrUtil.isNotBlank(warehouseAddress1) ? warehouseAddress1 : null);
        addressList.add(warehouseCity);
        WorldLocation country = iWorldLocationService.queryByLocationCode(countryCode, LocationTypeEnum.Country);
        String headerLanguage = ServletUtils.getHeaderLanguage();
        if (StrUtil.isNotBlank(stateCode)) {
            WorldLocation state = iWorldLocationService.queryByLocationCode(stateCode, LocationTypeEnum.State);
            if (StrUtil.equals(headerLanguage, "zh_CN")) {
                addressList.add(state.getLocationOtherName().get("zh_CN").toString());
            } else {
                addressList.add(state.getLocationOtherName().get("en_US").toString());
            }
        } else {
            addressList.add(warehouseState);
        }
        if (StrUtil.equals(headerLanguage, "zh_CN")) {
            addressList.add(country.getLocationOtherName().get("zh_CN").toString());
        } else {
            addressList.add(country.getLocationOtherName().get("en_US").toString());
        }
        CollUtil.removeNull(addressList);
        return CollUtil.join(addressList, ", ");
    }

    @InMethodLog(value = "国外现货订单上传快递标签或附件")
    @Override
    public R<OrderAttachment> uploadFile(MultipartFile file, String wiOrderNo, OrderAttachmentTypeEnum orderAttachmentType) throws Exception {
        if (StrUtil.isBlank(wiOrderNo)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }
        if (file == null) {
            return R.fail(ZSMallStatusCodeEnum.UPLOAD_FILE_IS_EMPTY);
        }

        WholesaleIntentionOrder wiOrder =
            iWholesaleIntentionOrderService.queryByWIOrderNo(wiOrderNo);
        if (wiOrder == null) {
            return R.fail(ZSMallStatusCodeEnum.WHOLESALE_ORDER_NOT_EXIST);
        }
        String orderNo = wiOrder.getOrderNo();

        OSSUploadEvent ossUploadEvent = new OSSUploadEvent(file);
        SpringUtils.context().publishEvent(ossUploadEvent);
        SysOssVo sysOssVo = ossUploadEvent.getSysOssVo();
        String originalName = sysOssVo.getOriginalName();
        String suffix = FileUtil.getSuffix(originalName);

        OrderAttachment newOrderAttachment = new OrderAttachment();
        newOrderAttachment.setOssId(sysOssVo.getOssId());
        newOrderAttachment.setOrderNo(orderNo);
        newOrderAttachment.setAttachmentName(sysOssVo.getFileName());
        newOrderAttachment.setAttachmentOriginalName(originalName);
        newOrderAttachment.setAttachmentShowUrl(sysOssVo.getUrl());
        newOrderAttachment.setAttachmentSavePath(sysOssVo.getSavePath());
        newOrderAttachment.setAttachmentSuffix(suffix.toLowerCase());
        newOrderAttachment.setAttachmentType(orderAttachmentType);
        iOrderAttachmentService.deleteByOrderNoAndAttachmentType(orderNo, orderAttachmentType);
        iOrderAttachmentService.save(newOrderAttachment);

        return R.ok(newOrderAttachment);
    }

    @InMethodLog(value = "取消批发订单")
    @Override
    @Transactional(rollbackFor = {RStatusCodeException.class, StockException.class, Exception.class})
    public R<Void> cancelOrder(WholesaleOrderBaseBo bo) throws RStatusCodeException, StockException {

        String wiOrderNo = bo.getWiOrderNo();

        WholesaleIntentionOrder wiOrder =
            iWholesaleIntentionOrderService.queryByWIOrderNo(wiOrderNo);

        if (wiOrder != null) {
            Integer orderStage = wiOrder.getOrderStage();
            Integer orderStatus = wiOrder.getOrderStatus();
            if (WholesaleConstant.IntentionOrderStatus.IN_PROGRESS.equals(orderStatus)) {

                Long wiOrderId = wiOrder.getId();
                List<WholesaleIntentionOrderItem> wiOrderItemList =
                    iWholesaleIntentionOrderItemService.queryWIOrderId(wiOrderId);
                WholesaleIntentionOrderLogistics wiOrderLogistics =
                    iWholesaleIntentionOrderLogisticsService.queryWIOrderId(wiOrderId);
                String warehouseSystemCode = wiOrderLogistics.getWarehouseSystemCode();

                String productCode = wiOrder.getProductCode();
                Product product = iProductService.queryByProductCodeAndProductTypeNotDelete(productCode, ProductTypeEnum.WholesaleProduct);
                if (product != null) {
                    Long productId = product.getId();
                    List<ProductSku> productSkuList = new ArrayList<>();

                    // 准备归还库存
                    for (WholesaleIntentionOrderItem wholesaleIntentionOrderItemEntity : wiOrderItemList) {
                        String productSkuCode = wholesaleIntentionOrderItemEntity.getProductSkuCode();
                        Integer quantity = wholesaleIntentionOrderItemEntity.getQuantity();

                        ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
                        if (productSku != null) {
                            productSku.setStockChanged(true);
                            productSku.setStockChange(quantity);
                            productSkuList.add(productSku);
                        }
                    }

                    if (CollUtil.isNotEmpty(productSkuList)) {
                        this.restockInventory(productSkuList, warehouseSystemCode);
                    }
                }
                // 退回订金
                orderSupport.wholesaleRefundToWallet(wiOrderId, wiOrder.getOrderDepositAmountPlatform());

                wiOrder.setOrderStatus(WholesaleConstant.IntentionOrderStatus.CANCELED);
                iWholesaleIntentionOrderService.saveOrUpdate(wiOrder);
            } else {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.OPERATION_CANNOT_BE_PERFORMED);
            }
        } else {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.WHOLESALE_ORDER_NOT_EXIST);
        }
        return R.ok();
    }


    @InMethodLog(value = "批发订单录入价格")
    @Override
    public R<Void> enterPrice(WholesaleEnterPriceBo bo) {

        String wiOrderNo = bo.getWiOrderNo();
        BigDecimal operationFee = bo.getOperationFee();
        BigDecimal shippingFee = bo.getShippingFee();
        Integer handleTime = bo.getHandleTime();

        if (StrUtil.isBlank(wiOrderNo) || operationFee == null || shippingFee == null || handleTime == null) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        WholesaleIntentionOrder wiOrder =
            iWholesaleIntentionOrderService.queryByWIOrderNo(wiOrderNo);
        if (wiOrder != null) {
            log.info("批发订单{}，录入价格，原订单数据 = {}", wiOrderNo, JSONUtil.toJsonStr(wiOrder));

            Integer orderStage = wiOrder.getOrderStage();
            Integer orderStatus = wiOrder.getOrderStatus();
            if (WholesaleConstant.IntentionOrderStage.NOT_ENTERED_PRICE.equals(orderStage)
                && WholesaleConstant.IntentionOrderStatus.IN_PROGRESS.equals(orderStatus)) {
                wiOrder.setFinalOperationFee(operationFee);
                wiOrder.setFinalShippingFee(shippingFee);
                wiOrder.setFinalHandleTime(handleTime);

                BigDecimal finalOperationFeePlatform = wholesaleSupport.calculatePlatformPrice(operationFee);
                BigDecimal finalShippingFeePlatform = wholesaleSupport.calculatePlatformPrice(shippingFee);

                wiOrder.setFinalOperationFeePlatform(finalOperationFeePlatform);
                wiOrder.setFinalShippingFeePlatform(finalShippingFeePlatform);

                BigDecimal orderBalanceAmount = wiOrder.getOrderBalanceAmount();
                BigDecimal orderBalanceAmountPlatform = wiOrder.getOrderBalanceAmountPlatform();
                BigDecimal newOrderBalanceAmount = NumberUtil.add(orderBalanceAmount, operationFee, shippingFee);
                BigDecimal newOrderBalanceAmountPlatform = NumberUtil.add(orderBalanceAmountPlatform, finalOperationFeePlatform, finalShippingFeePlatform);
                wiOrder.setOrderBalanceAmount(newOrderBalanceAmount);
                wiOrder.setOrderBalanceAmountPlatform(newOrderBalanceAmountPlatform);
                wiOrder.setOrderPayableAmount(newOrderBalanceAmountPlatform);

                BigDecimal orderTotalAmount = wiOrder.getOrderTotalAmount();
                BigDecimal orderTotalAmountPlatform = wiOrder.getOrderTotalAmountPlatform();
                BigDecimal newOrderTotalAmount = NumberUtil.add(orderTotalAmount, operationFee, shippingFee);
                BigDecimal newOrderTotalAmountPlatform = NumberUtil.add(orderTotalAmountPlatform, finalOperationFeePlatform, finalShippingFeePlatform);
                wiOrder.setOrderTotalAmount(newOrderTotalAmount);
                wiOrder.setOrderTotalAmountPlatform(newOrderTotalAmountPlatform);

                log.info("批发订单{}，录入价格，新订单数据 = {}", wiOrderNo, JSONUtil.toJsonStr(wiOrder));
                wiOrder.setOrderStage(WholesaleConstant.IntentionOrderStage.HAS_ENTERED_PRICE);
                TenantHelper.ignore(() -> iWholesaleIntentionOrderService.saveOrUpdate(wiOrder));
            } else {
                return R.fail(ZSMallStatusCodeEnum.NOT_NEED_ENTER_PRICE);
            }
        } else {
            return R.fail(ZSMallStatusCodeEnum.ENTER_PRICE_ERROR);
        }
        return R.ok(ZSMallStatusCodeEnum.REQUEST_SUCCESS);
    }

    /**
     * 回滚库存
     *
     * @param usedProductSku
     * @param warehouseSystemCode
     */
    private void restockInventory(List<ProductSku> usedProductSku, String warehouseSystemCode) throws StockException {
        List<AdjustStockDTO> adjustStockDTOS = new ArrayList();
        for (ProductSku productSku : usedProductSku) {
            if (productSku.getStockChanged() != null && productSku.getStockChanged()) {
                AdjustStockDTO adjustStockDTO = new AdjustStockDTO();
                adjustStockDTO.setAdjustQuantity(productSku.getStockChange());
                adjustStockDTO.setProductSkuCode(productSku.getProductSkuCode());
                adjustStockDTO.setSpecifyWarehouse(warehouseSystemCode);
                adjustStockDTOS.add(adjustStockDTO);
                productSku.setStockChanged(true);
            }
        }
        productSkuStockService.adjustStock(adjustStockDTOS);
    }

    /**
     * 生成订单物流信息
     *
     * @param warehouseSystemCode
     * @return
     */
    public WholesaleIntentionOrderLogistics generateOrderLogistics(String warehouseSystemCode, String logisticsTemplateNo) {
        WholesaleIntentionOrderLogistics wiOrderLogistics = new WholesaleIntentionOrderLogistics();
        Warehouse warehouse = iWarehouseService.queryByWarehouseSystemCode(warehouseSystemCode);
        WarehouseAddressVo warehouseAddressVo = iWarehouseAddressService.queryByWarehouseId(warehouse.getId());
        Long countryId = warehouseAddressVo.getCountryId();
        Long stateId = warehouseAddressVo.getStateId();
        String state = warehouseAddressVo.getState();
        String city= warehouseAddressVo.getCity();
        WorldLocationVo country = iWorldLocationService.queryById(countryId);
        String countryCode = country.getLocationCode();
        wiOrderLogistics.setWarehouseCountryCode(countryCode);
        if (stateId != null) {
            WorldLocationVo stateVo = iWorldLocationService.queryById(stateId);
            wiOrderLogistics.setWarehouseStateCode(stateVo.getLocationCode());
            state = stateVo.getLocationName();
        }
        wiOrderLogistics.setWarehouseState(state);
        wiOrderLogistics.setWarehouseCity(city);
        String address1 = warehouseAddressVo.getAddress1();
        String address2 = warehouseAddressVo.getAddress2();
        String zipCode = warehouse.getZipCode();
        String managerName = warehouseAddressVo.getManagerName();
        String contactNumber = warehouseAddressVo.getManagerPhone();

        wiOrderLogistics.setLogisticsTemplateNo(logisticsTemplateNo);
        wiOrderLogistics.setWarehouseSystemCode(warehouseSystemCode);
        wiOrderLogistics.setWarehouseAddress1(address1);
        wiOrderLogistics.setWarehouseAddress2(address2);
        wiOrderLogistics.setWarehouseZipCode(zipCode);
        wiOrderLogistics.setWarehouseContact(managerName);
        wiOrderLogistics.setWarehousePhone(contactNumber);
        return wiOrderLogistics;
    }

    /**
     * 生成订单收货地址信息
     *
     * @param tenantShippingAddress
     * @return
     */
    public WholesaleIntentionOrderAddress generateOrderAddress(TenantShippingAddress tenantShippingAddress) {
        WholesaleIntentionOrderAddress wiOrderAddress = new WholesaleIntentionOrderAddress();

        String countryCode = tenantShippingAddress.getCountryCode();
        WorldLocation country = iWorldLocationService.queryByLocationCode(countryCode, LocationTypeEnum.Country);
        String countryName = country.getLocationOtherName().get("en_US", String.class);

        String stateCode = tenantShippingAddress.getStateCode();
        String stateText = tenantShippingAddress.getState();
        if (stateCode != null) {
            wiOrderAddress.setRecipientStateCode(stateCode);
            WorldLocation state = iWorldLocationService.queryByLocationCode(stateCode, LocationTypeEnum.State);
            stateText = state.getLocationCode();
        }
        String cityText = tenantShippingAddress.getCity();
        String fullName = tenantShippingAddress.getFullName();
        String phoneNumber = tenantShippingAddress.getPhoneNumber();
        String shippingAddress1 = tenantShippingAddress.getAddress1();
        String shippingAddress2 = tenantShippingAddress.getAddress2();
        String zipCode = tenantShippingAddress.getZipCode();
        wiOrderAddress.setRecipientCountryCode(countryCode);
        wiOrderAddress.setRecipientCountry(countryName);
        wiOrderAddress.setRecipientState(stateText);
        wiOrderAddress.setRecipientCity(cityText);
        wiOrderAddress.setRecipientAddress1(shippingAddress1);
        wiOrderAddress.setRecipientAddress2(shippingAddress2);
        wiOrderAddress.setRecipientZipCode(zipCode);
        wiOrderAddress.setRecipientName(fullName);
        wiOrderAddress.setRecipientPhone(phoneNumber);
        return wiOrderAddress;
    }

}
