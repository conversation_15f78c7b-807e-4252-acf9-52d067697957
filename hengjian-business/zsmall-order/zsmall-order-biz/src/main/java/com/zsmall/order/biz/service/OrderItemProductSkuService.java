package com.zsmall.order.biz.service;

import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.order.entity.domain.bo.OrderItemProductSkuBo;
import com.zsmall.order.entity.domain.vo.OrderItemProductSkuVo;

import java.util.Collection;
import java.util.List;

/**
 * 子订单商品SKU信息Service接口
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
public interface OrderItemProductSkuService {

    /**
     * 查询子订单商品SKU信息
     */
    OrderItemProductSkuVo queryById(Long id);

    /**
     * 查询子订单商品SKU信息列表
     */
    TableDataInfo<OrderItemProductSkuVo> queryPageList(OrderItemProductSkuBo bo, PageQuery pageQuery);

    /**
     * 查询子订单商品SKU信息列表
     */
    List<OrderItemProductSkuVo> queryList(OrderItemProductSkuBo bo);

    /**
     * 新增子订单商品SKU信息
     */
    Boolean insertByBo(OrderItemProductSkuBo bo);

    /**
     * 修改子订单商品SKU信息
     */
    Boolean updateByBo(OrderItemProductSkuBo bo);

    /**
     * 校验并批量删除子订单商品SKU信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
