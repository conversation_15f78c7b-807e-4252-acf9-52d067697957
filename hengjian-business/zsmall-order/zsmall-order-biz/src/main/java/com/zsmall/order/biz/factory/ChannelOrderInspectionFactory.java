package com.zsmall.order.biz.factory;

import com.zsmall.common.enums.common.ChannelTypeEnum;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 渠道订单检查-工厂类
 * <AUTHOR>
 * @date 2023/6/25
 */
@Component
public class ChannelOrderInspectionFactory {

    public ChannelOrderInspectionService getService(ChannelTypeEnum channelType) {
        return map.get(channelType);
    }

    private static Map<ChannelTypeEnum, ChannelOrderInspectionService> map = new HashMap<>();

    public static void register(ChannelTypeEnum channelType, ChannelOrderInspectionService thirdChannelService) throws Exception {
        if (channelType == null || thirdChannelService == null) {
            throw new Exception("订单检查工厂 - 未找到对应实例注册");
        }
        map.put(channelType, thirdChannelService);
    }

}
