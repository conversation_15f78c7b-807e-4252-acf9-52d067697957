package com.zsmall.order.biz.service.impl;

import cn.hutool.json.JSONUtil;
import com.zsmall.extend.logistics.model.response.OutNotifyBase;
import com.zsmall.extend.logistics.service.Tracking17WebhookService;
import com.zsmall.order.biz.support.Tracking17Support;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 17Tracking物流信息Webhook实现类
 *
 * <AUTHOR>
 * @date 2022/12/9
 */
@Slf4j
@Service
public class Tracking17WebhookServiceImpl implements Tracking17WebhookService {

    @Autowired
    private Tracking17Support tracking17Support;

    @Override
    public void handleTrackingStopped(OutNotifyBase outNotifyBase) {
        log.info("handleTrackingUpdated outBase = {}", JSONUtil.toJsonStr(outNotifyBase));

    }

    @Override
    public void handleTrackingUpdated(OutNotifyBase outNotifyBase) {
        tracking17Support.handleTrackingUpdated(outNotifyBase);
    }
}
