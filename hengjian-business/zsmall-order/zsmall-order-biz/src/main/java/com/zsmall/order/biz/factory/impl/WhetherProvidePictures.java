package com.zsmall.order.biz.factory.impl;

import cn.hutool.core.collection.CollUtil;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.zsmall.common.constant.OrderRefundRuleConstant;
import com.zsmall.common.domain.bo.AttachmentBo;
import com.zsmall.common.enums.order.OrderAttachmentTypeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.order.factory.RefundRuleFactory;
import com.zsmall.order.factory.RefundRuleService;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.bo.SubmitRefundApplyBo;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 是否需要图片举证
 *
 * <AUTHOR>
 * @date 2023/2/7
 */
@Service("whetherProvidePictures")
public class WhetherProvidePictures implements RefundRuleService {

    @Override
    public void afterPropertiesSet() throws Exception {
        RefundRuleFactory.register("WhetherProvidePictures", this);
    }

    /**
     * 售后规则“是否”类条件处理
     *
     * @param whetherValue
     * @param bo
     * @param orderItem
     * @param orderRefund
     * @param orderRefundItem
     */
    @Override
    public void refundRuleWhetherHandle(Integer whetherValue, SubmitRefundApplyBo bo, OrderItem orderItem,
                                        OrderRefund orderRefund, OrderRefundItem orderRefundItem) throws RStatusCodeException {

        List<AttachmentBo> attachmentBodies = bo.getAttachments();
        // 整理附件

        List<OrderRefundAttachment> attachments = new ArrayList<>();
        if (CollUtil.isNotEmpty(attachmentBodies)) {
            for (int i = 0; i < attachmentBodies.size(); i++) {
                AttachmentBo attachment = attachmentBodies.get(i);
                OrderRefundAttachment refundAttachment = new OrderRefundAttachment();
                refundAttachment.setOssId(attachment.getOssId());
                refundAttachment.setOrderRefundNo(orderRefund.getOrderRefundNo());
                refundAttachment.setAttachmentSavePath(attachment.getAttachmentSavePath());
                refundAttachment.setAttachmentShowUrl(attachment.getAttachmentShowUrl());
                refundAttachment.setAttachmentSort(i + 1);
                refundAttachment.setAttachmentType(OrderAttachmentTypeEnum.Image);
                attachments.add(refundAttachment);
            }
            orderRefund.setAttachments(attachments);
        }

        if (OrderRefundRuleConstant.WhetherProvidePictures.NOT_REQUIRED.equals(whetherValue)) {

        } else if (OrderRefundRuleConstant.WhetherProvidePictures.REQUIRED.equals(whetherValue)) {
            if (CollUtil.isEmpty(attachments)) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.PLEASE_PROVIDE_PICTURES);
            }
        }

    }

    /**
     * 售后规则“是否”类条件处理（主订单适用）
     *
     * @param whetherValue
     * @param bo
     * @param order
     * @param orderItem
     * @param orderRefund
     * @param orderRefundItem
     */
    @Override
    public void refundRuleWhetherHandle(Integer whetherValue, SubmitRefundApplyBo bo, Orders order,
                                        OrderItem orderItem, OrderRefund orderRefund, OrderRefundItem orderRefundItem) throws RStatusCodeException {
        refundRuleWhetherHandle(whetherValue, bo, orderItem, orderRefund, orderRefundItem);
    }

}
