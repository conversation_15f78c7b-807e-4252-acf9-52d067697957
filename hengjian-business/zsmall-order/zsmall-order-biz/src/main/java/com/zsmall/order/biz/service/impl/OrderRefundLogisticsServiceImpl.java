package com.zsmall.order.biz.service.impl;

import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.order.biz.service.OrderRefundLogisticsService;
import com.zsmall.order.entity.domain.OrderRefundLogistics;
import com.zsmall.order.entity.domain.bo.OrderRefundLogisticsBo;
import com.zsmall.order.entity.domain.vo.OrderRefundLogisticsVo;
import com.zsmall.order.entity.iservice.IOrderRefundLogisticsService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 售后退货物流Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-08
 */
@RequiredArgsConstructor
@Service
public class OrderRefundLogisticsServiceImpl implements OrderRefundLogisticsService {

    private final IOrderRefundLogisticsService iOrderRefundLogisticsService;

    /**
     * 查询售后退货物流
     */
    @Override
    public OrderRefundLogisticsVo queryById(Long id){
        return iOrderRefundLogisticsService.queryById(id);
    }

    /**
     * 查询售后退货物流列表
     */
    @Override
    public TableDataInfo<OrderRefundLogisticsVo> queryPageList(OrderRefundLogisticsBo bo, PageQuery pageQuery) {
        return iOrderRefundLogisticsService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询售后退货物流列表
     */
    @Override
    public List<OrderRefundLogisticsVo> queryList(OrderRefundLogisticsBo bo) {
        return iOrderRefundLogisticsService.queryList(bo);
    }

    /**
     * 新增售后退货物流
     */
    @Override
    public Boolean insertByBo(OrderRefundLogisticsBo bo) {
        return iOrderRefundLogisticsService.insertByBo(bo) ;
    }

    /**
     * 修改售后退货物流
     */
    @Override
    public Boolean updateByBo(OrderRefundLogisticsBo bo) {
        return iOrderRefundLogisticsService.updateByBo(bo);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrderRefundLogistics entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除售后退货物流
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return iOrderRefundLogisticsService.deleteWithValidByIds(ids, isValid);
    }
}
