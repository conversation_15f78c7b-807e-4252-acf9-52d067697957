package com.zsmall.order.biz.service;

import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.order.entity.domain.bo.OrderRefundItemBo;
import com.zsmall.order.entity.domain.vo.OrderRefundItemVo;

import java.util.Collection;
import java.util.List;

/**
 * 售后申请子单Service接口
 *
 * <AUTHOR>
 * @date 2023-06-08
 */
public interface OrderRefundItemService {

    /**
     * 查询售后申请子单
     */
    OrderRefundItemVo queryById(Long id);

    /**
     * 查询售后申请子单列表
     */
    TableDataInfo<OrderRefundItemVo> queryPageList(OrderRefundItemBo bo, PageQuery pageQuery);

    /**
     * 查询售后申请子单列表
     */
    List<OrderRefundItemVo> queryList(OrderRefundItemBo bo);

    /**
     * 新增售后申请子单
     */
    Boolean insertByBo(OrderRefundItemBo bo);

    /**
     * 修改售后申请子单
     */
    Boolean updateByBo(OrderRefundItemBo bo);

    /**
     * 校验并批量删除售后申请子单信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


}
