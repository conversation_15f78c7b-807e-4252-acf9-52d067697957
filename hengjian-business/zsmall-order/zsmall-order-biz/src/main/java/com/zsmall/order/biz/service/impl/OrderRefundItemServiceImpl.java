package com.zsmall.order.biz.service.impl;

import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.order.biz.service.OrderRefundItemService;
import com.zsmall.order.entity.domain.OrderRefundItem;
import com.zsmall.order.entity.domain.bo.OrderRefundItemBo;
import com.zsmall.order.entity.domain.vo.OrderRefundItemVo;
import com.zsmall.order.entity.iservice.IOrderRefundItemService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 售后申请子单Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-08
 */
@RequiredArgsConstructor
@Service
public class OrderRefundItemServiceImpl implements OrderRefundItemService {

    private final IOrderRefundItemService iOrderRefundItemService;
    /**
     * 查询售后申请子单
     */
    @Override
    public OrderRefundItemVo queryById(Long id){
        return iOrderRefundItemService.queryById(id);
    }

    /**
     * 查询售后申请子单列表
     */
    @Override
    public TableDataInfo<OrderRefundItemVo> queryPageList(OrderRefundItemBo bo, PageQuery pageQuery) {
        return iOrderRefundItemService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询售后申请子单列表
     */
    @Override
    public List<OrderRefundItemVo> queryList(OrderRefundItemBo bo) {
        return iOrderRefundItemService.queryList(bo);
    }

    /**
     * 新增售后申请子单
     */
    @Override
    public Boolean insertByBo(OrderRefundItemBo bo) {
        return iOrderRefundItemService.insertByBo(bo);
    }

    /**
     * 修改售后申请子单
     */
    @Override
    public Boolean updateByBo(OrderRefundItemBo bo) {
        return iOrderRefundItemService.updateByBo(bo);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrderRefundItem entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除售后申请子单
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return iOrderRefundItemService.deleteWithValidByIds(ids, isValid);
    }

}
