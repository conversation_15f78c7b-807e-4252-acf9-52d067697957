package com.zsmall.order.biz.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zsmall.common.domain.dto.ErpHjOrderDto;
import com.zsmall.common.domain.resp.ErpResult;
import com.zsmall.order.biz.service.PushErpOrderService;
import com.zsmall.order.entity.domain.Orders;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.*;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/4 15:58
 */
@Component
@ComponentScan
@Slf4j
public class PushToThirdUtils implements Serializable {
    @Value("${spring.profiles.active}")
    private String env;

    @Resource
    private PushErpOrderService pushErpOrderService;


    /**
     * 功能描述：推送到 ERP
     *
     * @param orders 订单
     * <AUTHOR>
     * @date 2024/01/04
     */
    public ErpResult pushToErp(Orders orders){
        String url = getApiByEnv();
        String token = getTokenByEnv();
        OkHttpClient client = new OkHttpClient();
        StringBuilder result = new StringBuilder();
        MediaType mediaType = MediaType.parse("application/json");
        BufferedReader in = null;
        ErpHjOrderDto erpHjOrderDto = pushErpOrderService.convertToErpOrderMsg(orders);
        RequestBody body = RequestBody.create(mediaType, JSON.toJSONString(erpHjOrderDto));
        try {
            Request request = new Request.Builder().url(url)
                                                   .addHeader("Content-Type", "application/json")
                                                   .addHeader("Authorization", token)
                                                   .post(body)
                                                   .build();
            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                log.info("参数:{}, 调用tiktok接口失败response code: {} and message: {}",JSON.toJSONString(erpHjOrderDto),
                    response.code(), response.message());
                // 保存错误信息
            }
            ResponseBody responseBody = response.body();
            InputStream inputStream = responseBody.byteStream();
            in = new BufferedReader(new InputStreamReader(inputStream));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            in.close();
            log.info(" tiktok response body result:{}", result);
        } catch (IOException e) {
            e.printStackTrace();
        }
        ErpResult erpResult = JSONObject.parseObject(result.toString(), ErpResult.class);
        return erpResult;
    }


    private String getTokenByEnv() {
        String token =null;
        if ("test".equals(env)) {
            token = "ALsxNxijYOkhlc3GKnU1RkWFO8k1L7oh_test";
        }
        if("dev".equals(env)){
            token = "ALsxNxijYOkhlc3GKnU1RkWFO8k1L7oh_test";
        }
        if("prod".equals(env)){
            token = "be1664e37d6650cefe15811b8dd7806c";
        }

        return token;
    }

    /**
     * 功能描述：通过 env 获取 API
     *
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/01/04
     */
    private String getApiByEnv() {
        String apiEnv = null;
        String urlStart = "https://";
        // open-api.ehenglin.com/v1/saleOrder/create  "open-api.ehengjian.com/v1/saleOrder/create";   http://test-open-api.ehengjian.com/v1/saleOrder/create
        String path = "open-api.ehengjian.com/v1/saleOrder/create";
        String url;
        if ("test".equals(env)) {
            apiEnv = "test-";
        }
        if("dev".equals(env)){
            apiEnv = "test-";
        }
        if("prod".equals(env)){

        }
        url = urlStart+apiEnv+path;
        return url;
    }
}
