package com.zsmall.order.biz.service;

import com.zsmall.product.entity.domain.ProductSkuPrice;

/**
 * lty notes 产品相关复合类
 *
 * <AUTHOR> Theo
 * @create 2024/1/11 10:17
 */

public interface IProductAboutManger {



    ProductSkuPrice getProductPriceBySellerSku(String sellerSku, Long siteId);
    ProductSkuPrice getProductPriceByProductSkuCode(String productSkuCode, Long siteId);
    ProductSkuPrice getProductPriceByProductSkuCodeAndCountryCode(String productSkuCode,String countryCode);
    ProductSkuPrice getProductPriceByItemNo(String sellerSku, Long siteId);
}
