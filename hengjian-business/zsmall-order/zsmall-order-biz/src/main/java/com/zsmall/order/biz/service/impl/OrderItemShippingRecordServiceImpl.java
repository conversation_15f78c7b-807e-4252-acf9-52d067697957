package com.zsmall.order.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.order.biz.service.OrderItemShippingRecordService;
import com.zsmall.order.entity.domain.OrderItemShippingRecord;
import com.zsmall.order.entity.domain.bo.OrderItemShippingRecordBo;
import com.zsmall.order.entity.domain.vo.OrderItemShippingRecordVo;
import com.zsmall.order.entity.mapper.OrderItemShippingRecordMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 子订单出货单Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@RequiredArgsConstructor
@Service
public class OrderItemShippingRecordServiceImpl implements OrderItemShippingRecordService {

    private final OrderItemShippingRecordMapper baseMapper;

    /**
     * 查询子订单出货单
     */
    @Override
    public OrderItemShippingRecordVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询子订单出货单列表
     */
    @Override
    public TableDataInfo<OrderItemShippingRecordVo> queryPageList(OrderItemShippingRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OrderItemShippingRecord> lqw = buildQueryWrapper(bo);
        Page<OrderItemShippingRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询子订单出货单列表
     */
    @Override
    public List<OrderItemShippingRecordVo> queryList(OrderItemShippingRecordBo bo) {
        LambdaQueryWrapper<OrderItemShippingRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OrderItemShippingRecord> buildQueryWrapper(OrderItemShippingRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrderItemShippingRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getSupplierTenantId()), OrderItemShippingRecord::getSupplierTenantId, bo.getSupplierTenantId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), OrderItemShippingRecord::getOrderNo, bo.getOrderNo());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderItemNo()), OrderItemShippingRecord::getOrderItemNo, bo.getOrderItemNo());
        lqw.eq(StringUtils.isNotBlank(bo.getChannelType()), OrderItemShippingRecord::getChannelType, bo.getChannelType());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseType()), OrderItemShippingRecord::getWarehouseType, bo.getWarehouseType());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseCode()), OrderItemShippingRecord::getWarehouseCode, bo.getWarehouseCode());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseSystemCode()), OrderItemShippingRecord::getWarehouseSystemCode, bo.getWarehouseSystemCode());
        lqw.eq(StringUtils.isNotBlank(bo.getShippingNo()), OrderItemShippingRecord::getShippingNo, bo.getShippingNo());
        lqw.eq(StringUtils.isNotBlank(bo.getShippingState()), OrderItemShippingRecord::getShippingState, bo.getShippingState());
        lqw.eq(StringUtils.isNotBlank(bo.getShippingErrorCode()), OrderItemShippingRecord::getShippingErrorCode, bo.getShippingErrorCode());
        lqw.eq(bo.getSystemManaged() != null, OrderItemShippingRecord::getSystemManaged, bo.getSystemManaged());
        return lqw;
    }

    /**
     * 新增子订单出货单
     */
    @Override
    public Boolean insertByBo(OrderItemShippingRecordBo bo) {
        OrderItemShippingRecord add = MapstructUtils.convert(bo, OrderItemShippingRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改子订单出货单
     */
    @Override
    public Boolean updateByBo(OrderItemShippingRecordBo bo) {
        OrderItemShippingRecord update = MapstructUtils.convert(bo, OrderItemShippingRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrderItemShippingRecord entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除子订单出货单
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
