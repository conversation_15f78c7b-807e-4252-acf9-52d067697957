package com.zsmall.order.biz.service;

import com.hengjian.common.core.domain.R;
import com.zsmall.common.domain.dto.OrderAttachmentDTO;
import com.zsmall.order.entity.domain.bo.OrderItemNoListBo;
import com.zsmall.order.entity.domain.bo.OssReq;
import com.zsmall.order.entity.domain.bo.order.*;
import com.zsmall.order.entity.domain.bo.refund.CancelRefundRequestBo;
import com.zsmall.order.entity.domain.bo.refund.PlatformInterventionBo;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/6/15 18:55
 */
public interface DistributorOrderService {

    /**
     * 手动确认收货
     * @param bo
     * @return
     */
    R<Void> confirmReceiptOrder(OrderItemNoListBo bo);

    /**
     * 改变订单详情
     * @param bo
     * @return
     */
    R<Void> changeOrderDetails(ChangeOrderDetailsBo bo);

    /**
     * 取消订单
     * @param bo
     * @return
     */
    R<Void> cancelOrder(OrderNoBo bo);

    /**
     *改变订单收件信息
     * @param bo
     * @return
     */
    R<Void> changeOrderRecipientInfo(ChangeOrderRecipientInfoBo bo);

    /**
     *改变渠道订单号
     * @param bo
     * @return
     */
    R<Void> changeChannelOrderId(ChangeChannelOrderBo bo);

    /**
     *改变店铺链接
     * @param bo
     * @return
     */
    R<Void> changeStoreLink(ChangeStoreLinkBo bo);

    /**
     * 取消售后申请
     * @param bo
     * @return
     */
    R<Void> cancelRefundRequest(CancelRefundRequestBo bo);

    /**
     * 申请平台介入
     * @param bo
     * @return
     */
    R<Void> platformIntervention(PlatformInterventionBo bo);

    /**
     * 处理平台介入
     * @param bo
     * @return
     */
    R<Void> handlePlatformIntervention(PlatformInterventionBo bo);

    /**
     * 上传快递标签
     * @param bo
     * @return
     */
    R<Void> uploadShippingLabel(OrderUpdateBo bo);

    /**
     * 功能描述：上传运输标签 支持单个分销单多个运输标签
     *
     * @param bo bo
     * @return {@link R }<{@link Void }>
     * <AUTHOR>
     * @date 2024/06/19
     */
    R<Void> uploadShippingLabels(OrderUpdateBo bo);

    /**
     * 上传订单附件
     * @param bo
     * @return
     */
    R<Void> uploadOrderAttachment(OrderUpdateBo bo);



    /**
     * 功能描述：上传发货标签不批量
     *
     * @param bo bo
     * <AUTHOR>
     * @date 2025/01/09
     */
    void uploadShippingLabelNotBatch(OrderUpdateBo bo);

    /**
     * amazonVC订单快递标签上传检查
     *
     * @param orderNo
     * @return
     */
    Boolean amazonVCOrderCheck(String orderNo);

    R<Void> deleteAttachment(OssReq req);

    /**
     * 功能描述：更新订单附件包(所有子订单)
     *
     * @param orderNo     订单号
     * @param attachments 附件
     * @return
     * <AUTHOR>
     * @date 2025/01/09
     */
    Set<Long> updateOrderAttachmentIncludeAll(String orderNo, List<OrderAttachmentDTO> attachments);
}
