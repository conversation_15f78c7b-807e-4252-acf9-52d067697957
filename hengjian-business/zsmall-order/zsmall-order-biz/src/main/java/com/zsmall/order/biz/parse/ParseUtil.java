package com.zsmall.order.biz.parse;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hengjian.common.core.utils.LocalDateTimeUtils;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.stream.mq.utils.MathTimeUtils;
import com.thoughtworks.xstream.XStream;
import com.zsmall.common.enums.BooleanEnum;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.enums.common.AttachmentTypeEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.*;
import com.zsmall.common.enums.orderItem.ShippingOrderStateEnum;
import com.zsmall.common.enums.product.StockManagerEnum;
import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import com.zsmall.common.enums.warehouse.WarehouseChannelCodeEnum;
import com.zsmall.common.exception.OrderException;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.common.util.RegexUtil;
import com.zsmall.order.biz.manager.OrderAndItemManager;
import com.zsmall.order.biz.service.IProductAboutManger;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.dto.*;
import com.zsmall.order.entity.iservice.IOrdersService;
import com.zsmall.order.entity.iservice.OrderCodeGenerator;
import com.zsmall.product.entity.domain.Product;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductSkuAttachment;
import com.zsmall.product.entity.iservice.IProductChannelControlService;
import com.zsmall.product.entity.iservice.IProductService;
import com.zsmall.product.entity.iservice.IProductSkuAttachmentService;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.system.entity.iservice.IWorldLocationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;

/**
 * lty notes
 *  todo 抽离模型做一个工厂,Business这块设计可以考虑状态机的设计
 *
 * <AUTHOR> Theo
 * @create 2023/12/12 15:29
 */
@Slf4j
@Component
public class ParseUtil {
    // 挪表

    @Resource
    private IProductAboutManger productAboutManger;

    @Resource
    private OrderSupport orderSupport;

    @Resource
    private IProductSkuService iProductSkuService;

    @Resource
    private IProductService iProductService;

    @Resource
    private OrderAndItemManager orderAndItemManager;

    @Resource
    private IOrdersService iOrdersService;
    @Resource
    private ITenantSalesChannelService iTenantSalesChannelService;
    @Resource
    private IProductChannelControlService iProductChannelControlService;


    //@Resource
    //private ICommonRegionService commonRegionService;

    @Resource
    private BusinessParameterService businessParameterService;

    @Autowired
    ApplicationContext applicationContext;

    @Resource
    private IWorldLocationService iWorldLocationService;

    @Resource
    private OrderCodeGenerator orderCodeGenerator;


    @Resource
    private IProductSkuAttachmentService iProductSkuAttachmentService;



    //@Resource
    //private MarListingInfoService marListingInfoService;


    @Resource
    private IProductSkuService productSkuService;

    /**
     * 根据洲全称查看简称
     *
     * @param stateCode
     * @return
     */
    private String getStateCode(String stateCode) {
        /*if (stateCode.length() > 2) {
            CommonRegion commonRegionEntity = commonRegionService.getByNameEn(RegionLevelEnum.STATE.value(), stateCode);
            if (ObjectUtil.isNotEmpty(commonRegionEntity) && ObjectUtil.isNotEmpty(commonRegionEntity.getCode())) {
                return commonRegionEntity.getCode();
            }
            return stateCode.toUpperCase();
        }*/
        return stateCode.toUpperCase();
    }

    /**
     * 时间字符串合法性检测
     *
     * @param dateTimeStr
     * @return
     */
    private boolean dateTimeStrCheck(String dateTimeStr) {
        if (ObjectUtil.isEmpty(dateTimeStr)) {
            return true;
        }
        if ("1970-01-01 00:00:00".equals(dateTimeStr)) {
            return true;
        }
        if ("0000-00-00 00:00:00".equals(dateTimeStr)) {
            return true;
        }
        return false;
    }

    /**
     * 根据shipping_status获取到channel_status的对应状态
     *
     * @param shippingStatus
     * @return
     */
    private B2cChannelStatusEnum getChannelStatus(String shippingStatus) {
        B2cChannelStatusEnum channelStatus;
        switch (shippingStatus.toLowerCase()) {
            case "refund":
                channelStatus = B2cChannelStatusEnum.refunded;
                break;
            case "canceled":
                channelStatus = B2cChannelStatusEnum.canceled;
                break;
            case "cancelled":
                channelStatus = B2cChannelStatusEnum.canceled;
                break;
            case "cancel":
                channelStatus = B2cChannelStatusEnum.canceled;
                break;
            case "pending":
                channelStatus = B2cChannelStatusEnum.pending;
                break;
            case "shipped":
                channelStatus = B2cChannelStatusEnum.shipped;
                break;
            case "unshipped":
                channelStatus = B2cChannelStatusEnum.unshipped;
                break;
            default:
                throw new OrderException("未识别到渠道状态：" + shippingStatus);
                //channelStatus = B2cChannelStatusEnum.unshipped;
        }
        return channelStatus;
    }

    private String getCountryCode(String countryCode) {
        /*if (ObjectUtil.isNotEmpty(countryCode) && countryCode.length() > 2) {
            CommonRegion commonRegionEntity = commonRegionService.getByNameEn(RegionLevelEnum.COUNTRY.value(), countryCode);
            if (ObjectUtil.isNotEmpty(commonRegionEntity)) {
                return commonRegionEntity.getCode();
            }
        }*/
        return countryCode;
    }

    /**
     * 多渠道到分销
     *
     * @return {@link Orders }
     * <AUTHOR>
     */
    public Orders erpToDistribution(ErpSaleOrderDTO erpSaleOrderDTO) {
        Orders orders = new Orders();
        // 业务属性
        orders = setOrderBusinessField(erpSaleOrderDTO, orders);
        // 单据主键标识属性
        orders = setOrderTagSystem(erpSaleOrderDTO, orders);
        // 渠道相关
        orders = setChannelTag(erpSaleOrderDTO, orders);
        return orders;
    }

    /**
     * 设置渠道相关
     *
     * @param erpSaleOrderDTO ERP销售订单DTO
     * @param orders          订单
     * @return {@link Orders }
     * <AUTHOR>
     */
    private Orders setChannelTag(ErpSaleOrderDTO erpSaleOrderDTO, Orders orders) {
        // 渠道店铺主键 channelId channelAlias channelOrderNo channelOrderName channelOrderTime payTime orderNote
        orders.setChannelType(ChannelTypeEnum.MultiChannel);
        orders.setChannelOrderNo(erpSaleOrderDTO.getOrderNo());
        // 要通过flag和店铺表内的channelAlias
        TenantSalesChannel one =TenantHelper.ignore(()->iTenantSalesChannelService.getOne(Wrappers.<TenantSalesChannel>lambdaQuery()
                                                                                                  .eq(TenantSalesChannel::getThirdChannelFlag, erpSaleOrderDTO.getChannelFlag()))) ;
        if (ObjectUtil.isNull(one) || ObjectUtil.isEmpty(one.getId())) {
            throw new OrderException("渠道店铺不存在,ChannelFlag为:" + erpSaleOrderDTO.getChannelFlag() + ",请联系管理员手动维护");
        }
        orders.setChannelId(one.getId());
        orders.setChannelAlias(ChannelTypeEnum.MultiChannel + ":" + erpSaleOrderDTO.getChannelFlag());
        orders.setChannelOrderName(erpSaleOrderDTO.getOrderNo());
        orders.setChannelOrderTime(MathTimeUtils.localDateTimeToDate(erpSaleOrderDTO.getChannelCreated()));

        return orders;
    }

    /**
     * 设置订单标签属性 orderNo 等单据唯一标识属性
     *
     * @param erpSaleOrderDTO ERP销售订单DTO
     * @param orders          订单
     * @return {@link Orders }
     * <AUTHOR>
     */
    private Orders setOrderTagSystem(ErpSaleOrderDTO erpSaleOrderDTO, Orders orders) {
        // 看系统内的状态
        String orderNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderNo);
        orders.setOrderNo(orderNo);
        // 会影响售后,国外的单不能售后,其余用于搜索查询过滤,默认给了普通订单
        orders.setOrderType(OrderType.Normal);
        // 分销商的id 分销商和 要从店铺同步
        // 从渠道店铺拿对应的分销商租户id
        LambdaQueryWrapper<TenantSalesChannel> lqw = new LambdaQueryWrapper<TenantSalesChannel>().eq(TenantSalesChannel::getThirdChannelFlag, erpSaleOrderDTO.getChannelFlag());
        TenantSalesChannel channel = TenantHelper.ignore(()->iTenantSalesChannelService.getOne(lqw)) ;
        orders.setTenantId(channel.getTenantId());
        return orders;
    }

    /**
     * 设置订单业务字段   金额 运费
     *
     * @param erpSaleOrderDTO ERP销售订单DTO
     * @param orders          订单
     * @return {@link Orders }
     * <AUTHOR>
     */
    private Orders setOrderBusinessField(ErpSaleOrderDTO erpSaleOrderDTO, Orders orders) {

        List<ErpSaleOrderItemDTO> orderItemsList = erpSaleOrderDTO.getSaleOrderItemsList();
        BigDecimal amount = BigDecimal.ZERO;
        for (ErpSaleOrderItemDTO itemDTO : orderItemsList) {
            amount.add(itemDTO.getItemAmount());
        }
        ErpSaleOrderDetailDTO saleOrderDetails = erpSaleOrderDTO.getSaleOrderDetails();

        orders.setTotalQuantity(erpSaleOrderDTO.getOrderQuantity());
        orders.setCurrency(erpSaleOrderDTO.getCurrencyCode());
        orders.setPayTime(MathTimeUtils.localDateTimeToDate(saleOrderDetails.getPaymentDate()));
        orders.setPlatformActualTotalAmount(erpSaleOrderDTO.getAmount());
        // 物流类型 PickUp-自提，DropShipping-代发 ZSMallDropShipping
        orders.setLogisticsType(LogisticsTypeEnum.DropShipping);
        // 59.99

//        orders.setOriginalTotalProductAmount();
        orders.setOriginalTotalDropShippingPrice(amount);

        orders.setPlatformActualTotalAmount(BigDecimal.ZERO);
        // 支付状态
        orders.setOrderState(OrderStateType.Paid);
        orders.setFulfillmentProgress(LogisticsProgress.UnDispatched);
        return orders;
    }


    /**
     * 将 多渠道  XML 解析为分销
     *
     * @param message 消息
     * @return {@link ErpSaleOrderDTO }
     * <AUTHOR>
     */
    public ErpSaleOrderDTO parseXmlForErpToDistribution(String message) throws Exception {

        ErpSaleOrderDTO orderObj = new ErpSaleOrderDTO();
        ErpSaleOrderDetailDTO saleOrderDetailsEntity = new ErpSaleOrderDetailDTO();
        List<ErpSaleOrderAddressDTO> erpSaleOrderAddressDTOList = Lists.newArrayList();
        ErpSaleOrderAddressDTO erpSaleOrderAddressDTO = new ErpSaleOrderAddressDTO();
        erpSaleOrderAddressDTO.setAddressFlag(AddressFlagEnum.receive);
        List<ErpSaleOrderItemDTO> orderItemList = Lists.newArrayList();
        if (message == null) {
            throw new Exception("消息队列无数据");
        }
        //将XML字符串转换为java对象
        XStream xStream = new XStream();
        xStream.alias("OrderHeader", ErpOderHeader.class);
        xStream.alias("OrderItem", ErpOrderItemDTO.class);
        ErpOderHeader orderHeader = (ErpOderHeader) xStream.fromXML(message);

        //uspo
        if (ObjectUtil.isNotEmpty(orderHeader.getCustomer_refer_no())) {
            orderObj.setCustomerReferNo(orderHeader.getCustomer_refer_no());
        }
        if (ObjectUtil.isNotEmpty(orderHeader.getShip_to_party())) {
            orderObj.setShipToParty(orderHeader.getShip_to_party());
        }
        if (ObjectUtil.isNotEmpty(orderHeader.getPurchase_order_state())) {
            orderObj.setPurchaseOrderState(orderHeader.getPurchase_order_state());
        }


        //订单头
        if (ObjectUtil.isEmpty(orderHeader.getAccount_id())) {
            throw new OrderException("订单渠道不存在");
        }
        orderObj.setChannelFlag(orderHeader.getAccount_id());
        if (ObjectUtil.isEmpty(orderHeader.getOrdernum())) {
            throw new OrderException("订单号不存在");
        }
        // 订单状态
        orderObj.setOrderStatus(getOrderStatus(orderHeader.getStatus()));
        orderObj.setOrderNo(orderHeader.getOrdernum());
        orderObj.setAmount(ObjectUtil.isEmpty(orderHeader.getAmount()) ? null : new BigDecimal(orderHeader.getAmount()));
        orderObj.setCurrencyCode(ObjectUtil.isEmpty(orderHeader.getCurrency_code()) ? null : orderHeader.getCurrency_code()
                                                                                                        .toUpperCase());
        if (ObjectUtil.isEmpty(orderHeader.getShipping_status())) {
            throw new OrderException("渠道状态为空");
        }
        orderObj.setChannelStatus(getChannelStatus(orderHeader.getShipping_status()));
        saleOrderDetailsEntity.setBuyEmail(orderHeader.getBuy_email());
        saleOrderDetailsEntity.setBuyName(orderHeader.getBuy_name());
        saleOrderDetailsEntity.setCarrierAddition(orderHeader.getCarrier_addition());
        saleOrderDetailsEntity.setDisplaySeller(orderHeader.getDisplay_seller());
        saleOrderDetailsEntity.setDisplayOrdernum(orderHeader.getDisplay_ordernum());
        saleOrderDetailsEntity.setDisplayOrderComment(orderHeader.getDisplay_order_comment());
        if (dateTimeStrCheck(orderHeader.getDisplay_order_date())) {
            saleOrderDetailsEntity.setDisplayOrderDate(null);
        } else {
            saleOrderDetailsEntity.setDisplayOrderDate(LocalDateTimeUtils.parseDateTimeStrToLocalDateTime(orderHeader.getDisplay_order_date()));
        }
        if ("MFN".equals(orderHeader.getFulfillment_channel())) {
            orderObj.setFulfillmentChannel(FulfillmentChannelEnum.MFN);
        } else if ("AFN".equals(orderHeader.getFulfillment_channel())) {
            orderObj.setFulfillmentChannel(FulfillmentChannelEnum.AFN);
        } else {
            orderObj.setFulfillmentChannel(FulfillmentChannelEnum.MFN);
        }
        saleOrderDetailsEntity.setFulfillmentPolicy(orderHeader.getFulfillment_policy());

        if (dateTimeStrCheck(orderHeader.getReceived_date())) {
            saleOrderDetailsEntity.setReceivedDate(null);
        } else {
            saleOrderDetailsEntity.setReceivedDate(LocalDateTimeUtils.parseDateTimeStrToLocalDateTime(orderHeader.getReceived_date()));
        }
        orderObj.setShipServiceLevel(orderHeader.getShip_service_level());
        saleOrderDetailsEntity.setChannelMethodCode(orderHeader.getShipping_method_code());
        orderObj.setShippingName(orderHeader.getShipping_name());
        if (ObjectUtil.isEmpty(orderHeader.getAddressline1())) {
            if (ObjectUtil.isEmpty(orderHeader.getAddressline2())) {
                erpSaleOrderAddressDTO.setAddressline1(orderHeader.getAddressline3());
            } else {
                erpSaleOrderAddressDTO.setAddressline1(orderHeader.getAddressline2());
            }
        } else {
            erpSaleOrderAddressDTO.setAddressline1(orderHeader.getAddressline1());
        }
        if (ObjectUtil.isNotEmpty(orderHeader.getBuy_id())) {
            erpSaleOrderAddressDTO.setBuyId(orderHeader.getBuy_id());
        }
        erpSaleOrderAddressDTO.setAddressline2(orderHeader.getAddressline2());
        erpSaleOrderAddressDTO.setAddressline3(orderHeader.getAddressline3());
        erpSaleOrderAddressDTO.setAddressType(orderHeader.getAddress_type());
        erpSaleOrderAddressDTO.setDistrict(orderHeader.getDistrict());
        erpSaleOrderAddressDTO.setCity(orderHeader.getCity());
        erpSaleOrderAddressDTO.setState(ObjectUtil.isEmpty(orderHeader.getState_or_region()) ? null : getStateCode(orderHeader.getState_or_region()));

        erpSaleOrderAddressDTO.setCounty(orderHeader.getCountry());
        if (ObjectUtil.isEmpty(orderHeader.getCountry_code())) {
            erpSaleOrderAddressDTO.setCountryCode(getCountryCode(erpSaleOrderAddressDTO.getCounty()));
        } else {
            erpSaleOrderAddressDTO.setCountryCode(orderHeader.getCountry_code().toUpperCase());
        }
        orderObj.setCountryCode(erpSaleOrderAddressDTO.getCountryCode());
        erpSaleOrderAddressDTO.setPostalCode(orderHeader.getPostal_code());
        erpSaleOrderAddressDTO.setPhone(orderHeader.getPhone());
        orderObj.setRemark(orderHeader.getRemark());
        if (dateTimeStrCheck(orderHeader.getCreated())) {
            throw new OrderException("订单创建时间异常");
        } else {
            orderObj.setChannelCreated(LocalDateTimeUtils.parseDateTimeStrToLocalDateTime(orderHeader.getCreated()));
            if (orderObj.getChannelCreated().compareTo(LocalDateTime.now()) > 0) {
                throw new OrderException("订单创建时间不能比当时时间还晚");
            }
        }
        if (dateTimeStrCheck(orderHeader.getUpdated())) {
            orderObj.setChannelUpdated(orderObj.getChannelCreated());
        } else {
            orderObj.setChannelUpdated(LocalDateTimeUtils.parseDateTimeStrToLocalDateTime(orderHeader.getUpdated()));
        }

        if (dateTimeStrCheck(orderHeader.getPayment_date())) {
            saleOrderDetailsEntity.setPaymentDate(null);
        } else {
            saleOrderDetailsEntity.setPaymentDate(LocalDateTimeUtils.parseDateTimeStrToLocalDateTime(orderHeader.getPayment_date()));
        }

        if (dateTimeStrCheck(orderHeader.getLatest_ship_date())) {
            orderObj.setLatestShipDate(null);
            saleOrderDetailsEntity.setChannelLatestShipDate(null);
        } else {
            orderObj.setLatestShipDate(LocalDateTimeUtils.parseDateTimeStrToLocalDateTime(orderHeader.getLatest_ship_date()));
            saleOrderDetailsEntity.setChannelLatestShipDate(LocalDateTimeUtils.parseDateTimeStrToLocalDateTime(orderHeader.getLatest_ship_date()));
        }

        if (dateTimeStrCheck(orderHeader.getEarliest_ship_date())) {
            saleOrderDetailsEntity.setEarliestShipDate(null);
        } else {
            saleOrderDetailsEntity.setEarliestShipDate(LocalDateTimeUtils.parseDateTimeStrToLocalDateTime(orderHeader.getEarliest_ship_date()));
        }

        saleOrderDetailsEntity.setCarrier(orderHeader.getCarrier());
        saleOrderDetailsEntity.setSourceCarrier(orderHeader.getCarrier());
        orderObj.setIsBusinessOrder(ObjectUtil.isEmpty(orderHeader.getIs_business_order()) || "N".equals(orderHeader.getIs_business_order()) ? BooleanEnum.N : BooleanEnum.Y);
        orderObj.setPoNumber(orderHeader.getPo_number());
        //sales_channel
        saleOrderDetailsEntity.setSalesChannel(orderHeader.getSales_channel());
        saleOrderDetailsEntity.setAmountDiscount(ObjectUtil.isEmpty(orderHeader.getAmount_discount()) ? null : new BigDecimal(orderHeader.getAmount_discount()));
        if (ObjectUtil.isNotEmpty(orderHeader.getIs_prime())) {
            orderObj.setIsPrime("false".equals(orderHeader.getIs_prime()) ? BooleanEnum.N : BooleanEnum.Y);
        } else {
            orderObj.setIsPrime(BooleanEnum.N);
        }
        saleOrderDetailsEntity.setChargeAccountNo(orderHeader.getCharge_account_no());
        saleOrderDetailsEntity.setSourceThirdPartyAccount(orderHeader.getCharge_account_no());
        saleOrderDetailsEntity.setPackingUrl(orderHeader.getPacking_url());
        if (ObjectUtil.isNotEmpty(orderHeader.getWarehouse_code())) {
            orderObj.setChannelWarehouseCode(orderHeader.getWarehouse_code());
        } else {
            if (ObjectUtil.isNotEmpty(orderHeader.getSubinventory_code())) {
                orderObj.setChannelWarehouseCode(orderHeader.getSubinventory_code());
            }
        }
        if (ObjectUtil.isNotEmpty(orderHeader.getOrg_warehouse_id())) {
            orderObj.setOrgWarehouseId(Integer.parseInt(orderHeader.getOrg_warehouse_id()));
        }
        //手工导入时会出现
        saleOrderDetailsEntity.setCodData(orderHeader.getCod_data());
        orderObj.setShippingPrice(ObjectUtil.isEmpty(orderHeader.getShipping_price()) ? null : new BigDecimal(orderHeader.getShipping_price()));
        if (ObjectUtil.isNotEmpty(orderHeader.getOrderType())) {
            orderObj.setOrderType(B2cOrderTypeEnum.valueOf(orderHeader.getOrderType()));
        }
        if (ObjectUtil.isNotEmpty(orderHeader.getOrder_flag())) {
            if (B2cOrderSignEnum.celebrity == B2cOrderSignEnum.valueOf(orderHeader.getOrder_flag())) {
                orderObj.setIsCelebrity(BooleanEnum.Y);
            } else if (B2cOrderSignEnum.sample == B2cOrderSignEnum.valueOf(orderHeader.getOrder_flag())) {
                orderObj.setIsSample(BooleanEnum.Y);
            } else if (B2cOrderSignEnum.reshipment == B2cOrderSignEnum.valueOf(orderHeader.getOrder_flag())) {
                orderObj.setIsReshipment(BooleanEnum.Y);
            }
        }
        if (ObjectUtil.isNotEmpty(orderHeader.getCreated_by())) {
            orderObj.setCreatedBy(orderHeader.getCreated_by());
        }

        if (ObjectUtil.isEmpty(orderHeader.getItems())) {
            throw new OrderException("订单ITEMS不存在");
        }

        // 获取所有的 asin
       /* List<String> asins = orderHeader.getItems().stream()
                                        .map(ErpOrderItemDTO::getAsin).filter(StringUtils::isNotEmpty)
                                        .distinct().collect(Collectors.toList());
        // 通过 asin 列表中的数据去获取对应的优惠价格
        Map<String, BigDecimal> discountPriceByAsins = new HashMap<>();
        asins.forEach(asin -> {
            try {
                // 获取 asin 对应的优惠价格
                BigDecimal discountPrice = marListingInfoService.listingDiscountPriceByAsin(asin);
                discountPriceByAsins.put(asin, discountPrice);
            } catch (Exception ex) {
                ex.printStackTrace();
                log.error("#OrderImporService# ASIN #{}# 获取优惠价格失败 #{}#", asin, ex.getMessage());
                // 超时，或者失败则 asin 对应的优惠价格先设置为 0
                discountPriceByAsins.put(asin, BigDecimal.ZERO);
            }
        });*/

        //订单行
        orderHeader.getItems().stream().forEach(itemObj -> {
            ErpSaleOrderItemDTO orderItemObj = new ErpSaleOrderItemDTO();
            if (ObjectUtil.isEmpty(itemObj.getSku())) {
                log.error("");
                throw new OrderException("订单行的seller_sku不能为空");
            }

            //uspo
            if (ObjectUtil.isNotEmpty(itemObj.getIs_back_order_allowed())) {
                orderItemObj.setIsBackOrderAllowed(itemObj.getIs_back_order_allowed());
            }
            if (ObjectUtil.isNotEmpty(itemObj.getUnit_of_measure())) {
                orderItemObj.setUnitOfMeasure(itemObj.getUnit_of_measure());
            }
            if (ObjectUtil.isNotEmpty(itemObj.getUnit_size())) {
                orderItemObj.setUnitSize(itemObj.getUnit_size());
            }

            orderItemObj.setSellerSku(itemObj.getSku().trim());
            //orderItemObj.setErpSku("");
            orderItemObj.setAsin(itemObj.getAsin());
            // 获取优惠价格
            //BigDecimal discountPrice = discountPriceByAsins.get(itemObj.getAsin());
            //orderItemObj.setDiscountPrice(discountPrice);
            orderItemObj.setTitle(itemObj.getTitle());
            orderItemObj.setPrice(new BigDecimal(itemObj.getPrice()));
            orderItemObj.setQuantity(ObjectUtil.isEmpty(itemObj.getQuantity()) ? null : new BigDecimal(itemObj.getQuantity()).intValue());
            if (ObjectUtil.isNotEmpty(itemObj.getOrder_line_status()) && "CANCELLED".equals(itemObj.getOrder_line_status())) {
                orderItemObj.setQuantity(0);
            }
            // price 和subtotal一样 sales_total_amount 是 运费税+单价
            orderItemObj.setItemAmount(ObjectUtil.isEmpty(itemObj.getSubtotal()) ? null : new BigDecimal(itemObj.getSubtotal()));
            orderItemObj.setChannelTotalSalesAmount(ObjectUtil.isEmpty(itemObj.getSales_total_amount()) ? null : new BigDecimal(itemObj.getSales_total_amount()));
            orderItemObj.setCurrencyCode(ObjectUtil.isEmpty(itemObj.getCurrency_code()) ? null : itemObj.getCurrency_code()
                                                                                                        .toUpperCase());
            orderItemObj.setDisplayComment(itemObj.getDisplay_comment());
            orderItemObj.setShipedQuantity(ObjectUtil.isEmpty(itemObj.getShiped_quantity()) ? null : new BigDecimal(itemObj.getShiped_quantity()).intValue());
            orderItemObj.setTrackNo(itemObj.getTrack_no());
            orderItemObj.setProcessPrice(ObjectUtil.isEmpty(itemObj.getProcess_price()) ? null : new BigDecimal(itemObj.getProcess_price()));
            orderItemObj.setShippingCost(ObjectUtil.isEmpty(itemObj.getShipping_cost()) ? null : new BigDecimal(itemObj.getShipping_cost()));
            orderItemObj.setShippingCostCurrencyCode(itemObj.getShipping_cost_currency_code());
            orderItemObj.setShippingPrice(ObjectUtil.isEmpty(itemObj.getShipping_price()) ? null : new BigDecimal(itemObj.getShipping_price()));
            orderItemObj.setShippingCurrencyCode(itemObj.getShipping_currency_code());
            orderItemObj.setShippingTaxPrice(ObjectUtil.isEmpty(itemObj.getShipping_tax_price()) ? null : new BigDecimal(itemObj.getShipping_tax_price()));
            orderItemObj.setShippingTaxCurrencyCode(itemObj.getShipping_tax_currency_code());
            orderItemObj.setShippingDiscountPrice(ObjectUtil.isEmpty(itemObj.getShipping_discount_price()) ? null : new BigDecimal(itemObj.getShipping_discount_price()));
            orderItemObj.setShippingDiscountCurrencyCode(itemObj.getShipping_discount_currency_code());
            orderItemObj.setTaxPrice(ObjectUtil.isEmpty(itemObj.getTax_price()) ? null : new BigDecimal(itemObj.getTax_price()));
            orderItemObj.setTaxCurrencyCode(itemObj.getTax_currency_code());
            orderItemObj.setPromotionIds(itemObj.getPromotion_ids());
            orderItemObj.setPromotionDiscountPrice(ObjectUtil.isEmpty(itemObj.getPromotion_discount_price()) ? null : new BigDecimal(itemObj.getPromotion_discount_price()));
            orderItemObj.setPromotionDiscountCurrencyCode(itemObj.getPromotion_discount_currency_code());
            orderItemObj.setGiftMessageText(itemObj.getGift_message_text());
            orderItemObj.setGiftWrapPrice(ObjectUtil.isEmpty(itemObj.getGift_wrap_price()) ? null : new BigDecimal(itemObj.getGift_wrap_price()));
            orderItemObj.setGiftWrapCurrencyCode(itemObj.getGift_wrap_currency_code());
            orderItemObj.setGiftWrapTaxPrice(ObjectUtil.isEmpty(itemObj.getGift_wrap_tax_price()) ? null : new BigDecimal(itemObj.getGift_wrap_tax_price()));
            orderItemObj.setGiftWrapTaxCurrencyCode(itemObj.getGift_wrap_tax_currency_code());
            orderItemObj.setChannelOrderItemId(itemObj.getChannel_order_item_id());
            orderItemObj.setRemark(itemObj.getRemark());

            orderItemList.add(orderItemObj);
        });
        //地址
        erpSaleOrderAddressDTOList.add(erpSaleOrderAddressDTO);

        //附属信息
        orderObj.setSaleOrderDetails(saleOrderDetailsEntity);
        orderObj.setSaleOrderAddressList(erpSaleOrderAddressDTOList);
//        订单行
        orderObj.setSaleOrderItemsList(orderItemList);
        return orderObj;
    }

    private OrderStatusEnum getOrderStatus(String status) {
        return OrderStatusEnum.getCodeByStatusDesc(status);
    }

    /**
     * ERP 到分销-订单货物信息
     *
     * @param erpSaleOrderDTO ERP销售订单DTO
     * @param orders
     * @return {@link OrderItem }
     * <AUTHOR>
     */
    public Map erpToDistributionForItem(ErpSaleOrderDTO erpSaleOrderDTO, Orders orders) {
        List<ErpSaleOrderItemDTO> saleOrderItemsList = erpSaleOrderDTO.getSaleOrderItemsList();
        List<OrderItem> orderItems = new ArrayList();
        List<OrderItemProductSku> orderItemProductSkus = new ArrayList<>();
        HashMap<String, List> hashMap = new HashMap<>();


        for (ErpSaleOrderItemDTO erpSaleOrderItemDTO : saleOrderItemsList) {
            OrderItem orderItem = new OrderItem();
            setOrderBusinessField(orderItem, erpSaleOrderDTO, orders, erpSaleOrderItemDTO);
            setOrderTagSystem(orderItem, erpSaleOrderDTO, orders, erpSaleOrderItemDTO);
            setChannelTag(orderItem, erpSaleOrderDTO, orders, erpSaleOrderItemDTO);
            orderItems.add(orderItem);
            OrderItemProductSku orderItemProductSku = new OrderItemProductSku();
            // 通过 自订单编号进行关联,
            setBusinessField(orderItemProductSku, orderItem, erpSaleOrderDTO, orders, erpSaleOrderItemDTO);
            orderItemProductSkus.add(orderItemProductSku);
        }
        hashMap.put("orderItems", orderItems);
        hashMap.put("orderItemProductSkus", orderItemProductSkus);
        return hashMap;
    }


    /**
     * 设置渠道标识
     *
     * @param orderItem       订购项目
     * @param erpSaleOrderDTO ERP销售订单DTO
     * @param orders          订单
     * @param itemDTO
     * @return {@link OrderItem }
     * <AUTHOR>
     */
    private OrderItem setChannelTag(OrderItem orderItem, ErpSaleOrderDTO erpSaleOrderDTO, Orders orders,
                                    ErpSaleOrderItemDTO itemDTO) {
//        channelId channelItemNo
        orderItem.setChannelType(ChannelTypeEnum.MultiChannel);
        orderItem.setChannelItemNo(erpSaleOrderDTO.getOrderNo());
        // 拿店铺名去渠道店铺表里找tiktok
        TenantSalesChannel one = TenantHelper.ignore(()->iTenantSalesChannelService.getOne(Wrappers.<TenantSalesChannel>lambdaQuery()
                                                                                                   .eq(TenantSalesChannel::getThirdChannelFlag, erpSaleOrderDTO.getChannelFlag())));
        orderItem.setChannelId(one.getId());
        orderItem.setChannelSaleUnitPrice(BigDecimal.ZERO);
        orderItem.setChannelSaleTotalAmount(BigDecimal.ZERO);
        return orderItem;
    }

    /**
     * 设置订单的系统标识,常量
     *
     * @param orderItem       订购项目
     * @param erpSaleOrderDTO ERP销售订单DTO
     * @param orders          订单
     * @param itemDTO
     * @return {@link OrderItem }
     * <AUTHOR>
     */
    private OrderItem setOrderTagSystem(OrderItem orderItem, ErpSaleOrderDTO erpSaleOrderDTO, Orders orders,
                                        ErpSaleOrderItemDTO itemDTO) {
        // 最好提供一个channel_id联合做一个唯一标识
        /**
         * 两个做法:1.根据sku映射表拿到对应的sku_code
         *        2.根据产品表的erp_sku做搜索--后需要找到维护此字段的方式
         * /business/product/uploadProductExcel
         * */
//            ProductSku productSku = skuMappingService.mappingSku(itemDTO.getSellerSku(), erpSaleOrderDTO.getChannelFlag());
        LambdaQueryWrapper<ProductSku> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ProductSku::getSku, itemDTO.getSellerSku());
        ProductSku productSku = productSkuService.getOne(lqw);
        orderItem.setOrderId(orders.getId());
        orderItem.setOrderNo(orders.getOrderNo());
        // 原生订单逻辑
        String orderItemNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderItemNo);
        orderItem.setOrderItemNo(orderItemNo);
        orderItem.setProductSkuCode(productSku.getProductSkuCode());
        orderItem.setTotalQuantity(itemDTO.getQuantity());
        orderItem.setStockManager(Enum.valueOf(StockManagerEnum.class, StockManagerEnum.BizArk.name()));
        // 物流类型
        orderItem.setLogisticsType(LogisticsTypeEnum.DropShipping);
        orderItem.setFulfillmentTime(null);
        orderItem.setActivityType(null);

        // 供货商租户编号-供货商的租户编号逻辑是跟着商品上的 租户id product_sku 表
        orderItem.setSupplierTenantId(productSku.getTenantId());
        // 要与产品协调 估计是建立一个henjian类似的角色 拿固定的id
        // 从商品的tenantId拿
        orderItem.setTenantId(productSku.getTenantId());

        return orderItem;
    }

    /**
     * 设置订单业务字段-后续状态机改造
     *
     * @param orderItem       订购项目
     * @param erpSaleOrderDTO ERP销售订单DTO
     * @param orders          订单
     * @param itemDTO         项目 dto
     * @return {@link OrderItem }
     * <AUTHOR>
     */
    private OrderItem setOrderBusinessField(OrderItem orderItem, ErpSaleOrderDTO erpSaleOrderDTO, Orders orders,
                                            ErpSaleOrderItemDTO itemDTO) {
        orderItem.setShippingOrderState(ShippingOrderStateEnum.Created);
        orderItem.setRestockQuantity(0);
        String status = OrderStatusEnum.getCodeByStatusDesc(erpSaleOrderDTO.getOrderStatus())
                                       .getDistriButionStatus();
        orderItem.setOrderState(OrderStateType.getByName(status));
        orderItem.setFulfillmentProgress(LogisticsProgress.Dispatched);
        orderItem.setDispatchedTime(null);
        orderItem.setFulfillmentTime(null);
        orderItem.setActivityCode(null);
        // 产品价格-分销商
        orderItem.setPlatformActualTotalAmount(itemDTO.getItemAmount());

        BigDecimal itemAmount = itemDTO.getItemAmount();
        // 价格/数量
        orderItem.setPlatformPayableUnitPrice(itemAmount.divide(BigDecimal.valueOf(itemDTO.getQuantity()),4,RoundingMode.HALF_UP));
        // 产品价格-供应商
        orderItem.setOriginalActualTotalAmount(itemDTO.getItemAmount());
        // 支付金额
        orderItem.setPlatformPayableTotalAmount(itemDTO.getChannelTotalSalesAmount());
        // 退款金额
        orderItem.setPlatformRefundExecutableAmount(itemDTO.getChannelTotalSalesAmount());

        return orderItem;
    }

//    setChannelTag setOrderTagSystem ErpOrderItemDTO


    /**
     * 功能描述：设置业务领域
     *
     * @param orderItemProductSku 订购项目 产品 SKU
     * @param orderItem           订购项目
     * @param erpSaleOrderDTO     ERP销售订单DTO
     * @param orders              订单
     * @param itemDTO             项目 dto
     * @return {@link OrderItem }
     * <AUTHOR>
     * @date 2023/12/22
     */
    private OrderItemProductSku setBusinessField(OrderItemProductSku orderItemProductSku, OrderItem orderItem,
                                                 ErpSaleOrderDTO erpSaleOrderDTO, Orders orders,
                                                 ErpSaleOrderItemDTO itemDTO) {


        orderItemProductSku.setSupplierTenantId(orderItem.getSupplierTenantId());
        orderItemProductSku.setOrderNo(orderItem.getOrderNo());
        LambdaQueryWrapper<ProductSku> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ProductSku::getErpSku, itemDTO.getSellerSku());
        ProductSku productSku = productSkuService.getOne(lqw);

        List<ProductSkuAttachment> skuAttachmentList = iProductSkuAttachmentService.queryBySkuIdAndAttachmentTypeOrderBySortAsc(productSku.getId(), AttachmentTypeEnum.Image);
        ProductSkuAttachment firstImage = skuAttachmentList.get(0);
        Long ossId = firstImage.getOssId();
        String attachmentSavePath = firstImage.getAttachmentSavePath();
        String attachmentShowUrl = firstImage.getAttachmentShowUrl();

        /*
        orderItemProductSku.setSupplierTenantId(supplierTenantId);
        orderItemProductSku.setOrderNo(orderNo);
        orderItemProductSku.setOrderItemNo(orderItemNo);
        orderItemProductSku.setSku(sku);
        orderItemProductSku.setUpc(upc);
        orderItemProductSku.setErpSku(erpSku);
        orderItemProductSku.setChannelId(channelId);
        orderItemProductSku.setChannelType(channelType);
        orderItemProductSku.setProductCode(productCode);
        orderItemProductSku.setProductSkuCode(productSkuCode);
        orderItemProductSku.setProductName(productName);
        orderItemProductSku.setDescription(description);
        orderItemProductSku.setImageOssId(ossId);
        orderItemProductSku.setImageSavePath(attachmentSavePath);
        orderItemProductSku.setImageShowUrl(attachmentShowUrl);
        orderItemProductSku.setSpecComposeName(specComposeName);
        orderItemProductSku.setSpecValName(specValName);
        orderItemProductSku.setSpecifyWarehouse(warehouseSystemCode);
        */

        orderItemProductSku.setOrderItemNo(orderItem.getOrderItemNo());
        orderItemProductSku.setChannelType(ChannelTypeEnum.MultiChannel);
        orderItemProductSku.setChannelId(orderItem.getChannelId());
        orderItemProductSku.setChannelVariantId(null);
        // 建一个恒建仓库-从sku上拿
        orderItemProductSku.setChannelWarehouseCode(null);
        orderItemProductSku.setProductCode(productSku.getProductCode());
        orderItemProductSku.setProductSkuCode(productSku.getProductSkuCode());
        orderItemProductSku.setSku(productSku.getSku());
        orderItemProductSku.setUpc(productSku.getUpc());
        // 这里的第三方不知道特指什么,理论上来说应该是要有一个映射的sku
        orderItemProductSku.setMappingSku(productSku.getErpSku());
        orderItemProductSku.setProductName(productSku.getName());
        orderItemProductSku.setDescription(itemDTO.getTitle());
        orderItemProductSku.setImageOssId(ossId);
        orderItemProductSku.setImageSavePath(attachmentSavePath);
        orderItemProductSku.setImageShowUrl(attachmentShowUrl);
        orderItemProductSku.setSpecComposeName(productSku.getSpecComposeName());
        orderItemProductSku.setSpecValName(productSku.getSpecValName());

        // 通过 orderItemProduct确认skuCode和第三方仓库编号确认 目前先不考虑
//        orderItemProductSku.setSpecifyWarehouse(null);
//        orderItemProductSku.setWarehouseSystemCode();
        // ErpSaleOrderItemDTO
        orderItemProductSku.setDescription(itemDTO.getTitle());
        return orderItemProductSku;
    }

    /**
     * 功能描述：消息验证
     *
     * @param erpSaleOrderDTO ERP销售订单DTO
     * @return
     * <AUTHOR>
     * @date 2023/12/28
     */
    public Boolean msgVerify(ErpSaleOrderDTO erpSaleOrderDTO) {

        String parameterSupport = businessParameterService.getValueFromString(BusinessParameterType.SUPPORT_COUNTRY);
        // 支持的国家
        List<String> SUPPORT_COUNTRY = JSONUtil.toList(parameterSupport, String.class);
        // 支持的州
        List<String> SUPPORT_STATE = iWorldLocationService.queryChildCodeList("US");
        // 渠道单号重复Set

        String countryCode = erpSaleOrderDTO.getCountryCode();

        List<ErpSaleOrderAddressDTO> orderAddressList = erpSaleOrderDTO.getSaleOrderAddressList();
        ErpSaleOrderAddressDTO addressDTO = orderAddressList.get(0);


        Set<String> channelOrderNoSet = new HashSet<>();
        // 地区编码未统一 暂时不用
//        if (StrUtil.equals(countryCode, "US")) {
//            // 校验州 2022-8-16新需求，仅US的才校验州
//            if (!SUPPORT_STATE.contains(addressDTO.getState())) {
//                // 异常日志
//                return Boolean.FALSE;
//            }
//            // 校验邮编 2022-8-16新需求，仅US的才校验邮编
//            if (!RegexUtil.matchUSPostalCode(addressDTO.getPostalCode())) {
//                // 异常日记
//                return Boolean.FALSE;
//            }
//        }
        List<ErpSaleOrderItemDTO> orderItemsList = erpSaleOrderDTO.getSaleOrderItemsList();


        for (ErpSaleOrderItemDTO itemDTO : orderItemsList) {
            // 仓库号,可以通过渠道写死 提前配置好仓库
            ProductSku productSku = iProductSkuService.queryByProductSkuCodeAndWarehouseSystemCode(itemDTO.getSellerSku(), WarehouseChannelCodeEnum.OTHER_CHANNEL.getWarehouseSystemCode());
            if (productSku == null) {

                continue;
            } else {
                // 如果商品被管控，则报商品不存在
                String tenantId = "1";
                boolean checkUserAllow = iProductChannelControlService.checkUserAllow(tenantId, itemDTO.getSellerSku(), ChannelTypeEnum.Others.name());

                if (!checkUserAllow) {
                    // 日志记录商品不存在
                    return Boolean.FALSE;
                }
            }
            Orders orders = iOrdersService.getOne(new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, erpSaleOrderDTO.getOrderNo())
                                                                               .last("limit 1"));
            if(ObjectUtil.isNotEmpty(orders)){
                return Boolean.FALSE;
            }

            Product product = iProductService.queryByProductSkuCode(itemDTO.getSellerSku());
            if (StrUtil.isNotBlank(erpSaleOrderDTO.getOrderNo())) {
                if (channelOrderNoSet.contains(erpSaleOrderDTO.getOrderNo())) {
                    // ?
                } else {
                    // 查询此账户所有订单判断是否有重复的，排除Canceled的
                    boolean orderExists = iOrdersService.existsChannelOrderNo(erpSaleOrderDTO.getOrderNo(), OrderStateType.Canceled);
                    // 查询导入缓存表
                    if (orderExists) {
                        return Boolean.FALSE;
                        // 存在同一个订单
                    } else {
                        channelOrderNoSet.add(erpSaleOrderDTO.getOrderNo());
                    }
                }
            }
            // 规则校验
            if (!RegexUtil.matchQuantity(itemDTO.getQuantity().toString())) {
                // 数量异常-正则表达式
                return Boolean.FALSE;
            }

            // 校验手机号
            if (StrUtil.isNotBlank(addressDTO.getPhone()) && StrUtil.length(addressDTO.getPhone()) > 80) {
                // 手机号异常
                return Boolean.FALSE;
            }

            SupportedLogisticsEnum supportedLogistics = product.getSupportedLogistics();

            // 三方默认代发
            String logisticsType = "DropShipping";
            if (StrUtil.equals(logisticsType, "DropShipping")) {
                // 仅支持自提
                if (SupportedLogisticsEnum.PickUpOnly.equals(supportedLogistics)) {
                    // 商品仅支持自提
                    log.info("订单与商品物流类型冲突,订单号:{},商品sku:{},商品仅支持自提", erpSaleOrderDTO.getOrderNo(), itemDTO.getSellerSku());
                    return Boolean.FALSE;
                }
            }


        }
        return Boolean.TRUE;
    }

    /**
     * 功能描述：ERP到物流配送
     *
     * @param erpSaleOrderDTO ERP销售订单DTO
     * @param orders          订单
     * @return {@link Map }
     * <AUTHOR>
     * @date 2023/12/28
     */
    public Map erpToDistributionForLogistics(ErpSaleOrderDTO erpSaleOrderDTO, Orders orders) {
        HashMap<String, Object> map = new HashMap<>();
        OrderLogisticsInfo orderLogisticsInfo = new OrderLogisticsInfo();
        ErpSaleOrderAddressDTO addressDTO = erpSaleOrderDTO.getSaleOrderAddressList().get(0);
        ErpSaleOrderDetailDTO details = erpSaleOrderDTO.getSaleOrderDetails();

        List<String> result = Arrays.asList(addressDTO.getPostalCode().split("-"));
        OrderAddressInfo orderAddressInfo = new OrderAddressInfo();

        orderLogisticsInfo.setOrderId(orders.getId());
        orderLogisticsInfo.setOrderNo(orders.getOrderNo());
        orderLogisticsInfo.setLogisticsType(LogisticsTypeEnum.DropShipping);
//        orderLogisticsInfo.setLogisticsAccount(logisticsAccount);
//        orderLogisticsInfo.setLogisticsAccountZipCode(logisticsAccountZipCode);
//        orderLogisticsInfo.setLogisticsCompanyName(logisticsCarrier);
//        orderLogisticsInfo.setLogisticsServiceName(logisticsServiceName);
//        orderLogisticsInfo.setLogisticsAccount(logisticsAccount);
//        orderLogisticsInfo.setLogisticsAccountZipCode(logisticsAccountZipCode);

        orderLogisticsInfo.setZipCode(StrUtil.trim(addressDTO.getPostalCode()));
        orderLogisticsInfo.setLogisticsZipCode(StrUtil.trim(result.get(0)));
//        if (StrUtil.contains(zipCode, "-")) {
//            // 存在-的邮编，需要分割出前面5位的主邮编
//            String mainZipCode = StrUtil.trim(StrUtil.split(zipCode, "-").get(0));
//            orderLogisticsInfo.setLogisticsZipCode(StrUtil.trim(mainZipCode));
//        }
        orderLogisticsInfo.setLogisticsCountryCode(addressDTO.getCountryCode());

        orderAddressInfo.setOrderId(orders.getId());
        orderAddressInfo.setOrderNo(orders.getOrderNo());
        orderAddressInfo.setAddressType(OrderAddressType.ShipAddress);
        orderAddressInfo.setRecipient(erpSaleOrderDTO.getShippingName());
        orderAddressInfo.setPhoneNumber(addressDTO.getPhone());
        orderAddressInfo.setCountry(addressDTO.getCounty());
        orderAddressInfo.setCountryCode(addressDTO.getCountryCode());
        orderAddressInfo.setState(addressDTO.getState());
        orderAddressInfo.setStateCode(addressDTO.getState());
        orderAddressInfo.setCity(addressDTO.getCity());
        orderAddressInfo.setAddress1(addressDTO.getAddressline1());
        orderAddressInfo.setAddress2(addressDTO.getAddressline2());
        orderAddressInfo.setZipCode(addressDTO.getPostalCode());

        map.put("logisticsInfo", orderLogisticsInfo);
        map.put("addressInfo", orderAddressInfo);
        return map;
    }

    /**
     * 功能描述：
     * 功能描述：ERP到订单分销
     *
     * @param orderItems 订购项目
     * @return {@link List }<{@link OrderItemPrice }>
     * <AUTHOR>
     * @date 2023/12/28
     */
    public List<OrderItemPrice> erpToDistributionForOrder(List<OrderItem> orderItems) throws Exception {

        List<OrderItemPrice> itemPrices = new ArrayList<>();
        OrderPriceCalculateDTO paramDTO = new OrderPriceCalculateDTO();

        for (OrderItem orderItem : orderItems) {
            paramDTO.setOrderItem(orderItem);
            paramDTO.setLogisticsType(LogisticsTypeEnum.DropShipping);
            OrderPriceCalculateDTO calculateDTO = orderSupport.calculationOrderItemPriceForThird(paramDTO);
            OrderItemPrice itemPrice = calculateDTO.getOrderItemPrice();
            itemPrice.setOrderItemId(orderItem.getId());
            itemPrices.add(itemPrice);
        }


        return itemPrices;

    }

    /**
     * 功能描述：测试三
     *
     * @param message 消息
     * <AUTHOR>
     * @date 2023/12/29
     */
    @Transactional(rollbackFor = Exception.class)
    public void testThird(String message) throws Exception {
        ErpSaleOrderDTO erpSaleOrderDTO = parseXmlForErpToDistribution(message);
        // 订单数据校验
        Boolean isVerify = msgVerify(erpSaleOrderDTO);
        Boolean result = Boolean.TRUE;
        if (isVerify) {
            Orders orders = erpToDistribution(erpSaleOrderDTO);
            Map map = erpToDistributionForItem(erpSaleOrderDTO, orders);
            List<OrderItemProductSku> orderItemProductSkus = (List<OrderItemProductSku>) map.get("orderItemProductSku");
            List<OrderItem> orderItems = (List<OrderItem>) map.get("orderItems");
            result = orderAndItemManager.saveOrderAndItem(orders, orderItems, orderItemProductSkus);
            Map logistics = erpToDistributionForLogistics(erpSaleOrderDTO, orders);
            List<OrderItemPrice> itemPrices = erpToDistributionForOrder(orderItems);
            orderAndItemManager.saveOrderAbout(logistics.get("logisticsInfo"), logistics.get("addressInfo"), itemPrices);
        }
    }
}
