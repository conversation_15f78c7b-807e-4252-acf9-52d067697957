package com.zsmall.order.biz.aspect.bussiness.annotation;

import com.zsmall.order.biz.aspect.bussiness.enums.TikTokBusinessEnums;
import com.zsmall.order.biz.aspect.bussiness.enums.TikTokOptEnums;

import java.lang.annotation.*;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/14 18:35
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface PullTikTok {

    TikTokBusinessEnums business() default TikTokBusinessEnums.DEFAULT;

    TikTokOptEnums opt() default TikTokOptEnums.DEFAULT ;
}
