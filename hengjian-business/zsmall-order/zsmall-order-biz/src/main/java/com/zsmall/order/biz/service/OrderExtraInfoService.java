package com.zsmall.order.biz.service;

import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.order.entity.domain.bo.OrderExtraInfoBo;
import com.zsmall.order.entity.domain.vo.OrderExtraInfoVo;

import java.util.Collection;
import java.util.List;

/**
 * 主订单额外信息Service接口
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
public interface OrderExtraInfoService {

    /**
     * 查询主订单额外信息
     */
    OrderExtraInfoVo queryById(Long id);

    /**
     * 查询主订单额外信息列表
     */
    TableDataInfo<OrderExtraInfoVo> queryPageList(OrderExtraInfoBo bo, PageQuery pageQuery);

    /**
     * 查询主订单额外信息列表
     */
    List<OrderExtraInfoVo> queryList(OrderExtraInfoBo bo);

    /**
     * 新增主订单额外信息
     */
    Boolean insertByBo(OrderExtraInfoBo bo);

    /**
     * 修改主订单额外信息
     */
    Boolean updateByBo(OrderExtraInfoBo bo);

    /**
     * 校验并批量删除主订单额外信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
