package com.zsmall.order.biz.support;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.WholesaleIntentionOrder;
import com.zsmall.order.entity.domain.WholesaleIntentionOrderItem;
import com.zsmall.order.entity.iservice.IOrderItemService;
import com.zsmall.order.entity.iservice.IWholesaleIntentionOrderItemService;
import com.zsmall.order.entity.iservice.IWholesaleIntentionOrderService;
import com.zsmall.product.entity.domain.Product;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductSkuWholesalePrice;
import com.zsmall.product.entity.domain.ProductWholesaleTieredPrice;
import com.zsmall.product.entity.domain.dto.wholesale.WholesaleTieredPriceMatchDTO;
import com.zsmall.product.entity.iservice.IProductService;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.product.entity.iservice.IProductSkuWholesalePriceService;
import com.zsmall.product.entity.iservice.IProductWholesaleTieredPriceService;
import com.zsmall.system.biz.support.BillSupport;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 国外现货批发-业务支持类
 *
 * <AUTHOR>
 * @date 2023/2/22
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WholesaleOrderSupport {

    private final BusinessParameterService businessParameterService;
    private final IOrderItemService iOrderItemService;
    private final IWholesaleIntentionOrderService iWholesaleIntentionOrderService;
    private final IWholesaleIntentionOrderItemService iWholesaleIntentionOrderItemService;
    private final BillSupport billSupport;
    private final IProductService iProductService;
    private final IProductSkuService iProductSkuService;
    private final IProductWholesaleTieredPriceService iProductWholesaleTieredPriceService;
    private final IProductSkuWholesalePriceService iProductSkuWholesalePriceService;


    /**
     * 匹配阶梯价
     * @param productCode
     * @param totalQuantity
     * @return
     */
    public WholesaleTieredPriceMatchDTO tieredPriceMatch(String productCode, Integer totalQuantity) throws RStatusCodeException {
        Assert.notNull(productCode);
        Assert.notNull(totalQuantity);
        if (ObjectUtil.isEmpty(productCode) || ObjectUtil.isEmpty(totalQuantity)) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }
        log.info("商品编号 productCode = {}, totalQuantity = {}", productCode, totalQuantity);
        Product product = iProductService.queryByProductCodeNotTenant(productCode);
        ProductWholesaleTieredPrice tieredPrice = iProductWholesaleTieredPriceService.queryListByProductIdAndQuantity(product.getId(), totalQuantity);
        if (ObjectUtil.isEmpty(tieredPrice)) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.WHOLESALE_TIERED_PRICE_NOT_EXIST);
        }
        Long tieredPriceId = tieredPrice.getId();
        List<ProductSkuWholesalePrice> skuWholesalePrices = iProductSkuWholesalePriceService.queryListByTieredPriceId(tieredPriceId);
        List<ProductSku> productSkus = iProductSkuService.queryByProductIdNotDelete(product.getId());

        Map<String, ProductSkuWholesalePrice> productSkuWholesalePriceMap = new HashMap<>();
        for (ProductSku sku: productSkus) {
            List<ProductSkuWholesalePrice> priceEntities = skuWholesalePrices.stream().filter(p -> ObjectUtil.equals(p.getProductSkuId(), sku.getId())).collect(Collectors.toList());
            if (CollUtil.size(priceEntities) > 0) {
                productSkuWholesalePriceMap.put(sku.getProductSkuCode(), priceEntities.get(0));
            }
        }
        WholesaleTieredPriceMatchDTO dto = new WholesaleTieredPriceMatchDTO();
        dto.setTieredPrice(tieredPrice);
        dto.setProductSkuWholesalePriceMap(productSkuWholesalePriceMap);
        return dto;
    }

    /**
     * 计算平台价格（结果四舍五入取两为小数）
     *
     * @param originPrice
     * @return
     * @throws IllegalArgumentException（入参为空时会抛出）
     */
    public BigDecimal calculatePlatformPrice(BigDecimal originPrice) throws IllegalArgumentException {
        Assert.notNull(originPrice);
        log.info("计算平台价格 originPrice = {}");
        BigDecimal ratio =
            businessParameterService.getValueFromBigDecimal(BusinessParameterType.WHOLESALE_PLATFORM_PRICE_RATIO);
        ratio = ratio.add(BigDecimal.ONE);
        BigDecimal result = NumberUtil.mul(originPrice, ratio).setScale(2, RoundingMode.HALF_UP);
        log.info("计算平台价格 ratio = {} result = {}", ratio, result);
        return result;
    }

    /**
     * 现货订单记账（异步）
     *
     * @param wiOrder
     */
    public void wholesaleOrderAddToBill(Orders order, List<OrderItem> orderItems, WholesaleIntentionOrder wiOrder, List<WholesaleIntentionOrderItem> wiOrderItems) {
        log.info("现货订单记账（异步） wiOrder = {}", JSONUtil.toJsonStr(wiOrder));
        ThreadUtil.execute(() -> billSupport.generateBillDTOByWholesaleOrder(order, orderItems, wiOrder, wiOrderItems, null));
    }

    /**
     * 现货订单记账（异步）
     *
     * @param order
     */
    public void wholesaleOrderAddToBill(Orders order) {
        String orderNo = order.getOrderNo();
        WholesaleIntentionOrder wiOrder =
            iWholesaleIntentionOrderService.queryByOrderNo(orderNo);
        List<WholesaleIntentionOrderItem> wiOrderItems =
            iWholesaleIntentionOrderItemService.queryWIOrderId(wiOrder.getId());
        List<OrderItem> orderItems = iOrderItemService.getListByOrderId(order.getId());

        log.info("现货订单记账（异步） wiOrder = {}", JSONUtil.toJsonStr(wiOrder));
        ThreadUtil.execute(() -> billSupport.generateBillDTOByWholesaleOrder(order, orderItems, wiOrder, wiOrderItems, null));
    }

}
