package com.zsmall.order.biz.handler.impl;

import com.thoughtworks.xstream.converters.basic.AbstractSingleValueConverter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024年8月6日  15:54
 * @description: XML自定义转换器，处理为null的字段
 */
public class CustomBigDecimalConverter extends AbstractSingleValueConverter {

    public boolean shouldFlushPassiveNulls() {
        return true;
    }

    @Override
    public boolean canConvert(Class type) {
        return type.equals(BigDecimal.class);
    }

    @Override
    public Object fromString(String str) {
        if (str == null || str.trim().isEmpty()) {
            return null;
        }
        try {
            return new BigDecimal(str);
        } catch (NumberFormatException e) {
            throw new RuntimeException(e);
        }
    }
}
