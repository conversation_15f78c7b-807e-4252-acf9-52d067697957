package com.zsmall.order.biz.service;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.common.enums.order.OrderAttachmentTypeEnum;
import com.zsmall.common.exception.StockException;
import com.zsmall.order.entity.domain.OrderAttachment;
import com.zsmall.order.entity.domain.bo.order.PlaceOrderBo;
import com.zsmall.order.entity.domain.bo.wholesaleOrder.WholesaleOrderBaseBo;
import com.zsmall.order.entity.domain.bo.wholesaleOrder.WholesaleOrderListBo;
import com.zsmall.order.entity.domain.bo.wholesaleOrder.WholesalePlaceOrderBo;
import com.zsmall.order.entity.domain.vo.wholesaleOrder.WholesaleOrderListVo;
import com.zsmall.order.entity.domain.vo.wholesaleOrder.WholesaleOrderVo;
import com.zsmall.product.entity.domain.bo.wholesale.WholesaleEnterPriceBo;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2023/7/24 9:45
 */
public interface OrderWholesaleService {

    /**
     * 下现货批发意向订单
     * @param apiRequest
     * @return
     */
    R<Void> placeIntentionOrder(WholesalePlaceOrderBo apiRequest);

    /**
     * 下订单（支付尾款）
     * @param apiRequest
     * @return
     * @throws Exception
     */
    R<Void> placeOrder(PlaceOrderBo apiRequest);

    /**
     * 查询批发订单列表
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<WholesaleOrderListVo> queryOrderPage(WholesaleOrderListBo bo, PageQuery pageQuery);

    /**
     * 查询批发订单详情
     * @param bo
     * @return
     */
    R<WholesaleOrderVo> queryOrderDetail(WholesaleOrderBaseBo bo);

    /**
     * 国外现货订单上传快递标签
     * @param file
     * @return
     */
    R<OrderAttachment> uploadFile(MultipartFile file, String wiOrderNo, OrderAttachmentTypeEnum orderAttachmentType) throws Exception;

    /**
     * 取消批发订单
     * @param bo
     * @return
     */
    R<Void> cancelOrder(WholesaleOrderBaseBo bo) throws RStatusCodeException, StockException;

    /**
     * 批发订单录入价格
     * @param bo
     * @return
     */
    R<Void> enterPrice(WholesaleEnterPriceBo bo);


}
