package com.zsmall.order.biz.service;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.order.entity.domain.bo.orderImport.*;
import com.zsmall.order.entity.domain.vo.orderImport.OrderImportRecordDetailVo;
import com.zsmall.order.entity.domain.vo.orderImport.OrderImportRecordVo;
import com.zsmall.order.entity.domain.vo.orderImport.TempOrder2OrdersVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 订单导入记录-业务接口层
 *
 * <AUTHOR>
 * @date 2023/6/8
 */
public interface OrderImportRecordService {

    /**
     * 分页查询查询订单导入记录列表
     * @param pageQuery
     * @return
     */
    TableDataInfo<OrderImportRecordVo> queryPageList(OrderImportRecordBo bo, PageQuery pageQuery);

    /**
     * 上传订单Excel
     * @param file
     * @return
     */
    R<OrderImportRecordVo> uploadExcel(MultipartFile file) throws Exception;

    /**
     * 临时订单转正式订单
     * @param bo
     * @return
     * @throws Exception
     */
    R<TempOrder2OrdersVo> tempOrder2Orders(TempOrder2OrdersBo bo) throws Exception;
    R<TempOrder2OrdersVo> tempOrder2OrdersV2(TempOrder2OrdersBo bo) throws Exception;
    /**
     * 查询导入记录详情
     * @param bo
     * @return
     */
    R<OrderImportRecordDetailVo> queryImportRecordDetail(OrderImportRecordBo bo);

    /**
     * 更新临时订单
     */
    R<Void> updateTempOrder(TempOrderUpdateBo bo, String updateType) throws Exception;
    R<Void> updateTempOrder(TempOrderUpdateV2Bo bo, String updateType) throws Exception;

    /**
     * 更新购物车临时订单
     */
    R<Void> updateShippingCartTempOrder(ShippingCartTempOrderUpdateBo bo);

    /**
     * 批量上传快递标签
     * @param recordNo
     * @param fileList
     * @return
     */
    R<Void> batchUploadShippingLabel(String recordNo, List<MultipartFile> fileList);

    /**
     * 删除临时订单
     * @param bo
     * @return
     */
    R<Void> deleteTempOrder(TempOrderUpdateBo bo);

    /**
     * 取消导入
     * @param
     * @return
     */
    R<Void> cancelImport(OrderImportRecordBo bo);

    /**
     * 功能描述：批量上传渠道订单发货标签
     *
     * @param orderNo  订单号
     * @param fileList 文件列表
     * @return {@link R }<{@link Void }>
     * <AUTHOR>
     * @date 2024/02/29
     */
    R<Void> batchUploadShippingLabelForChannelOrder(String orderNo, List<MultipartFile> fileList);

    R<Void> updateForChannelOrder(String tempOrderUpdateBo, String deleteShippingLabel);
}
