package com.zsmall.order.biz.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.zsmall.common.domain.tiktok.domain.dto.order.TikTokLineOrderCompensation;

/**
* <AUTHOR>
* @description 针对表【tiktok_line_order_compensation】的数据库操作Service
* @createDate 2024-06-04 17:19:19
*/
public interface TiktokLineOrderCompensationService extends IService<TikTokLineOrderCompensation> {

    void cancellationCompensationPlan(String type);
}
