package com.zsmall.order.biz.service;

import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.bo.OrderRefundRuleBo;
import com.zsmall.order.entity.domain.vo.OrderRefundRuleVo;

import java.util.Collection;
import java.util.List;

/**
 * 售后规则Service接口
 *
 * <AUTHOR> Li
 * @date 2023-06-08
 */
public interface OrderRefundRuleService {

    /**
     * 查询售后规则
     */
    OrderRefundRuleVo queryById(Long id);

    /**
     * 查询售后规则列表
     */
    TableDataInfo<OrderRefundRuleVo> queryPageList(OrderRefundRuleBo bo, PageQuery pageQuery);

    /**
     * 查询售后规则列表
     */
    List<OrderRefundRuleVo> queryList(OrderRefundRuleBo bo);

    /**
     * 新增售后规则
     */
    Boolean insertByBo(OrderRefundRuleBo bo);

    /**
     * 修改售后规则
     */
    Boolean updateByBo(OrderRefundRuleBo bo);

    /**
     * 校验并批量删除售后规则信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 判断子订单是否符合退货规则
     * @param orderItem
     * @param existsOrderRefund
     * @return
     */
    Boolean compareRefundRules(OrderItem orderItem, Boolean existsOrderRefund);
}
