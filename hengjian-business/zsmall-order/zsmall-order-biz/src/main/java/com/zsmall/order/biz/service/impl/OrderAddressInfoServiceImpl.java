package com.zsmall.order.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.order.biz.service.OrderAddressInfoService;
import com.zsmall.order.entity.domain.OrderAddressInfo;
import com.zsmall.order.entity.domain.bo.OrderAddressInfoBo;
import com.zsmall.order.entity.domain.vo.OrderAddressInfoVo;
import com.zsmall.order.entity.mapper.OrderAddressInfoMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 主订单地址信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@RequiredArgsConstructor
@Service
public class OrderAddressInfoServiceImpl implements OrderAddressInfoService {

    private final OrderAddressInfoMapper baseMapper;

    /**
     * 查询主订单地址信息
     */
    @Override
    public OrderAddressInfoVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询主订单地址信息列表
     */
    @Override
    public TableDataInfo<OrderAddressInfoVo> queryPageList(OrderAddressInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OrderAddressInfo> lqw = buildQueryWrapper(bo);
        Page<OrderAddressInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询主订单地址信息列表
     */
    @Override
    public List<OrderAddressInfoVo> queryList(OrderAddressInfoBo bo) {
        LambdaQueryWrapper<OrderAddressInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OrderAddressInfo> buildQueryWrapper(OrderAddressInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrderAddressInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getOrderId() != null, OrderAddressInfo::getOrderId, bo.getOrderId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), OrderAddressInfo::getOrderNo, bo.getOrderNo());
        lqw.eq(StringUtils.isNotBlank(bo.getAddressType()), OrderAddressInfo::getAddressType, bo.getAddressType());
        lqw.eq(StringUtils.isNotBlank(bo.getRecipient()), OrderAddressInfo::getRecipient, bo.getRecipient());
        lqw.eq(StringUtils.isNotBlank(bo.getCountry()), OrderAddressInfo::getCountry, bo.getCountry());
        lqw.eq(StringUtils.isNotBlank(bo.getCountryCode()), OrderAddressInfo::getCountryCode, bo.getCountryCode());
        lqw.eq(StringUtils.isNotBlank(bo.getState()), OrderAddressInfo::getState, bo.getState());
        lqw.eq(StringUtils.isNotBlank(bo.getStateCode()), OrderAddressInfo::getStateCode, bo.getStateCode());
        lqw.eq(StringUtils.isNotBlank(bo.getCity()), OrderAddressInfo::getCity, bo.getCity());
        lqw.eq(StringUtils.isNotBlank(bo.getAddress1()), OrderAddressInfo::getAddress1, bo.getAddress1());
        lqw.eq(StringUtils.isNotBlank(bo.getAddress2()), OrderAddressInfo::getAddress2, bo.getAddress2());
        lqw.eq(StringUtils.isNotBlank(bo.getEmail()), OrderAddressInfo::getEmail, bo.getEmail());
        lqw.eq(StringUtils.isNotBlank(bo.getPhoneNumber()), OrderAddressInfo::getPhoneNumber, bo.getPhoneNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getZipCode()), OrderAddressInfo::getZipCode, bo.getZipCode());
        return lqw;
    }

    /**
     * 新增主订单地址信息
     */
    @Override
    public Boolean insertByBo(OrderAddressInfoBo bo) {
        OrderAddressInfo add = MapstructUtils.convert(bo, OrderAddressInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改主订单地址信息
     */
    @Override
    public Boolean updateByBo(OrderAddressInfoBo bo) {
        OrderAddressInfo update = MapstructUtils.convert(bo, OrderAddressInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrderAddressInfo entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除主订单地址信息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
