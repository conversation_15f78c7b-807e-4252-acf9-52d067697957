package com.zsmall.order.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.order.biz.service.OrderExtraInfoService;
import com.zsmall.order.entity.domain.OrderExtraInfo;
import com.zsmall.order.entity.domain.bo.OrderExtraInfoBo;
import com.zsmall.order.entity.domain.vo.OrderExtraInfoVo;
import com.zsmall.order.entity.mapper.OrderExtraInfoMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 主订单额外信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@RequiredArgsConstructor
@Service
public class OrderExtraInfoServiceImpl implements OrderExtraInfoService {

    private final OrderExtraInfoMapper baseMapper;

    /**
     * 查询主订单额外信息
     */
    @Override
    public OrderExtraInfoVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询主订单额外信息列表
     */
    @Override
    public TableDataInfo<OrderExtraInfoVo> queryPageList(OrderExtraInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OrderExtraInfo> lqw = buildQueryWrapper(bo);
        Page<OrderExtraInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询主订单额外信息列表
     */
    @Override
    public List<OrderExtraInfoVo> queryList(OrderExtraInfoBo bo) {
        LambdaQueryWrapper<OrderExtraInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OrderExtraInfo> buildQueryWrapper(OrderExtraInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrderExtraInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), OrderExtraInfo::getOrderNo, bo.getOrderNo());
        lqw.like(StringUtils.isNotBlank(bo.getEntityName()), OrderExtraInfo::getEntityName, bo.getEntityName());
        lqw.eq(StringUtils.isNotBlank(bo.getInfoType()), OrderExtraInfo::getInfoType, bo.getInfoType());
        return lqw;
    }

    /**
     * 新增主订单额外信息
     */
    @Override
    public Boolean insertByBo(OrderExtraInfoBo bo) {
        OrderExtraInfo add = MapstructUtils.convert(bo, OrderExtraInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改主订单额外信息
     */
    @Override
    public Boolean updateByBo(OrderExtraInfoBo bo) {
        OrderExtraInfo update = MapstructUtils.convert(bo, OrderExtraInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrderExtraInfo entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除主订单额外信息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
