package com.zsmall.order.biz.service;

import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.order.entity.domain.bo.OrderItemShippingRecordBo;
import com.zsmall.order.entity.domain.vo.OrderItemShippingRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * 子订单出货单Service接口
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
public interface OrderItemShippingRecordService {

    /**
     * 查询子订单出货单
     */
    OrderItemShippingRecordVo queryById(Long id);

    /**
     * 查询子订单出货单列表
     */
    TableDataInfo<OrderItemShippingRecordVo> queryPageList(OrderItemShippingRecordBo bo, PageQuery pageQuery);

    /**
     * 查询子订单出货单列表
     */
    List<OrderItemShippingRecordVo> queryList(OrderItemShippingRecordBo bo);

    /**
     * 新增子订单出货单
     */
    Boolean insertByBo(OrderItemShippingRecordBo bo);

    /**
     * 修改子订单出货单
     */
    Boolean updateByBo(OrderItemShippingRecordBo bo);

    /**
     * 校验并批量删除子订单出货单信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
