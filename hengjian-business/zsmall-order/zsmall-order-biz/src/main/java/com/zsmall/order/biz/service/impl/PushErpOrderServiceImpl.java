package com.zsmall.order.biz.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.domain.dto.ErpHjAttachInfoItemDto;
import com.zsmall.common.domain.dto.ErpHjOrderDto;
import com.zsmall.common.domain.dto.ErpHjProductItemDto;
import com.zsmall.common.enums.order.LogisticsProgress;
import com.zsmall.common.enums.order.OrderAttachmentTypeEnum;
import com.zsmall.common.exception.AppRuntimeException;
import com.zsmall.order.biz.service.PushErpOrderService;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.iservice.IOrderAddressInfoService;
import com.zsmall.order.entity.iservice.IOrderAttachmentService;
import com.zsmall.order.entity.iservice.IOrderItemService;
import com.zsmall.order.entity.iservice.IOrderItemTrackingRecordService;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.system.entity.domain.vo.salesChannel.TenantSalesChannelVo;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * lty notes 推送分销订单至erp项目
 *
 * <AUTHOR> Theo
 * @create 2024/1/3 17:16
 */
@RequiredArgsConstructor
@Service
public class PushErpOrderServiceImpl implements PushErpOrderService {

    private final IOrderItemService orderItemService;
    private final IOrderAddressInfoService orderAddressInfoService;
    private final IOrderAttachmentService orderAttachmentService;
    private final IProductSkuService productSkuService;
    private final ITenantSalesChannelService iTenantSalesChannelService;

    private final IOrderItemTrackingRecordService trackingRecordService;
    @Override
    public ErpHjOrderDto convertToErpOrderMsg(Orders orders) {
        OrderAddressInfo addressInfo = orderAddressInfoService.getByOrderNo(orders.getOrderNo());
        LambdaQueryWrapper<OrderItem> lqw = new LambdaQueryWrapper<OrderItem>().eq(OrderItem::getOrderId, orders.getId());
        List<OrderItemTrackingRecord> itemTrackingRecords = trackingRecordService.getListByOrderNo(orders.getOrderNo());

        List<ErpHjProductItemDto> hjItemsDto = new ArrayList<>();
        List<OrderItem> orderItems = orderItemService.list(lqw);
        // 只有主订单有附件信息
        List<ErpHjAttachInfoItemDto> attachments = new ArrayList<>();

        OrderAttachment orderAttachment = orderAttachmentService.getByOrderNo(orders.getOrderNo());
        ErpHjOrderDto erpHjOrderDto = new ErpHjOrderDto();
        for (OrderItem orderItem : orderItems) {
            ErpHjProductItemDto hjItemDto = new ErpHjProductItemDto();
            String productSkuCode = orderItem.getProductSkuCode();

            LogisticsProgress fulfillmentProgress = orderItem.getFulfillmentProgress();

            LambdaQueryWrapper<ProductSku> productSkuLqw = new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, productSkuCode).last("limit 1");
            ProductSku productSku = TenantHelper.ignore(()->productSkuService.getOne(productSkuLqw));
            hjItemDto.setSku(productSku.getErpSku());
            hjItemDto.setQuantity(orderItem.getTotalQuantity());
            hjItemDto.setIsInsure(0);
            hjItemDto.setIsSignature(fulfillmentProgress.equals(LogisticsProgress.Fulfilled)?0:1);
            hjItemDto.setSkuAmount(orderItem.getOriginalActualTotalAmount().doubleValue());
            hjItemsDto.add(hjItemDto);
        }
        TenantSalesChannelVo tenantSalesChannelVo = iTenantSalesChannelService.queryById(orders.getChannelId());
        erpHjOrderDto.setChannelFlag(tenantSalesChannelVo.getThirdChannelFlag());
        erpHjOrderDto.setOrderNo(orders.getChannelOrderNo());
        erpHjOrderDto.setCurrency(orders.getCurrency());
        // 仓库跟着商品走 要么在item里面写要么就拼接到此处
        // 物流跟商品挂钩
//        erpHjOrderDto.setWarehouseCode();
//        erpHjOrderDto.setCarrier();
//        erpHjOrderDto.setPaidAt(MathTimeUtils.dateToDateTime(orders.getPayTime()));
        erpHjOrderDto.setShipToCountry(addressInfo.getCountryCode());
        erpHjOrderDto.setShipToState(addressInfo.getStateCode());
        if(ObjectUtil.isEmpty(addressInfo.getCity())){
            throw new AppRuntimeException("请完善目的地城市信息");
        }
        erpHjOrderDto.setShipToCity(addressInfo.getCity());
        erpHjOrderDto.setShipToPostal(addressInfo.getZipCode());
        if(ObjectUtil.isEmpty(addressInfo.getAddress1())){
            throw new AppRuntimeException("请完善目的地信息");
        }
        erpHjOrderDto.setShipToAddress1(addressInfo.getAddress1());
        erpHjOrderDto.setShipToAddress2(addressInfo.getAddress2());
        // 发运电话 shipToTelephone
        erpHjOrderDto.setShipToContact(addressInfo.getRecipient());
        erpHjOrderDto.setShipToTelephone(addressInfo.getPhoneNumber());
        erpHjOrderDto.setCustomerEmail(addressInfo.getEmail());
        // items
        erpHjOrderDto.addProductItems(hjItemsDto);
        // 附件
        for (OrderItemTrackingRecord itemTrackingRecord : itemTrackingRecords) {
            OrderAttachmentTypeEnum attachmentType = orderAttachment.getAttachmentType();
            String value = attachmentType.getValue();
            int typeCode ;
            if(OrderAttachmentTypeEnum.Zip.getValue().equals(value)||
                OrderAttachmentTypeEnum.ShippingLabel.getValue().equals(value)){
                typeCode = 0 ;
            }else{
                typeCode = 3 ;
            }

            ErpHjAttachInfoItemDto attachInfoItemDto = new ErpHjAttachInfoItemDto();
            attachInfoItemDto.setUrl(orderAttachment.getAttachmentShowUrl());
            attachInfoItemDto.setName(itemTrackingRecord.getLogisticsTrackingNo());
            attachInfoItemDto.setType(typeCode);
            attachInfoItemDto.setRemark(orderAttachment.getAttachmentOriginalName());
            attachments.add(attachInfoItemDto);
        }

        erpHjOrderDto.addAttachInfoItems(attachments);

        return erpHjOrderDto;
    }
}
