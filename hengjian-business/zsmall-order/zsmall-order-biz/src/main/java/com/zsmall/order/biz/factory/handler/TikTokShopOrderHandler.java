package com.zsmall.order.biz.factory.handler;

import com.alibaba.fastjson.JSONObject;
import com.zsmall.common.domain.req.base.TikTokRequestBaseEntity;
import com.zsmall.common.domain.resp.base.TikTokResponseBaseEntity;
import com.zsmall.common.handler.AbstractOrderBaseHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/31 00:37
 */
@Slf4j
@Lazy
@Component("tiktokShopOrderSave")
public class TikTokShopOrderHandler extends AbstractOrderBaseHandler<TikTokResponseBaseEntity, TikTokRequestBaseEntity>  {
    @Override
    public void saveOrderBefore(JSONObject json, Class<TikTokResponseBaseEntity> t) {

    }

    @Override
    public void saveOrder(JSONObject json, Class<TikTokResponseBaseEntity> t) {

    }
}
