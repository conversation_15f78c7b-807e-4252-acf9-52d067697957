package com.zsmall.order.biz.support;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.enums.order.LogisticsProgress;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.OrderRefund;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.iservice.IOrderItemPriceService;
import com.zsmall.order.entity.iservice.IOrderItemService;
import com.zsmall.order.entity.iservice.IOrderRefundItemService;
import com.zsmall.order.entity.iservice.IOrderRefundService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.Objects;

/**
 * 售后相关功能支持
 *
 * <AUTHOR>
 * @date 2023/2/10
 */
@Slf4j
@Component
public class RefundSupport {

    @Autowired
    private BusinessParameterService businessParameterService;

    @Autowired
    private IOrderRefundService iOrderRefundService;
    @Autowired
    private IOrderRefundItemService iOrderRefundItemService;
    @Autowired
    private IOrderItemPriceService iOrderItemPriceService;
    @Autowired
    private IOrderItemService iOrderItemService;

    /**
     * 判断子订单是否符合退货规则
     *
     * @param orderItem
     * @return
     */
    public void compareRefundRules(OrderItem orderItem) throws RStatusCodeException {
        Boolean canRefund = true;
        if (Objects.equals(orderItem.getFulfillmentProgress(), LogisticsProgress.Fulfilled)) {
            Date fulfillmentTime = orderItem.getFulfillmentTime();
            if (fulfillmentTime != null) {
                Integer refundDays = businessParameterService.getValueFromInteger(BusinessParameterType.REFUND_DAYS);
                Date lastDay = DateUtil.offsetDay(fulfillmentTime, refundDays);
                Date today = new Date();
                if (today.compareTo(lastDay) <= 0 == false) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.ORDER_EXCEEDED_AFTER_SALES_LIMITATIONS);
                }
            } else {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.ORDER_EXCEEDED_AFTER_SALES_LIMITATIONS);
            }
        }

        if (canRefund) {
            Integer count = iOrderRefundItemService.countByInProgress(orderItem.getOrderItemNo());
            if (count > 0) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.HAS_REFUNDING_ORDER);
            }
        }
    }

    /**
     * 判断退货率是否超标
     *
     * @param orderItem
     * @param platformRefundAmount
     * @return
     */
    public Boolean refundRateOverStandard(OrderItem orderItem, BigDecimal platformRefundAmount) {
        BigDecimal refundRate = calculateRefundRate(orderItem, platformRefundAmount);
        BigDecimal warning = NumberUtil.toBigDecimal(businessParameterService.getValueFromDouble(BusinessParameterType.REFUND_RATE_WARNING));
        if (NumberUtil.isGreaterOrEqual(refundRate, warning)) {
            return true;
        } else {
            return false;
        }
    }


    /**
     * 判断退货率是否超标（主订单适用）
     *
     * @param order
     * @param orderRefund
     * @return
     */
    public Boolean refundRateOverStandard(Orders order, OrderRefund orderRefund) {
        BigDecimal refundRate = calculateRefundRate(order, orderRefund);
        BigDecimal warning = NumberUtil.toBigDecimal(businessParameterService.getValueFromDouble(BusinessParameterType.REFUND_RATE_WARNING));
        if (NumberUtil.isGreaterOrEqual(refundRate, warning)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 计算退货率
     *
     * @param orderItem
     * @param platformRefundAmount
     * @return
     */
    public BigDecimal calculateRefundRate(OrderItem orderItem, BigDecimal platformRefundAmount) {
        BigDecimal orderItemTotalAmount = orderItem.getPlatformPayableTotalAmount();
        log.info("计算退货率 子订单总金额 = {}, 退款金额 = {}", orderItemTotalAmount, platformRefundAmount);

        BigDecimal refundRate = NumberUtil.div(platformRefundAmount, orderItemTotalAmount).setScale(2, RoundingMode.HALF_UP);
        log.info("退货率 = {}", refundRate);
        return refundRate;
    }

    /**
     * 计算退货率（主订单适用）
     *
     * @param order
     * @param orderRefund
     * @return
     */
    public BigDecimal calculateRefundRate(Orders order, OrderRefund orderRefund) {
        String orderRefundNo = orderRefund.getOrderRefundNo();
        BigDecimal platformPayableTotalAmount = order.getPlatformPayableTotalAmount();
        BigDecimal platformRefundAmount = orderRefund.getPlatformRefundAmount();
        BigDecimal totalPriceBD = NumberUtil.toBigDecimal(platformPayableTotalAmount);
        BigDecimal refundPriceBD = NumberUtil.toBigDecimal(platformRefundAmount);
        log.info("{} 计算退货率 totalPriceBD = {}, refundPriceBD = {}", orderRefundNo, totalPriceBD, refundPriceBD);
        BigDecimal refundRate = NumberUtil.div(refundPriceBD, totalPriceBD).setScale(2, RoundingMode.HALF_UP);
        log.info("{} 退货率 = {}", orderRefundNo, refundRate);
        return refundRate;
    }

    // /**
    //  * 计算退货率
    //  *
    //  * @param orderRefundItems
    //  * @return
    //  */
    // public BigDecimal calculateRefundRate(List<OrderRefundItem> orderRefundItems) {
    //     BigDecimal totalPriceBD = BigDecimal.ZERO;
    //     BigDecimal refundPriceBD = BigDecimal.ZERO;
    //
    //     for (OrderRefundItem orderRefundItem : orderRefundItems) {
    //         OrderItem orderItem = iOrderItemService.getByOrderItemNo(orderRefundItem.getOrderItemNo());
    //         String orderRefundItemNo = orderRefundItem.getOrderRefundItemNo();
    //         BigDecimal orderItemTotalAmount = orderItem.getPlatformPayableTotalAmount();
    //         BigDecimal orderRefundItemTotalAmount = orderRefundItem.getPlatformPayableTotalAmount();
    //
    //         BigDecimal totalPriceBDItem = NumberUtil.toBigDecimal(orderItemTotalAmount);
    //         BigDecimal refundPriceBDItem = NumberUtil.toBigDecimal(orderRefundItemTotalAmount);
    //         log.info("{} 计算退货率 totalPriceBD = {}, refundPriceBD = {}", orderRefundItemNo, totalPriceBDItem, refundPriceBDItem);
    //
    //         OrderItemPrice orderItemPrice = iOrderItemPriceService.queryByOrderItemNo(orderItem.getOrderItemNo());
    //         BigDecimal platformDepositUnitPrice = orderItemPrice.getPlatformDepositUnitPrice();
    //         Integer num = orderItem.getTotalQuantity();
    //         if (platformDepositUnitPrice != null) {
    //             BigDecimal depositTotalPrice = NumberUtil.mul(platformDepositUnitPrice, num);
    //             log.info("计算退货率 加上订金总价作为分母 depositTotalPrice = {}", depositTotalPrice);
    //             totalPriceBDItem = NumberUtil.add(totalPriceBDItem, depositTotalPrice);
    //             log.info("计算退货率 加上订金总价作为分母 totalPriceBD = {}", refundPriceBDItem);
    //         }
    //
    //         totalPriceBD = NumberUtil.add(totalPriceBD, totalPriceBDItem);
    //         refundPriceBD = NumberUtil.add(refundPriceBD, refundPriceBDItem);
    //     }
    //
    //     BigDecimal refundRate = NumberUtil.div(refundPriceBD, totalPriceBD).setScale(2, RoundingMode.HALF_UP);
    //     log.info("退货率 = {}", refundRate);
    //     return refundRate;
    // }


}
