package com.zsmall.order.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zsmall.order.entity.domain.OrderItemTrackingImportRecord;
import com.zsmall.system.entity.domain.DownloadRecord;

/**
* <AUTHOR>
* @description 针对表【order_item_tracking_import_record】的数据库操作Service
* @createDate 2025-03-28 14:00:33
*/
public interface OrderItemTrackingImportRecordService extends IService<OrderItemTrackingImportRecord> {

    OrderItemTrackingImportRecord initialize(DownloadRecord newRecord, int dataRowCount);
}
