package com.zsmall.order.biz.listener;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.rabbitmq.client.Channel;
import com.zsmall.common.exception.AppRuntimeException;
import com.zsmall.order.biz.factory.ThirdOrderOperationFactory;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.dto.AmazonVCOrderLogisticsAttachmentCallBackDTO;
import com.zsmall.order.entity.iservice.IOrdersService;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.domain.vo.salesChannel.TrackingNotificationVo;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.w3c.dom.Document;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.annotation.Resource;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.IOException;
import java.io.StringReader;
import java.time.Duration;
import java.util.Arrays;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2023/12/12 11:26
 */
@Slf4j
@Component
public class OrderSyncConsumer {
    @Resource
    private ThirdOrderOperationFactory factory;
    @Resource
    private IOrdersService iOrdersService;
    @Resource
    private ITenantSalesChannelService iTenantSalesChannelService;
    @Resource
    private OrderSupport orderSupport;


    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.STORE_SYNCHRONIZATION_QUEUE)
    public void receive(Message message, @Payload String msg, @Header(AmqpHeaders.CHANNEL) Channel channel) throws IOException {
        // 处理消息

        try {
            factory.getInvokeStrategy("multiOperationHandler").formalTripartiteEntry(msg, new Orders());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("队列错误日志:{}",e.getMessage());
        } finally {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(),true);
        }
    }

    /**
     * temu渠道订单对接
     *
     * @param message
     * @param channel
     * @throws IOException
     */
    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.TEMU_ORDER_INFO_QUEUE)
    public void temuOrderDispose(Message message, @Header(AmqpHeaders.CHANNEL) Channel channel) throws IOException {
        String messageContext = new String(message.getBody());
        log.info("接收到订单信息: {}",messageContext);
        if (StringUtils.isEmpty(messageContext)) {
            log.warn("渠道订单队列消息队列: {}, 信息为空",RabbitMqConstant.TRACKING_NOTIFY_ERROR_QUEUE);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);
            return;
        }
        // 生成唯一指纹
        String fingerprint;
        if (message.getMessageProperties().getMessageId() != null) {
            fingerprint = "zsmall:order:" + DigestUtils.md5Hex(messageContext) + message.getMessageProperties().getMessageId();
        } else {
            fingerprint = "zsmall:order:" + DigestUtils.md5Hex(messageContext);
        }
        log.info("渠道订单队列消息队列指纹: {}", fingerprint);
        String processKey = "order:process:" + fingerprint;
        try {
            // 状态检查
            String status = RedisUtils.getCacheObject(processKey);
            if ("processed".equals(status)) {
                log.info("渠道订单队列消息队列消息重复: {}", fingerprint);
                return;
            }
            // 原子标记处理中（替代分布式锁）
            if (!RedisUtils.setObjectIfAbsent(processKey, "processing", Duration.ofSeconds(120))) {
                log.warn("渠道订单队列消息队列消息处理中: {},指纹: {}", messageContext,fingerprint);
                return;
            }
            // 从XML字符串中获取account_id的值
            DocumentBuilderFactory documentBuilderFactory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = documentBuilderFactory.newDocumentBuilder();
            InputSource is = new InputSource(new StringReader(messageContext));
            Document doc = builder.parse(is);
            // 获取所有名为"name"的节点
            NodeList nameNodes = doc.getElementsByTagName("account_id");
            String accountId = null;
            if (nameNodes.getLength() > 0) {
                String accountIdValue = nameNodes.item(0).getTextContent();
                log.info("订单中的accountId:{},xml字符串:{}",accountIdValue,messageContext);
                accountId = accountIdValue;
            }
            TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getTenantSalesChannelByChannelName(accountId);
            if(null == tenantSalesChannel){
                log.error("根据店铺标识获取租户为空，店铺标识: {}",accountId);
                return;
            }
            log.info("订单xml对应的销售渠道为:{}",tenantSalesChannel);
            switch (tenantSalesChannel.getChannelType()){
                case "Temu":
                    factory.getInvokeStrategy("temuOrderOperationHandler").formalTripartiteEntryForTemu(messageContext, new Orders());
                    break;
                case "Amazon_VC":
                    factory.getInvokeStrategy("amazonVCOrderOperationHandler").formalTripartiteEntryForAmazonVc(messageContext, new Orders());
                     break;
                case "EC":
                    factory.getInvokeStrategy("ecOrderOperationHandler").formalTripartiteEntryForAmazonVc(messageContext, new Orders());
                     break;
                case "Amazon_SC":
                    factory.getInvokeStrategy("amazonScOrderOperationHandler").formalTripartiteEntryForAmazonVc(messageContext, new Orders());
                    break;
                default:
                    throw new AppRuntimeException("暂不支持该渠道");
            }
            // 更新为完成状态（延长有效期）
            RedisUtils.setObjectIfAbsent(processKey, "processed", Duration.ofHours(24));
        } catch (Exception e) {
            // 异常时清除处理标记，允许重试
            RedisUtils.deleteObject(processKey);
            log.error("订单信息队列错误日志:{}",e.getMessage());
        } finally {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(),true);
        }
    }

    /**
     * 订单tracking回传失败
     *
     * @param message
     * @param channel
     * @throws IOException
     */
    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.TRACKING_NOTIFY_ERROR_QUEUE)
    public void orderTrackingFail(Message message, @Header(AmqpHeaders.CHANNEL) Channel channel) throws IOException {
        try {
            String messageContext = new String(message.getBody());
            if (StringUtils.isEmpty(messageContext)) {
                log.warn("消息队列: {}, 信息为空",RabbitMqConstant.TRACKING_NOTIFY_ERROR_QUEUE);
                return;
            }
            log.info("订单tracking回传失败，收到消息队列: {}", messageContext);
            TrackingNotificationVo trackingNotificationVo = new JSONObject(messageContext).toBean(TrackingNotificationVo.class);
            if(ObjectUtil.isNotNull(trackingNotificationVo) && StringUtils.isNotEmpty(trackingNotificationVo.getOrderNo()) && StringUtils.isNotEmpty(trackingNotificationVo.getChannelFlag())){
                TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getTenantSalesChannelByChannelName(trackingNotificationVo.getChannelFlag());
                if(ObjectUtil.isNull(tenantSalesChannel)){
                    log.error("根据店铺标识获取租户为空，店铺标识: {}",trackingNotificationVo.getChannelFlag());
                    return;
                }
                // 修改订单trackingFlag
                iOrdersService.updateOrderTrackingFlagByChannelOrderNoAndChannelId(trackingNotificationVo.getOrderNo(),tenantSalesChannel.getId(),1);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("队列错误日志:{}",e.getMessage());
        } finally {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(),true);
        }
    }

    /**
     * amazonVC订单获取面单消息队列
     * @param message
     * @param channel
     * @throws IOException
     */
    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.ORDER_AMAZON_VC_LOGISTICS_ATTACHMENT_CALL_BACK_QUEUE)
    public void amazonVCOrderAttachmentCallBack(Message message, @Header(AmqpHeaders.CHANNEL) Channel channel) throws IOException {
        try {
            String messageContext = new String(message.getBody());
            if (StringUtils.isEmpty(messageContext)) {
                log.warn("订单获取面单消息队列: {}, 信息为空",RabbitMqConstant.ORDER_AMAZON_VC_LOGISTICS_ATTACHMENT_CALL_BACK_QUEUE);
                return;
            }
            log.info("订单获取面单消息队列: {}", messageContext);
            AmazonVCOrderLogisticsAttachmentCallBackDTO amazonVCOrderLogisticsAttachmentCallBackDTO = new JSONObject(messageContext).toBean(AmazonVCOrderLogisticsAttachmentCallBackDTO.class);
            if(ObjectUtil.isNotNull(amazonVCOrderLogisticsAttachmentCallBackDTO) && StringUtils.isNotEmpty(amazonVCOrderLogisticsAttachmentCallBackDTO.getOrderNo()) && StringUtils.isNotEmpty(amazonVCOrderLogisticsAttachmentCallBackDTO.getChannel())){
                TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getTenantSalesChannelByChannelName(amazonVCOrderLogisticsAttachmentCallBackDTO.getChannel());
                if(ObjectUtil.isNull(tenantSalesChannel)){
                    log.error("订单获取面单消息队列根据店铺标识获取租户为空，店铺标识: {}",amazonVCOrderLogisticsAttachmentCallBackDTO.getChannel());
                    return;
                }
                // 处理面单
                orderSupport.amazonVCOrderAttachmentCallBack(amazonVCOrderLogisticsAttachmentCallBackDTO);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("订单获取面单消息队列错误日志:{}",e.getMessage());
        } finally {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(),true);
        }
    }

    /**
     * 推送订单队列
     * @param message
     * @param channel
     * @throws IOException
     */
    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.DISTRIBUTION_PUSH_ORDER_QUEUE)
    public void pushOrder(Message message, @Header(AmqpHeaders.CHANNEL) Channel channel) throws IOException {
        try {
            String messageContext = new String(message.getBody());
            if (StringUtils.isEmpty(messageContext)) {
                log.warn("推送订单队列: {}, 信息为空",RabbitMqConstant.DISTRIBUTION_PUSH_ORDER_QUEUE);
                return;
            }
            log.info("接收到推送订单队列信息: {}", messageContext);
            Orders orders = new JSONObject(messageContext).toBean(Orders.class);
            if(ObjectUtil.isNotNull(orders)){
                // 推送订单
                orderSupport.orderThirdWarehouseFollowUp(Arrays.asList(orders), true);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("推送订单队列错误日志:{},订单:{}",e.getMessage(),new String(message.getBody()));
        } finally {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(),true);
        }
    }

    /**
     * 订单支付队列
     * @param message
     * @param channel
     * @throws IOException
     */
    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.DISTRIBUTION_PUSH_ORDER_PAY_QUEUE)
    public void orderPay(Message message, @Header(AmqpHeaders.CHANNEL) Channel channel) throws IOException {
        try {
            String messageContext = new String(message.getBody());
            if (StringUtils.isEmpty(messageContext)) {
                log.warn("订单支付队列: {}, 信息为空",RabbitMqConstant.DISTRIBUTION_PUSH_ORDER_PAY_QUEUE);
                return;
            }
            log.info("接收到订单支付队列信息: {}", messageContext);
//            JSONArray jsonArray = JSONUtil.parseArray(messageContext);
//            if (jsonArray == null || jsonArray.isEmpty()) {
//                log.error("接收到订单支付队列信息不是一个有效的JSON数组: {}", messageContext);
//                return;
//            }
//            List<Orders> ordersList = jsonArray.stream()
//                                               .map(item -> ((JSONObject) item).toBean(Orders.class))
//                                               .collect(Collectors.toList());
//            if(ObjectUtil.isNotNull(ordersList)){
//                // 校验订单及钱包支付
//                orderSupport.orderPayChainQueue(ordersList.get(0).getTenantId(), ordersList, true, true);
//            }
            Orders orders = new JSONObject(messageContext).toBean(Orders.class);
            if(ObjectUtil.isNotNull(orders)){
                // 校验订单及钱包支付
                orderSupport.orderPayChainQueue(orders.getTenantId(), Arrays.asList(orders), true, true);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("订单支付队列出现异常:{},订单:{}",e.getMessage(),new String(message.getBody()));
        } finally {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(),true);
        }
    }
}
